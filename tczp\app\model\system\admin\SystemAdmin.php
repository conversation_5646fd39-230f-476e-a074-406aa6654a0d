<?php

namespace app\model\system\admin;

use app\model\supplier\SystemSupplier;
use crmeb\basic\BaseModel;
use crmeb\traits\JwtAuthModelTrait;
use crmeb\traits\ModelTrait;
use think\Model;

/**
 * 管理员模型
 * Class SystemAdmin
 * @package app\model\system\admin
 */
class SystemAdmin extends BaseModel
{
    use ModelTrait;
    use JwtAuthModelTrait;

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'system_admin';

    protected $insert = ['add_time'];

    /**
     * 权限数据
     * @param $value
     * @return false|string[]
     */
    public static function getRolesAttr($value)
    {
        return explode(',', $value);
    }

    /**
     * 供应商
     * @return \think\model\relation\HasOne
     */
    public function supplier()
    {
        return $this->hasOne(SystemSupplier::class, 'id', 'relation_id')->field(['id', 'supplier_name', 'admin_id', 'avatar', 'is_show'])->bind([
            'supplier_id' => 'id',
            'supplier_name',
            'avatar',
            'is_show'
        ]);
    }

    /**
     * 管理员级别搜索器
     * @param Model $query
     * @param $value
     * @param $data
     */
    public function searchLevelAttr($query, $value)
    {
        if (is_array($value)) {
            $query->where('level', $value[0], $value[1]);
        } else {
            $query->where('level', $value);
        }
    }

    /**
     * 管理员账号和姓名搜索器
     * @param Model $query
     * @param $value
     */
    public function searchAccountLikeAttr($query, $value)
    {
        if ($value) {
            $query->whereLike('account|real_name', '%' . $value . '%');
        }
    }

    public function searchIdAttr($query, $value)
    {
        if ($value !== '') {
            if (is_array($value)) {
                $query->whereIn('id', $value);
            } else {
                $query->where('id', $value);
            }
        }
    }

    /**
     * 管理员账号搜索器
     * @param Model $query
     * @param $value
     */
    public function searchAccountAttr($query, $value)
    {
        if ($value) {
            $query->where('account', $value);
        }
    }

    /**
     * 管理员电话搜索器
     * @param Model $query
     * @param $value
     */
    public function searchPhoneAttr($query, $value)
    {
        if ($value != '') {
            $query->where('phone', $value);
        }
    }

    /**
     * 管理员权限搜索器
     * @param Model $query
     * @param $roles
     */
    public function searchRolesAttr($query, $roles)
    {
        if ($roles) {
            $query->where("CONCAT(',',roles,',')  LIKE '%,$roles,%'");
        }
    }

    /**
     * 是否删除搜索器
     * @param Model $query
     * @param $value
     */
    public function searchIsDelAttr($query)
    {
        $query->where('is_del', 0);
    }

    /**
     * 状态搜索器
     * @param Model $query
     * @param $value
     */
    public function searchStatusAttr($query, $value)
    {
        if ($value != '' && $value != null) {
            $query->where('status', $value);
        }
    }

    /**
     * 关联ID搜索器
     * @param Model $query
     * @param $value
     */
    public function searchRelationIdAttr($query, $value)
    {
        if ($value != '' && $value != null) {
            $query->where('relation_id', $value);
        }
    }

    /**
     * 状态搜索器
     * @param Model $query
     * @param $value
     */
    public function searchAdminTypeAttr($query, $value)
    {
        if ($value != '' && $value != null) {
            $query->where('admin_type', $value);
        }
    }

}
