<?php

namespace app\controller\api\user;


use app\Request;
use app\services\message\service\StoreServiceLogServices;
use app\services\message\service\StoreServiceServices;
use think\annotation\Inject;

/**
 * Class StoreService
 * @package app\api\controller\v2\user
 */
class StoreService
{

    /**
     * @var StoreServiceLogServices
     */
    #[Inject]
    protected StoreServiceLogServices $services;

    /**
     * 客服聊天记录
     * @param Request $request
     * @param StoreServiceServices $services
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function record(Request $request, StoreServiceServices $services)
    {
        [$uidTo, $limit, $toUid] = $request->getMore([
            [['uidTo', 'd'], 0],
            [['limit', 'd'], 10],
            [['toUid', 'd'], 0],
        ], true);
        $uid = (int)$request->uid();
        return app('json')->successful($services->getRecord($uid, $uidTo, $limit, $toUid));
    }
}
