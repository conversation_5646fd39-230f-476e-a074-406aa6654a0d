<?php

namespace app\listener\user;

use app\jobs\system\CapitalFlowJob;
use crmeb\interfaces\ListenerInterface;

/**
 * 用户充值事件
 * Class Recharge
 * @package app\listener\user
 */
class Recharge implements ListenerInterface
{
    /**
     * 用户充值事件
     * @param $event
     */
    public function handle($event): void
    {
        [$order, $now_money] = $event;

		//记录资金流水
        CapitalFlowJob::dispatch([$order, 'recharge']);
        //提醒推送
        event('notice.notice', [['order' => $order, 'now_money' => $now_money], 'recharge_success']);
		// 小程序订单服务
        event('order.routine.shipping', ['recharge', $order, 3, '', '']);
    }
}
