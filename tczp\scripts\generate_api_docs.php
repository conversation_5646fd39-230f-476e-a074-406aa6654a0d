<?php
/**
 * TCZP API文档生成器
 * 自动生成三端完整的API文档
 */

declare(strict_types=1);

// 定义根目录
define('ROOT_PATH', dirname(__DIR__) . '/');

echo "==================== TCZP API文档生成器 ====================\n";
echo "开始生成API文档...\n\n";

/**
 * API文档数据结构
 */
$apiDocs = [
    'info' => [
        'title' => 'TCZP同城找师傅API文档',
        'version' => '3.0.0',
        'description' => '同城找师傅平台三端统一API接口文档',
        'contact' => [
            'name' => 'TCZP Team',
            'email' => '<EMAIL>'
        ]
    ],
    'servers' => [
        [
            'url' => 'https://api.tczp.com',
            'description' => '生产环境'
        ],
        [
            'url' => 'https://test-api.tczp.com',
            'description' => '测试环境'
        ],
        [
            'url' => 'http://localhost:8000',
            'description' => '开发环境'
        ]
    ],
    'paths' => []
];

/**
 * 公共接口文档
 */
$publicApis = [
    '/api/v3/public/config' => [
        'get' => [
            'summary' => '获取系统配置',
            'description' => '获取平台基础配置信息',
            'tags' => ['公共接口'],
            'responses' => [
                '200' => [
                    'description' => '成功',
                    'content' => [
                        'application/json' => [
                            'schema' => [
                                'type' => 'object',
                                'properties' => [
                                    'code' => ['type' => 'integer', 'example' => 200],
                                    'msg' => ['type' => 'string', 'example' => 'success'],
                                    'data' => [
                                        'type' => 'object',
                                        'properties' => [
                                            'site_name' => ['type' => 'string', 'example' => '同城找师傅'],
                                            'site_logo' => ['type' => 'string', 'example' => '/logo.png'],
                                            'service_phone' => ['type' => 'string', 'example' => '************']
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ]
    ],
    '/api/v3/public/cities' => [
        'get' => [
            'summary' => '获取城市列表',
            'description' => '获取支持的城市列表',
            'tags' => ['公共接口'],
            'parameters' => [
                [
                    'name' => 'parent_id',
                    'in' => 'query',
                    'description' => '父级城市ID',
                    'schema' => ['type' => 'integer', 'default' => 0]
                ],
                [
                    'name' => 'level',
                    'in' => 'query',
                    'description' => '城市级别',
                    'schema' => ['type' => 'integer', 'default' => 1]
                ]
            ],
            'responses' => [
                '200' => [
                    'description' => '成功',
                    'content' => [
                        'application/json' => [
                            'schema' => [
                                'type' => 'object',
                                'properties' => [
                                    'code' => ['type' => 'integer', 'example' => 200],
                                    'msg' => ['type' => 'string', 'example' => 'success'],
                                    'data' => [
                                        'type' => 'array',
                                        'items' => [
                                            'type' => 'object',
                                            'properties' => [
                                                'id' => ['type' => 'integer', 'example' => 1],
                                                'name' => ['type' => 'string', 'example' => '北京市'],
                                                'code' => ['type' => 'string', 'example' => '110000'],
                                                'level' => ['type' => 'integer', 'example' => 1]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ]
    ],
    '/api/v3/public/sms/send' => [
        'post' => [
            'summary' => '发送短信验证码',
            'description' => '发送短信验证码到指定手机号',
            'tags' => ['公共接口'],
            'requestBody' => [
                'required' => true,
                'content' => [
                    'application/json' => [
                        'schema' => [
                            'type' => 'object',
                            'required' => ['phone', 'type'],
                            'properties' => [
                                'phone' => ['type' => 'string', 'example' => '13800138000', 'description' => '手机号'],
                                'type' => ['type' => 'string', 'enum' => ['register', 'login', 'reset_password', 'bind_phone'], 'example' => 'register', 'description' => '验证码类型']
                            ]
                        ]
                    ]
                ]
            ],
            'responses' => [
                '200' => [
                    'description' => '发送成功',
                    'content' => [
                        'application/json' => [
                            'schema' => [
                                'type' => 'object',
                                'properties' => [
                                    'code' => ['type' => 'integer', 'example' => 200],
                                    'msg' => ['type' => 'string', 'example' => '验证码发送成功'],
                                    'data' => ['type' => 'array', 'example' => []]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ]
    ]
];

/**
 * 师傅端接口文档
 */
$workerApis = [
    '/api/v3/worker/auth/register' => [
        'post' => [
            'summary' => '师傅注册',
            'description' => '师傅用户注册接口',
            'tags' => ['师傅端-认证'],
            'requestBody' => [
                'required' => true,
                'content' => [
                    'application/json' => [
                        'schema' => [
                            'type' => 'object',
                            'required' => ['phone', 'password', 'verify_code', 'real_name'],
                            'properties' => [
                                'phone' => ['type' => 'string', 'example' => '13800138000'],
                                'password' => ['type' => 'string', 'example' => '123456'],
                                'verify_code' => ['type' => 'string', 'example' => '123456'],
                                'real_name' => ['type' => 'string', 'example' => '张师傅'],
                                'id_card' => ['type' => 'string', 'example' => '110101199001011234']
                            ]
                        ]
                    ]
                ]
            ],
            'responses' => [
                '200' => [
                    'description' => '注册成功',
                    'content' => [
                        'application/json' => [
                            'schema' => [
                                'type' => 'object',
                                'properties' => [
                                    'code' => ['type' => 'integer', 'example' => 200],
                                    'msg' => ['type' => 'string', 'example' => '注册成功'],
                                    'data' => [
                                        'type' => 'object',
                                        'properties' => [
                                            'token' => ['type' => 'string', 'example' => 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...'],
                                            'user_info' => [
                                                'type' => 'object',
                                                'properties' => [
                                                    'uid' => ['type' => 'integer', 'example' => 1001],
                                                    'phone' => ['type' => 'string', 'example' => '13800138000'],
                                                    'real_name' => ['type' => 'string', 'example' => '张师傅']
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ]
    ],
    '/api/v3/worker/profile' => [
        'get' => [
            'summary' => '获取个人档案',
            'description' => '获取师傅个人档案信息',
            'tags' => ['师傅端-个人中心'],
            'security' => [['bearerAuth' => []]],
            'responses' => [
                '200' => [
                    'description' => '成功',
                    'content' => [
                        'application/json' => [
                            'schema' => [
                                'type' => 'object',
                                'properties' => [
                                    'code' => ['type' => 'integer', 'example' => 200],
                                    'msg' => ['type' => 'string', 'example' => 'success'],
                                    'data' => [
                                        'type' => 'object',
                                        'properties' => [
                                            'id' => ['type' => 'integer', 'example' => 1],
                                            'worker_no' => ['type' => 'string', 'example' => 'W202401001'],
                                            'real_name' => ['type' => 'string', 'example' => '张师傅'],
                                            'phone' => ['type' => 'string', 'example' => '13800138000'],
                                            'work_experience' => ['type' => 'integer', 'example' => 5],
                                            'rating' => ['type' => 'number', 'format' => 'float', 'example' => 4.8],
                                            'order_count' => ['type' => 'integer', 'example' => 156],
                                            'completion_rate' => ['type' => 'number', 'format' => 'float', 'example' => 98.5],
                                            'auth_status' => ['type' => 'string', 'enum' => ['pending', 'approved', 'rejected'], 'example' => 'approved'],
                                            'work_status' => ['type' => 'string', 'enum' => ['available', 'busy', 'offline'], 'example' => 'available']
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ]
    ]
];

/**
 * 用户端接口文档
 */
$userApis = [
    '/api/v3/user/auth/register' => [
        'post' => [
            'summary' => '用户注册',
            'description' => '普通用户注册接口',
            'tags' => ['用户端-认证'],
            'requestBody' => [
                'required' => true,
                'content' => [
                    'application/json' => [
                        'schema' => [
                            'type' => 'object',
                            'required' => ['phone', 'password', 'verify_code'],
                            'properties' => [
                                'phone' => ['type' => 'string', 'example' => '13800138000'],
                                'password' => ['type' => 'string', 'example' => '123456'],
                                'verify_code' => ['type' => 'string', 'example' => '123456'],
                                'nickname' => ['type' => 'string', 'example' => '用户昵称']
                            ]
                        ]
                    ]
                ]
            ],
            'responses' => [
                '200' => [
                    'description' => '注册成功',
                    'content' => [
                        'application/json' => [
                            'schema' => [
                                'type' => 'object',
                                'properties' => [
                                    'code' => ['type' => 'integer', 'example' => 200],
                                    'msg' => ['type' => 'string', 'example' => '注册成功'],
                                    'data' => [
                                        'type' => 'object',
                                        'properties' => [
                                            'token' => ['type' => 'string', 'example' => 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...'],
                                            'user_info' => [
                                                'type' => 'object',
                                                'properties' => [
                                                    'uid' => ['type' => 'integer', 'example' => 2001],
                                                    'phone' => ['type' => 'string', 'example' => '13800138000'],
                                                    'nickname' => ['type' => 'string', 'example' => '用户昵称']
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ]
    ],
    '/api/v3/user/tasks' => [
        'post' => [
            'summary' => '发布任务',
            'description' => '用户发布新任务',
            'tags' => ['用户端-任务管理'],
            'security' => [['bearerAuth' => []]],
            'requestBody' => [
                'required' => true,
                'content' => [
                    'application/json' => [
                        'schema' => [
                            'type' => 'object',
                            'required' => ['task_title', 'task_description', 'task_category_id', 'hourly_rate'],
                            'properties' => [
                                'task_title' => ['type' => 'string', 'example' => '空调维修'],
                                'task_description' => ['type' => 'string', 'example' => '家用空调不制冷，需要专业师傅上门维修'],
                                'task_category_id' => ['type' => 'integer', 'example' => 1],
                                'hourly_rate' => ['type' => 'number', 'format' => 'float', 'example' => 80.00],
                                'task_location' => ['type' => 'string', 'example' => '北京市朝阳区xxx小区'],
                                'required_workers' => ['type' => 'integer', 'example' => 1],
                                'contact_phone' => ['type' => 'string', 'example' => '13800138000']
                            ]
                        ]
                    ]
                ]
            ],
            'responses' => [
                '200' => [
                    'description' => '发布成功',
                    'content' => [
                        'application/json' => [
                            'schema' => [
                                'type' => 'object',
                                'properties' => [
                                    'code' => ['type' => 'integer', 'example' => 200],
                                    'msg' => ['type' => 'string', 'example' => '任务发布成功'],
                                    'data' => [
                                        'type' => 'object',
                                        'properties' => [
                                            'task_id' => ['type' => 'integer', 'example' => 1001],
                                            'task_no' => ['type' => 'string', 'example' => 'T202401001']
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ]
    ]
];

/**
 * 管理后台接口文档
 */
$adminApis = [
    '/api/v3/admin/auth/login' => [
        'post' => [
            'summary' => '管理员登录',
            'description' => '管理员登录接口',
            'tags' => ['管理后台-认证'],
            'requestBody' => [
                'required' => true,
                'content' => [
                    'application/json' => [
                        'schema' => [
                            'type' => 'object',
                            'required' => ['account', 'password'],
                            'properties' => [
                                'account' => ['type' => 'string', 'example' => 'admin'],
                                'password' => ['type' => 'string', 'example' => 'admin123']
                            ]
                        ]
                    ]
                ]
            ],
            'responses' => [
                '200' => [
                    'description' => '登录成功',
                    'content' => [
                        'application/json' => [
                            'schema' => [
                                'type' => 'object',
                                'properties' => [
                                    'code' => ['type' => 'integer', 'example' => 200],
                                    'msg' => ['type' => 'string', 'example' => '登录成功'],
                                    'data' => [
                                        'type' => 'object',
                                        'properties' => [
                                            'token' => ['type' => 'string', 'example' => 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...'],
                                            'admin_info' => [
                                                'type' => 'object',
                                                'properties' => [
                                                    'id' => ['type' => 'integer', 'example' => 1],
                                                    'account' => ['type' => 'string', 'example' => 'admin'],
                                                    'real_name' => ['type' => 'string', 'example' => '系统管理员'],
                                                    'level' => ['type' => 'integer', 'example' => 0]
                                                ]
                                            ],
                                            'permissions' => [
                                                'type' => 'array',
                                                'items' => ['type' => 'string'],
                                                'example' => ['dashboard:view', 'user:manage', 'task:audit']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ]
    ]
];

/**
 * 合并所有API文档
 */
$apiDocs['paths'] = array_merge($publicApis, $workerApis, $userApis, $adminApis);

/**
 * 添加安全定义
 */
$apiDocs['components'] = [
    'securitySchemes' => [
        'bearerAuth' => [
            'type' => 'http',
            'scheme' => 'bearer',
            'bearerFormat' => 'JWT'
        ]
    ]
];

/**
 * 生成OpenAPI 3.0格式的JSON文档
 */
$openApiDoc = [
    'openapi' => '3.0.0',
    'info' => $apiDocs['info'],
    'servers' => $apiDocs['servers'],
    'paths' => $apiDocs['paths'],
    'components' => $apiDocs['components']
];

/**
 * 保存文档文件
 */
$docsDir = ROOT_PATH . 'docs/api/';
if (!is_dir($docsDir)) {
    mkdir($docsDir, 0755, true);
}

// 保存OpenAPI JSON文档
file_put_contents($docsDir . 'openapi.json', json_encode($openApiDoc, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

// 生成Markdown文档
$markdownDoc = generateMarkdownDoc($apiDocs);
file_put_contents($docsDir . 'api.md', $markdownDoc);

// 生成Postman集合
$postmanCollection = generatePostmanCollection($apiDocs);
file_put_contents($docsDir . 'postman_collection.json', json_encode($postmanCollection, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

echo "✅ API文档生成完成！\n";
echo "   - OpenAPI文档: docs/api/openapi.json\n";
echo "   - Markdown文档: docs/api/api.md\n";
echo "   - Postman集合: docs/api/postman_collection.json\n";

/**
 * 生成Markdown文档
 */
function generateMarkdownDoc($apiDocs): string
{
    $markdown = "# {$apiDocs['info']['title']}\n\n";
    $markdown .= "版本: {$apiDocs['info']['version']}\n\n";
    $markdown .= "{$apiDocs['info']['description']}\n\n";
    
    $markdown .= "## 服务器地址\n\n";
    foreach ($apiDocs['servers'] as $server) {
        $markdown .= "- {$server['description']}: `{$server['url']}`\n";
    }
    $markdown .= "\n";
    
    $markdown .= "## 认证方式\n\n";
    $markdown .= "使用Bearer Token认证，在请求头中添加：\n";
    $markdown .= "```\nAuthorization: Bearer <token>\n```\n\n";
    
    $markdown .= "## 接口列表\n\n";
    
    $groupedPaths = [];
    foreach ($apiDocs['paths'] as $path => $methods) {
        foreach ($methods as $method => $details) {
            $tag = $details['tags'][0] ?? '其他';
            $groupedPaths[$tag][] = [
                'path' => $path,
                'method' => strtoupper($method),
                'summary' => $details['summary'],
                'description' => $details['description']
            ];
        }
    }
    
    foreach ($groupedPaths as $tag => $apis) {
        $markdown .= "### {$tag}\n\n";
        foreach ($apis as $api) {
            $markdown .= "#### {$api['method']} {$api['path']}\n\n";
            $markdown .= "**{$api['summary']}**\n\n";
            $markdown .= "{$api['description']}\n\n";
        }
    }
    
    return $markdown;
}

/**
 * 生成Postman集合
 */
function generatePostmanCollection($apiDocs): array
{
    $collection = [
        'info' => [
            'name' => $apiDocs['info']['title'],
            'description' => $apiDocs['info']['description'],
            'schema' => 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json'
        ],
        'item' => []
    ];
    
    $groupedPaths = [];
    foreach ($apiDocs['paths'] as $path => $methods) {
        foreach ($methods as $method => $details) {
            $tag = $details['tags'][0] ?? '其他';
            $groupedPaths[$tag][] = [
                'name' => $details['summary'],
                'request' => [
                    'method' => strtoupper($method),
                    'header' => [],
                    'url' => [
                        'raw' => '{{base_url}}' . $path,
                        'host' => ['{{base_url}}'],
                        'path' => explode('/', trim($path, '/'))
                    ]
                ]
            ];
        }
    }
    
    foreach ($groupedPaths as $tag => $requests) {
        $collection['item'][] = [
            'name' => $tag,
            'item' => $requests
        ];
    }
    
    return $collection;
}

echo "\n==================== API文档生成完成 ====================\n";
