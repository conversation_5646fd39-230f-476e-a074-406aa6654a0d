<?php

namespace app\model\system\timer;

use crmeb\basic\BaseModel;
use crmeb\traits\JwtAuthModelTrait;
use crmeb\traits\ModelTrait;
use think\Model;

/**
 * 定时任务模型
 * Class SystemTimer
 * @package app\model\system\timer
 */
class SystemTimer extends BaseModel
{
    use ModelTrait;
    use JwtAuthModelTrait;

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'system_timer';

    protected $insert = ['add_time'];

}
