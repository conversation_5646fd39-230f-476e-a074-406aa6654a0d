# 师傅端系统优化方案

## 📋 目录
- [1. 系统架构分析](#1-系统架构分析)
- [2. 问题诊断](#2-问题诊断)
- [3. 优化方案](#3-优化方案)
- [4. 实施计划](#4-实施计划)
- [5. 测试验证](#5-测试验证)

## 1. 系统架构分析

### 1.1 用户角色关系
```
同城用工平台用户体系：
├── 普通用户/企业 (Customer/Enterprise)
│   ├── 发布任务需求
│   ├── 选择师傅
│   ├── 支付费用
│   └── 评价服务
├── 师傅 (Worker)
│   ├── 实名认证
│   ├── 技能认证
│   ├── 接单服务
│   └── 收入管理
└── 平台管理员 (Admin)
    ├── 用户管理
    ├── 师傅审核
    ├── 任务监管
    └── 平台运营
```

### 1.2 业务流程
```
师傅端核心业务流程：
注册申请 → 身份认证 → 技能认证 → 浏览任务 → 申请接单 → 执行任务 → 完成结算 → 评价反馈
```

## 2. 问题诊断

### 2.1 数据库设计问题

#### ❌ 当前问题
1. **角色区分不明确**
   - `user`表缺少明确的师傅角色标识
   - `user_type`和`identity`字段含义模糊
   - 角色权限管理混乱

2. **师傅信息设计不合理**
   - 师傅信息散落在多个表中
   - 缺少独立的师傅档案表
   - 认证状态管理不完善

3. **技能管理设计缺陷**
   - 使用JSON存储技能标签，查询困难
   - 缺少技能等级和认证状态
   - 技能分类体系不完整

#### ✅ 优化方案
1. **重新设计用户角色体系**
   - 创建`user_roles`角色表
   - 创建`user_role_relations`角色关联表
   - 明确定义师傅角色权限

2. **独立师傅信息管理**
   - 创建`worker_profiles`师傅档案表
   - 统一管理师傅基本信息和状态
   - 完善认证流程设计

3. **重构技能管理体系**
   - 创建`skill_categories`技能分类表
   - 重新设计`skills`技能表
   - 创建`worker_skills`师傅技能关联表

### 2.2 中间件设计问题

#### ❌ 当前问题
1. **认证中间件混乱**
   - 多种认证中间件并存
   - 认证逻辑分散重复
   - 缺少统一的权限控制

2. **权限验证不完整**
   - 部分接口缺少权限验证
   - 师傅状态验证不充分
   - 业务权限控制缺失

#### ✅ 优化方案
1. **统一认证中间件**
   - 创建`StaffAuthMiddleware`统一认证
   - 标准化Token解析和验证
   - 统一用户状态检查

2. **完善权限控制**
   - 创建`StaffPermissionMiddleware`权限中间件
   - 基于角色的权限控制(RBAC)
   - 细粒度的业务权限验证

### 2.3 API接口设计问题

#### ❌ 当前问题
1. **路由设计不规范**
   - 接口命名不一致
   - RESTful规范执行不到位
   - 缺少版本控制

2. **接口权限控制缺失**
   - 部分接口缺少权限验证
   - 师傅状态验证不充分
   - 错误处理不完善

#### ✅ 优化方案
1. **RESTful API设计**
   - 统一接口命名规范
   - 标准化HTTP方法使用
   - 实现API版本控制

2. **完善权限控制**
   - 为每个接口配置适当的权限
   - 统一错误处理和响应格式
   - 完善接口文档

## 3. 优化方案

### 3.1 数据库优化

#### 新增表结构
```sql
-- 用户角色表
user_roles (角色定义)
user_role_relations (用户角色关联)

-- 师傅信息表
worker_profiles (师傅档案)
worker_auth_records (认证记录)

-- 技能管理表
skill_categories (技能分类)
skills_new (技能表)
worker_skills_new (师傅技能关联)
```

#### 优化现有表
```sql
-- 任务表优化
ALTER TABLE tasks ADD COLUMN required_skills json;
ALTER TABLE tasks ADD COLUMN difficulty_level tinyint(1);
ALTER TABLE tasks ADD COLUMN worker_requirements json;

-- 任务申请表优化
ALTER TABLE task_applications ADD COLUMN skill_match_score decimal(5,2);
ALTER TABLE task_applications ADD COLUMN distance decimal(8,2);
```

### 3.2 中间件优化

#### 统一认证中间件
```php
StaffAuthMiddleware::class
├── Token解析验证
├── 用户状态检查
├── 师傅角色验证
└── 请求上下文设置
```

#### 权限控制中间件
```php
StaffPermissionMiddleware::class
├── 权限检查
├── 角色验证
├── 业务权限控制
└── 错误处理
```

### 3.3 API接口优化

#### RESTful路由设计
```
/staff/v2/
├── public/          # 公共接口
├── auth/            # 认证相关
├── tasks/           # 任务管理
├── profile/         # 个人中心
├── skills/          # 技能管理
├── income/          # 收入管理
├── reviews/         # 评价管理
├── messages/        # 消息通知
├── settings/        # 设置管理
├── support/         # 帮助支持
└── utils/           # 工具接口
```

#### 权限配置
```php
// 权限级别
'task.manage'     // 任务管理权限
'skill.manage'    // 技能管理权限
'income.manage'   // 收入管理权限
'profile.manage'  // 个人信息管理权限
```

## 4. 实施计划

### 4.1 第一阶段：数据库重构 (1-2周)
1. **创建新表结构**
   - 执行数据库优化脚本
   - 创建角色和权限表
   - 创建师傅信息表

2. **数据迁移**
   - 迁移现有用户数据
   - 设置默认角色权限
   - 验证数据完整性

### 4.2 第二阶段：中间件重构 (1周)
1. **创建新中间件**
   - 实现统一认证中间件
   - 实现权限控制中间件
   - 编写单元测试

2. **替换旧中间件**
   - 逐步替换现有中间件
   - 测试认证流程
   - 验证权限控制

### 4.3 第三阶段：API接口优化 (2-3周)
1. **重构路由配置**
   - 实现RESTful路由设计
   - 配置权限控制
   - 更新接口文档

2. **优化控制器**
   - 重构师傅端控制器
   - 统一响应格式
   - 完善错误处理

### 4.4 第四阶段：服务类重构 (1-2周)
1. **创建服务类**
   - 实现师傅认证服务
   - 实现技能管理服务
   - 实现任务管理服务

2. **业务逻辑优化**
   - 重构业务逻辑
   - 优化数据查询
   - 提升性能

## 5. 测试验证

### 5.1 单元测试
- 中间件功能测试
- 服务类方法测试
- 数据库操作测试

### 5.2 集成测试
- API接口测试
- 认证流程测试
- 权限控制测试

### 5.3 性能测试
- 数据库查询性能
- 接口响应时间
- 并发处理能力

### 5.4 用户验收测试
- 师傅注册认证流程
- 任务申请接单流程
- 收入管理功能

## 6. 预期效果

### 6.1 技术改进
- ✅ 统一的认证体系
- ✅ 完善的权限控制
- ✅ 规范的API设计
- ✅ 清晰的数据结构

### 6.2 业务提升
- ✅ 师傅认证流程优化
- ✅ 任务匹配精度提升
- ✅ 用户体验改善
- ✅ 系统稳定性增强

### 6.3 维护性提升
- ✅ 代码结构清晰
- ✅ 业务逻辑分离
- ✅ 扩展性增强
- ✅ 维护成本降低
