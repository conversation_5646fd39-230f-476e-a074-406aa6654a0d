<?php

use app\http\middleware\AllowOriginMiddleware;
use app\http\middleware\StationOpenMiddleware;
use app\http\middleware\staff\StaffAuthMiddleware;
use app\http\middleware\staff\StaffPermissionMiddleware;
use think\facade\Route;

/**
 * 师傅端接口路由配置 - 优化版本
 * 版本: v2.0
 * 特性: 统一认证、权限控制、RESTful规范
 */
Route::group('staff/v2', function () {

    // ==================== 公共接口（无需登录） ====================
    Route::group('public', function () {
        // 系统配置
        Route::get('config', 'Common/getConfig')->name('staff.config');
        Route::get('cities', 'Common/getCityList')->name('staff.cities');
        Route::get('task-categories', 'Common/getTaskCategories')->name('staff.task_categories');
        Route::get('skill-categories', 'Common/getSkillCategories')->name('staff.skill_categories');

        // 任务大厅（公开浏览）
        Route::get('tasks', 'Task/getTaskList')->name('staff.public.tasks');
        Route::get('tasks/:id', 'Task/getTaskDetail')->name('staff.public.task_detail');

        // 师傅展示（公开）
        Route::get('workers', 'Worker/getWorkerList')->name('staff.public.workers');
        Route::get('workers/:id', 'Worker/getWorkerDetail')->name('staff.public.worker_detail');

        // 注册相关
        Route::post('register/apply', 'Auth/applyRegister')->name('staff.register.apply');
        Route::post('register/verify-phone', 'Auth/verifyPhone')->name('staff.register.verify_phone');

    })->middleware(StationOpenMiddleware::class);

    // ==================== 认证相关接口 ====================
    Route::group('auth', function () {
        // 身份认证
        Route::post('identity/submit', 'Auth/submitIdentityAuth')->name('staff.auth.identity');
        Route::get('identity/status', 'Auth/getIdentityStatus')->name('staff.auth.identity_status');

        // 技能认证
        Route::post('skills/apply', 'Auth/applySkillAuth')->name('staff.auth.skill_apply');
        Route::get('skills/status', 'Auth/getSkillAuthStatus')->name('staff.auth.skill_status');
        Route::put('skills/:id', 'Auth/updateSkillAuth')->name('staff.auth.skill_update');

        // 文件上传
        Route::post('upload/identity', 'Auth/uploadIdentityFiles')->name('staff.auth.upload_identity');
        Route::post('upload/skill', 'Auth/uploadSkillFiles')->name('staff.auth.upload_skill');
        Route::post('upload/certificate', 'Auth/uploadCertificate')->name('staff.auth.upload_certificate');

    })->middleware(StationOpenMiddleware::class)
        ->middleware(StaffAuthMiddleware::class, true);

    // ==================== 任务管理接口 ====================
    Route::group('tasks', function () {
        // 任务浏览
        Route::get('', 'Task/getTaskList')->name('staff.tasks.list');
        Route::get(':id', 'Task/getTaskDetail')->name('staff.tasks.detail');
        Route::get('recommended', 'Task/getRecommendedTasks')->name('staff.tasks.recommended');

        // 任务申请
        Route::post(':id/apply', 'Task/applyTask')->name('staff.tasks.apply');
        Route::get('applications', 'Task/getMyApplications')->name('staff.tasks.applications');
        Route::put('applications/:id', 'Task/updateApplication')->name('staff.tasks.update_application');

        // 任务执行
        Route::get('my', 'Task/getMyTasks')->name('staff.tasks.my');
        Route::post(':id/start', 'Task/startTask')->name('staff.tasks.start');
        Route::post(':id/complete', 'Task/completeTask')->name('staff.tasks.complete');
        Route::post(':id/cancel', 'Task/cancelTask')->name('staff.tasks.cancel');
        Route::post(':id/pause', 'Task/pauseTask')->name('staff.tasks.pause');
        Route::post(':id/resume', 'Task/resumeTask')->name('staff.tasks.resume');

    })->middleware(StationOpenMiddleware::class)
        ->middleware(StaffAuthMiddleware::class, true)
        ->middleware(StaffPermissionMiddleware::class, 'task.manage');

    // ==================== 个人中心接口 ====================
    Route::group('profile', function () {
        // 基本信息
        Route::get('', 'Profile/getProfile')->name('staff.profile.get');
        Route::put('', 'Profile/updateProfile')->name('staff.profile.update');
        Route::post('avatar', 'Profile/updateAvatar')->name('staff.profile.avatar');

        // 工作状态
        Route::get('work-status', 'Profile/getWorkStatus')->name('staff.profile.work_status');
        Route::put('work-status', 'Profile/updateWorkStatus')->name('staff.profile.update_work_status');

        // 服务区域
        Route::get('service-areas', 'Profile/getServiceAreas')->name('staff.profile.service_areas');
        Route::put('service-areas', 'Profile/updateServiceAreas')->name('staff.profile.update_service_areas');

        // 统计信息
        Route::get('stats', 'Profile/getStats')->name('staff.profile.stats');
        Route::get('stats/chart', 'Profile/getStatsChart')->name('staff.profile.stats_chart');

    })->middleware(StationOpenMiddleware::class)
        ->middleware(StaffAuthMiddleware::class, true);

    // ==================== 技能管理接口 ====================
    Route::group('skills', function () {
        // 技能信息
        Route::get('', 'Skill/getMySkills')->name('staff.skills.my');
        Route::get('categories', 'Skill/getSkillCategories')->name('staff.skills.categories');
        Route::get('available', 'Skill/getAvailableSkills')->name('staff.skills.available');

        // 技能认证
        Route::post('', 'Skill/addSkill')->name('staff.skills.add');
        Route::put(':id', 'Skill/updateSkill')->name('staff.skills.update');
        Route::delete(':id', 'Skill/removeSkill')->name('staff.skills.remove');
        Route::get(':id/auth-status', 'Skill/getAuthStatus')->name('staff.skills.auth_status');

    })->middleware(StationOpenMiddleware::class)
        ->middleware(StaffAuthMiddleware::class, true)
        ->middleware(StaffPermissionMiddleware::class, 'skill.manage');

    // ==================== 收入管理接口 ====================
    Route::group('income', function () {
        // 收入概览
        Route::get('overview', 'Income/getOverview')->name('staff.income.overview');
        Route::get('stats', 'Income/getStats')->name('staff.income.stats');
        Route::get('chart', 'Income/getChart')->name('staff.income.chart');

        // 收入记录
        Route::get('records', 'Income/getRecords')->name('staff.income.records');
        Route::get('records/:id', 'Income/getRecordDetail')->name('staff.income.record_detail');

        // 提现管理
        Route::post('withdraw', 'Income/withdraw')->name('staff.income.withdraw');
        Route::get('withdraw/records', 'Income/getWithdrawRecords')->name('staff.income.withdraw_records');
        Route::get('withdraw/config', 'Income/getWithdrawConfig')->name('staff.income.withdraw_config');

    })->middleware(StationOpenMiddleware::class)
        ->middleware(StaffAuthMiddleware::class, true)
        ->middleware(StaffPermissionMiddleware::class, 'income.manage');

    // ==================== 评价管理接口 ====================
    Route::group('reviews', function () {
        // 评价列表
        Route::get('', 'Review/getReviews')->name('staff.reviews.list');
        Route::get(':id', 'Review/getReviewDetail')->name('staff.reviews.detail');
        Route::get('stats', 'Review/getReviewStats')->name('staff.reviews.stats');

        // 评价回复
        Route::post(':id/reply', 'Review/replyReview')->name('staff.reviews.reply');
        Route::put(':id/reply', 'Review/updateReply')->name('staff.reviews.update_reply');

    })->middleware(StationOpenMiddleware::class)
        ->middleware(StaffAuthMiddleware::class, true);

    // ==================== 消息通知接口 ====================
    Route::group('messages', function () {
        // 消息列表
        Route::get('', 'Message/getMessages')->name('staff.messages.list');
        Route::get(':id', 'Message/getMessageDetail')->name('staff.messages.detail');
        Route::get('unread/count', 'Message/getUnreadCount')->name('staff.messages.unread_count');

        // 消息操作
        Route::put(':id/read', 'Message/markAsRead')->name('staff.messages.read');
        Route::put('read-all', 'Message/markAllAsRead')->name('staff.messages.read_all');
        Route::delete(':id', 'Message/deleteMessage')->name('staff.messages.delete');

    })->middleware(StationOpenMiddleware::class)
        ->middleware(StaffAuthMiddleware::class, true);

    // ==================== 设置管理接口 ====================
    Route::group('settings', function () {
        // 个人设置
        Route::get('', 'Setting/getSettings')->name('staff.settings.get');
        Route::put('', 'Setting/updateSettings')->name('staff.settings.update');

        // 通知设置
        Route::get('notifications', 'Setting/getNotificationSettings')->name('staff.settings.notifications');
        Route::put('notifications', 'Setting/updateNotificationSettings')->name('staff.settings.update_notifications');

        // 隐私设置
        Route::get('privacy', 'Setting/getPrivacySettings')->name('staff.settings.privacy');
        Route::put('privacy', 'Setting/updatePrivacySettings')->name('staff.settings.update_privacy');

    })->middleware(StationOpenMiddleware::class)
        ->middleware(StaffAuthMiddleware::class, true);

    // ==================== 帮助支持接口 ====================
    Route::group('support', function () {
        // 帮助文档
        Route::get('help', 'Support/getHelpList')->name('staff.support.help');
        Route::get('help/:id', 'Support/getHelpDetail')->name('staff.support.help_detail');

        // 常见问题
        Route::get('faq', 'Support/getFaqList')->name('staff.support.faq');
        Route::get('faq/:id', 'Support/getFaqDetail')->name('staff.support.faq_detail');

        // 意见反馈
        Route::post('feedback', 'Support/submitFeedback')->name('staff.support.feedback');
        Route::get('feedback', 'Support/getFeedbackList')->name('staff.support.feedback_list');

    })->middleware(StationOpenMiddleware::class)
        ->middleware(StaffAuthMiddleware::class, false); // 可选登录

    // ==================== 工具接口 ====================
    Route::group('utils', function () {
        // 文件上传
        Route::post('upload/image', 'Utils/uploadImage')->name('staff.utils.upload_image');
        Route::post('upload/video', 'Utils/uploadVideo')->name('staff.utils.upload_video');
        Route::post('upload/file', 'Utils/uploadFile')->name('staff.utils.upload_file');

        // 地理位置
        Route::get('location/cities', 'Utils/getCities')->name('staff.utils.cities');
        Route::get('location/districts', 'Utils/getDistricts')->name('staff.utils.districts');
        Route::post('location/geocode', 'Utils/geocode')->name('staff.utils.geocode');

        // 其他工具
        Route::post('qrcode/generate', 'Utils/generateQrcode')->name('staff.utils.qrcode');
        Route::get('version/check', 'Utils/checkVersion')->name('staff.utils.version_check');

    })->middleware(StationOpenMiddleware::class)
        ->middleware(StaffAuthMiddleware::class, true);

})->prefix('staff.')->middleware(AllowOriginMiddleware::class);
