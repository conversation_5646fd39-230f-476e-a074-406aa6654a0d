<?php
declare(strict_types=1);

namespace app\controller\user;

use app\controller\BaseController;
use app\services\user\EnterpriseAuthServices;
use think\Response;

/**
 * 用户端认证控制器 - 优化版
 * Class Auth
 * @package app\controller\user
 */
class Auth extends BaseController
{
    /**
     * 企业认证服务
     * @var EnterpriseAuthServices
     */
    protected $enterpriseAuthService;
    
    /**
     * 初始化
     */
    protected function initialize(): void
    {
        parent::initialize();
        $this->enterpriseAuthService = app()->make(EnterpriseAuthServices::class);
    }
    
    /**
     * 获取用户ID
     * @return int
     */
    private function getUserId(): int
    {
        return $this->request->uid ?? 0;
    }

    /**
     * 用户注册
     * @return Response
     */
    public function registerUser(): Response
    {
        try {
            $data = $this->request->post();
            
            // 验证必填字段
            $required = ['phone', 'password', 'verify_code'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    return $this->fail("请填写{$field}");
                }
            }
            
            // 这里应该调用用户注册服务
            // $userService = app()->make(UserServices::class);
            // $result = $userService->register($data);
            
            return $this->success([], '注册成功');
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 企业注册
     * @return Response
     */
    public function registerEnterprise(): Response
    {
        try {
            $data = $this->request->post();
            
            // 验证必填字段
            $required = ['enterprise_name', 'legal_person', 'contact_person', 'contact_phone', 'business_license'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    return $this->fail("请填写{$field}");
                }
            }
            
            $uid = $this->getUserId();
            
            // 检查是否已经是企业用户
            if ($this->enterpriseAuthService->isEnterprise($uid)) {
                return $this->fail('您已经是企业用户');
            }
            
            // 创建企业档案
            $result = $this->enterpriseAuthService->createEnterpriseProfile($uid, $data);
            
            if ($result) {
                return $this->success([], '企业注册申请提交成功，请等待审核');
            } else {
                return $this->fail('申请提交失败');
            }
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 提交个人认证
     * @return Response
     */
    public function submitPersonalAuth(): Response
    {
        try {
            $data = $this->request->post();
            
            // 验证必填字段
            $required = ['real_name', 'id_card'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    return $this->fail("请填写{$field}");
                }
            }
            
            $uid = $this->getUserId();
            
            // 这里应该调用个人认证服务
            // $personalAuthService = app()->make(PersonalAuthServices::class);
            // $result = $personalAuthService->submitAuth($uid, $data);
            
            return $this->success([], '个人认证资料提交成功，请等待审核');
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 提交企业认证
     * @return Response
     */
    public function submitEnterpriseAuth(): Response
    {
        try {
            $data = $this->request->post();
            
            // 验证必填字段
            $required = ['enterprise_name', 'legal_person', 'contact_person', 'contact_phone', 'business_license'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    return $this->fail("请填写{$field}");
                }
            }
            
            $uid = $this->getUserId();
            $enterpriseInfo = $this->enterpriseAuthService->getEnterpriseInfo($uid);
            
            if (!$enterpriseInfo) {
                // 如果没有企业档案，先创建
                $result = $this->enterpriseAuthService->createEnterpriseProfile($uid, $data);
            } else {
                // 更新企业信息并重新提交认证
                $updateData = array_merge($data, [
                    'auth_status' => 'reviewing',
                    'update_time' => time()
                ]);
                
                $result = \think\facade\Db::name('enterprise_profiles')
                    ->where('user_id', $uid)
                    ->update($updateData);
            }
            
            if ($result) {
                return $this->success([], '企业认证资料提交成功，请等待审核');
            } else {
                return $this->fail('提交失败');
            }
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取个人认证状态
     * @return Response
     */
    public function getPersonalStatus(): Response
    {
        try {
            $uid = $this->getUserId();
            
            // 这里应该调用个人认证服务获取状态
            // $personalAuthService = app()->make(PersonalAuthServices::class);
            // $status = $personalAuthService->getAuthStatus($uid);
            
            $data = [
                'auth_status' => 'pending',
                'message' => '个人认证功能开发中'
            ];
            
            return $this->success($data);
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取企业认证状态
     * @return Response
     */
    public function getEnterpriseStatus(): Response
    {
        try {
            $uid = $this->getUserId();
            $enterpriseInfo = $this->enterpriseAuthService->getEnterpriseInfo($uid);
            
            if (!$enterpriseInfo) {
                return $this->success([
                    'is_enterprise' => false,
                    'auth_status' => 'not_applied',
                    'message' => '尚未申请企业认证'
                ]);
            }
            
            $data = [
                'is_enterprise' => true,
                'auth_status' => $enterpriseInfo['auth_status'],
                'enterprise_info' => [
                    'enterprise_no' => $enterpriseInfo['enterprise_no'],
                    'enterprise_name' => $enterpriseInfo['enterprise_name'],
                    'enterprise_type' => $enterpriseInfo['enterprise_type'],
                    'contact_person' => $enterpriseInfo['contact_person'],
                    'contact_phone' => $enterpriseInfo['contact_phone'],
                    'credit_rating' => $enterpriseInfo['credit_rating'],
                    'credit_score' => $enterpriseInfo['credit_score'],
                    'task_count' => $enterpriseInfo['task_count'],
                    'completed_count' => $enterpriseInfo['completed_count'],
                    'create_time' => $enterpriseInfo['create_time'],
                    'auth_time' => $enterpriseInfo['auth_time']
                ]
            ];
            
            return $this->success($data);
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 更新企业认证信息
     * @return Response
     */
    public function updateEnterpriseAuth(): Response
    {
        try {
            $data = $this->request->put();
            $uid = $this->getUserId();
            
            $enterpriseInfo = $this->enterpriseAuthService->getEnterpriseInfo($uid);
            if (!$enterpriseInfo) {
                return $this->fail('企业信息不存在');
            }
            
            if ($enterpriseInfo['auth_status'] === 'approved') {
                return $this->fail('企业认证已通过，无需重复提交');
            }
            
            // 更新企业信息
            $updateData = array_merge($data, [
                'auth_status' => 'reviewing',
                'update_time' => time()
            ]);
            
            $result = \think\facade\Db::name('enterprise_profiles')
                ->where('user_id', $uid)
                ->update($updateData);
                
            if ($result) {
                return $this->success([], '企业认证信息更新成功');
            } else {
                return $this->fail('更新失败');
            }
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 上传个人认证文件
     * @return Response
     */
    public function uploadPersonalFiles(): Response
    {
        try {
            $files = $this->request->file();
            
            if (empty($files)) {
                return $this->fail('请选择要上传的文件');
            }
            
            $uid = $this->getUserId();
            $uploadedFiles = [];
            
            foreach ($files as $key => $file) {
                if ($file->isValid()) {
                    // 这里应该调用文件上传服务
                    // $uploadResult = $this->uploadService->upload($file);
                    // 暂时返回模拟数据
                    $uploadedFiles[$key] = [
                        'url' => '/uploads/personal/' . $file->getOriginalName(),
                        'name' => $file->getOriginalName(),
                        'size' => $file->getSize()
                    ];
                }
            }
            
            return $this->success($uploadedFiles, '文件上传成功');
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 上传企业认证文件
     * @return Response
     */
    public function uploadEnterpriseFiles(): Response
    {
        try {
            $files = $this->request->file();
            
            if (empty($files)) {
                return $this->fail('请选择要上传的文件');
            }
            
            $uid = $this->getUserId();
            $uploadedFiles = [];
            
            foreach ($files as $key => $file) {
                if ($file->isValid()) {
                    // 这里应该调用文件上传服务
                    // $uploadResult = $this->uploadService->upload($file);
                    // 暂时返回模拟数据
                    $uploadedFiles[$key] = [
                        'url' => '/uploads/enterprise/' . $file->getOriginalName(),
                        'name' => $file->getOriginalName(),
                        'size' => $file->getSize()
                    ];
                }
            }
            
            return $this->success($uploadedFiles, '文件上传成功');
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 上传营业执照
     * @return Response
     */
    public function uploadLicense(): Response
    {
        try {
            $file = $this->request->file('license');
            
            if (!$file) {
                return $this->fail('请选择营业执照文件');
            }
            
            if (!$file->isValid()) {
                return $this->fail('文件无效');
            }
            
            // 验证文件类型
            $allowedTypes = ['jpg', 'jpeg', 'png', 'pdf'];
            $extension = strtolower($file->getOriginalExtension());
            
            if (!in_array($extension, $allowedTypes)) {
                return $this->fail('只支持JPG、PNG、PDF格式的文件');
            }
            
            $uid = $this->getUserId();
            
            // 这里应该调用文件上传服务
            // $uploadResult = $this->uploadService->upload($file);
            // 暂时返回模拟数据
            $uploadResult = [
                'url' => '/uploads/license/' . $file->getOriginalName(),
                'name' => $file->getOriginalName(),
                'size' => $file->getSize(),
                'type' => $extension
            ];
            
            return $this->success($uploadResult, '营业执照上传成功');
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 验证手机号
     * @return Response
     */
    public function verifyPhone(): Response
    {
        try {
            $data = $this->request->post();
            
            if (empty($data['phone']) || empty($data['verify_code'])) {
                return $this->fail('手机号和验证码不能为空');
            }
            
            // 这里应该调用短信验证服务
            // $smsService = app()->make(SmsServices::class);
            // $result = $smsService->verifyCode($data['phone'], $data['verify_code']);
            
            return $this->success([], '手机号验证成功');
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}
