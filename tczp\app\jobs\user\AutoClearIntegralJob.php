<?php
namespace app\jobs\user;

use crmeb\basic\BaseJobs;
use app\services\user\UserIntegralServices;
use crmeb\traits\QueueTrait;

/**
 * 自动清空用户积分
 * Class AutoClearIntegralJob
 * @package app\jobs\user
 */
class AutoClearIntegralJob extends BaseJobs
{
    use QueueTrait;

    /**
     * @return string
     */
    protected static function queueName()
    {
        return 'CRMEB_PRO_TASK';
    }

    public function doJob()
    {
        //清空积分
        try {
            /** @var UserIntegralServices $userIntegralServices */
            $userIntegralServices = app()->make(UserIntegralServices::class);
            [$clear_time, $start_time, $end_time] = $userIntegralServices->getTime();
            //到清空积分的最后一天
            if ($clear_time == strtotime(date('Y-m-d', time()))) {
                $userIntegralServices->clearExpireIntegral();
            }
        } catch (\Throwable $e) {
            response_log_write([
                'message' => '清空积分,失败原因:[' . class_basename($this) . ']' . $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
		return true;
    }
}
