<?php
declare(strict_types=1);

namespace app\services\staff;

use app\services\BaseServices;
use think\facade\Db;
use think\facade\Cache;

/**
 * 师傅技能管理服务类
 * Class StaffSkillServices
 * @package app\services\staff
 */
class StaffSkillServices extends BaseServices
{
    /**
     * 获取技能分类列表
     * @param int $parentId
     * @return array
     */
    public function getSkillCategories(int $parentId = 0): array
    {
        $cacheKey = "skill_categories_{$parentId}";
        
        return Cache::remember($cacheKey, function () use ($parentId) {
            return Db::name('skill_categories')
                ->where('parent_id', $parentId)
                ->where('status', 1)
                ->order('sort asc, id asc')
                ->field([
                    'id', 'category_name', 'category_code', 
                    'icon', 'description', 'sort'
                ])
                ->select()
                ->toArray();
        }, 600);
    }
    
    /**
     * 获取技能分类树
     * @return array
     */
    public function getSkillCategoryTree(): array
    {
        $cacheKey = 'skill_category_tree';
        
        return Cache::remember($cacheKey, function () {
            // 获取所有分类
            $categories = Db::name('skill_categories')
                ->where('status', 1)
                ->order('sort asc, id asc')
                ->field([
                    'id', 'parent_id', 'category_name', 'category_code',
                    'icon', 'description', 'sort'
                ])
                ->select()
                ->toArray();
                
            // 构建树形结构
            return $this->buildTree($categories);
        }, 600);
    }
    
    /**
     * 获取技能列表
     * @param array $where
     * @param int $page
     * @param int $limit
     * @return array
     */
    public function getSkillList(array $where = [], int $page = 1, int $limit = 20): array
    {
        $query = Db::name('skills_new')
            ->alias('s')
            ->join('skill_categories sc', 's.category_id = sc.id')
            ->where('s.status', 1);
            
        // 分类筛选
        if (!empty($where['category_id'])) {
            $query->where('s.category_id', $where['category_id']);
        }
        
        // 关键词搜索
        if (!empty($where['keyword'])) {
            $query->where('s.skill_name', 'like', "%{$where['keyword']}%");
        }
        
        // 是否需要认证
        if (isset($where['certification_required'])) {
            $query->where('s.certification_required', $where['certification_required']);
        }
        
        $total = $query->count();
        $list = $query->field([
                's.id', 's.skill_name', 's.skill_code', 's.description',
                's.icon', 's.price_range', 's.unit', 's.certification_required',
                'sc.category_name', 'sc.category_code'
            ])
            ->order('s.sort asc, s.id asc')
            ->page($page, $limit)
            ->select()
            ->toArray();
            
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    }
    
    /**
     * 获取师傅的技能列表
     * @param int $workerId
     * @return array
     */
    public function getWorkerSkills(int $workerId): array
    {
        return Db::name('worker_skills_new')
            ->alias('ws')
            ->join('skills_new s', 'ws.skill_id = s.id')
            ->join('skill_categories sc', 's.category_id = sc.id')
            ->where('ws.worker_id', $workerId)
            ->field([
                'ws.id', 'ws.skill_level', 'ws.experience_years', 
                'ws.hourly_rate', 'ws.auth_status', 'ws.create_time',
                's.skill_name', 's.skill_code', 's.description as skill_description',
                's.icon', 's.price_range', 's.unit',
                'sc.category_name', 'sc.category_code'
            ])
            ->order('ws.create_time desc')
            ->select()
            ->toArray();
    }
    
    /**
     * 师傅申请技能认证
     * @param int $workerId
     * @param array $data
     * @return bool
     */
    public function applySkillAuth(int $workerId, array $data): bool
    {
        // 验证必填字段
        $required = ['skill_id', 'skill_level', 'hourly_rate'];
        foreach ($required as $field) {
            if (!isset($data[$field]) || $data[$field] === '') {
                throw new \Exception("字段 {$field} 不能为空");
            }
        }
        
        // 检查技能是否存在
        $skill = Db::name('skills_new')->where('id', $data['skill_id'])->find();
        if (!$skill) {
            throw new \Exception('技能不存在');
        }
        
        // 检查是否已申请过该技能
        $exists = Db::name('worker_skills_new')
            ->where('worker_id', $workerId)
            ->where('skill_id', $data['skill_id'])
            ->count() > 0;
            
        if ($exists) {
            throw new \Exception('已申请过该技能认证');
        }
        
        try {
            Db::startTrans();
            
            // 创建技能认证申请
            $skillData = [
                'worker_id' => $workerId,
                'skill_id' => $data['skill_id'],
                'skill_level' => $data['skill_level'],
                'experience_years' => $data['experience_years'] ?? 0,
                'hourly_rate' => $data['hourly_rate'],
                'certificate_images' => json_encode($data['certificate_images'] ?? []),
                'work_samples' => json_encode($data['work_samples'] ?? []),
                'auth_status' => 'pending',
                'create_time' => time(),
                'update_time' => time(),
            ];
            
            $result = Db::name('worker_skills_new')->insert($skillData);
            
            if ($result) {
                // 记录认证申请日志
                $this->logSkillAuthRecord($workerId, $data['skill_id'], 'pending');
            }
            
            Db::commit();
            return $result;
            
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 更新技能认证状态
     * @param int $id
     * @param string $status
     * @param string $reason
     * @param int $reviewerId
     * @return bool
     */
    public function updateSkillAuthStatus(int $id, string $status, string $reason = '', int $reviewerId = 0): bool
    {
        $allowedStatus = ['pending', 'approved', 'rejected'];
        if (!in_array($status, $allowedStatus)) {
            throw new \Exception('无效的认证状态');
        }
        
        $updateData = [
            'auth_status' => $status,
            'auth_time' => time(),
            'reviewer_id' => $reviewerId,
            'update_time' => time(),
        ];
        
        if ($status === 'rejected' && $reason) {
            $updateData['reject_reason'] = $reason;
        }
        
        $result = Db::name('worker_skills_new')
            ->where('id', $id)
            ->update($updateData);
            
        if ($result) {
            // 获取技能信息用于记录日志
            $skillInfo = Db::name('worker_skills_new')
                ->where('id', $id)
                ->field('worker_id, skill_id')
                ->find();
                
            if ($skillInfo) {
                $this->logSkillAuthRecord($skillInfo['worker_id'], $skillInfo['skill_id'], $status, $reason);
            }
        }
        
        return $result > 0;
    }
    
    /**
     * 获取技能认证统计
     * @param int $workerId
     * @return array
     */
    public function getSkillAuthStats(int $workerId): array
    {
        $stats = Db::name('worker_skills_new')
            ->where('worker_id', $workerId)
            ->field([
                'COUNT(*) as total',
                'SUM(CASE WHEN auth_status = "pending" THEN 1 ELSE 0 END) as pending',
                'SUM(CASE WHEN auth_status = "approved" THEN 1 ELSE 0 END) as approved',
                'SUM(CASE WHEN auth_status = "rejected" THEN 1 ELSE 0 END) as rejected'
            ])
            ->find();
            
        return $stats ?: [
            'total' => 0,
            'pending' => 0,
            'approved' => 0,
            'rejected' => 0
        ];
    }
    
    /**
     * 构建树形结构
     * @param array $data
     * @param int $parentId
     * @return array
     */
    private function buildTree(array $data, int $parentId = 0): array
    {
        $tree = [];
        
        foreach ($data as $item) {
            if ($item['parent_id'] == $parentId) {
                $children = $this->buildTree($data, $item['id']);
                if (!empty($children)) {
                    $item['children'] = $children;
                }
                $tree[] = $item;
            }
        }
        
        return $tree;
    }
    
    /**
     * 记录技能认证日志
     * @param int $workerId
     * @param int $skillId
     * @param string $status
     * @param string $reason
     */
    private function logSkillAuthRecord(int $workerId, int $skillId, string $status, string $reason = ''): void
    {
        Db::name('worker_auth_records')->insert([
            'worker_id' => $workerId,
            'auth_type' => 'skill',
            'auth_item_id' => $skillId,
            'auth_data' => json_encode(['reason' => $reason]),
            'status' => $status,
            'submit_time' => time(),
            'create_time' => time(),
        ]);
    }
}
