<?php

use app\http\middleware\AllowOriginMiddleware;
use app\http\middleware\StationOpenMiddleware;
use app\http\middleware\user\UserAuthMiddleware;
use app\http\middleware\user\UserPermissionMiddleware;
use app\http\middleware\user\EnterpriseAuthMiddleware;
use think\facade\Route;

/**
 * 用户端（企业端）接口路由配置 - 优化版本
 * 版本: v2.0
 * 特性: 统一认证、权限控制、RESTful规范、企业功能完善
 */
Route::group('user/v2', function () {

    // ==================== 公共接口（无需登录） ====================
    Route::group('public', function () {
        // 系统配置
        Route::get('config', 'Common/getConfig')->name('user.config');
        Route::get('cities', 'Common/getCityList')->name('user.cities');
        Route::get('task-categories', 'Common/getTaskCategories')->name('user.task_categories');
        Route::get('skill-categories', 'Common/getSkillCategories')->name('user.skill_categories');

        // 任务大厅（公开浏览）
        Route::get('tasks', 'Task/getTaskList')->name('user.public.tasks');
        Route::get('tasks/:id', 'Task/getTaskDetail')->name('user.public.task_detail');

        // 师傅展示（公开）
        Route::get('workers', 'Worker/getWorkerList')->name('user.public.workers');
        Route::get('workers/:id', 'Worker/getWorkerDetail')->name('user.public.worker_detail');

        // 企业展示（公开）
        Route::get('enterprises', 'Enterprise/getEnterpriseList')->name('user.public.enterprises');
        Route::get('enterprises/:id', 'Enterprise/getEnterpriseDetail')->name('user.public.enterprise_detail');

        // 注册相关
        Route::post('register/user', 'Auth/registerUser')->name('user.register.user');
        Route::post('register/enterprise', 'Auth/registerEnterprise')->name('user.register.enterprise');
        Route::post('verify-phone', 'Auth/verifyPhone')->name('user.verify_phone');

    })->middleware(StationOpenMiddleware::class);

    // ==================== 用户认证相关接口 ====================
    Route::group('auth', function () {
        // 个人认证
        Route::post('personal/submit', 'Auth/submitPersonalAuth')->name('user.auth.personal');
        Route::get('personal/status', 'Auth/getPersonalStatus')->name('user.auth.personal_status');
        
        // 企业认证
        Route::post('enterprise/submit', 'Auth/submitEnterpriseAuth')->name('user.auth.enterprise');
        Route::get('enterprise/status', 'Auth/getEnterpriseStatus')->name('user.auth.enterprise_status');
        Route::put('enterprise/update', 'Auth/updateEnterpriseAuth')->name('user.auth.enterprise_update');
        
        // 文件上传
        Route::post('upload/personal', 'Auth/uploadPersonalFiles')->name('user.auth.upload_personal');
        Route::post('upload/enterprise', 'Auth/uploadEnterpriseFiles')->name('user.auth.upload_enterprise');
        Route::post('upload/license', 'Auth/uploadLicense')->name('user.auth.upload_license');

    })->middleware(StationOpenMiddleware::class)
        ->middleware(UserAuthMiddleware::class, true);

    // ==================== 任务管理接口 ====================
    Route::group('tasks', function () {
        // 任务发布
        Route::post('', 'Task/publishTask')->name('user.tasks.publish');
        Route::get('my', 'Task/getMyTasks')->name('user.tasks.my');
        Route::get(':id', 'Task/getTaskDetail')->name('user.tasks.detail');
        Route::put(':id', 'Task/updateTask')->name('user.tasks.update');
        Route::delete(':id', 'Task/cancelTask')->name('user.tasks.cancel');
        
        // 任务申请管理
        Route::get(':id/applications', 'Task/getTaskApplications')->name('user.tasks.applications');
        Route::put('applications/:id/approve', 'Task/approveApplication')->name('user.tasks.approve');
        Route::put('applications/:id/reject', 'Task/rejectApplication')->name('user.tasks.reject');
        
        // 任务状态管理
        Route::put(':id/confirm', 'Task/confirmTask')->name('user.tasks.confirm');
        Route::put(':id/complete', 'Task/completeTask')->name('user.tasks.complete');
        
        // 任务模板
        Route::get('templates', 'Task/getTaskTemplates')->name('user.tasks.templates');
        Route::post('templates', 'Task/saveTaskTemplate')->name('user.tasks.save_template');
        Route::post('templates/:id/use', 'Task/useTaskTemplate')->name('user.tasks.use_template');

    })->middleware(StationOpenMiddleware::class)
        ->middleware(UserAuthMiddleware::class, true)
        ->middleware(UserPermissionMiddleware::class, 'task.create');

    // ==================== 企业管理接口 ====================
    Route::group('enterprise', function () {
        // 企业信息管理
        Route::get('profile', 'Enterprise/getProfile')->name('user.enterprise.profile');
        Route::put('profile', 'Enterprise/updateProfile')->name('user.enterprise.profile_update');
        
        // 师傅管理
        Route::get('workers', 'Enterprise/getWorkers')->name('user.enterprise.workers');
        Route::post('workers/:id/favorite', 'Enterprise/favoriteWorker')->name('user.enterprise.favorite_worker');
        Route::delete('workers/:id/favorite', 'Enterprise/unfavoriteWorker')->name('user.enterprise.unfavorite_worker');
        Route::get('workers/favorites', 'Enterprise/getFavoriteWorkers')->name('user.enterprise.favorite_workers');
        
        // 批量任务管理
        Route::post('tasks/batch', 'Enterprise/batchPublishTasks')->name('user.enterprise.batch_tasks');
        Route::get('tasks/batch/:id', 'Enterprise/getBatchTaskDetail')->name('user.enterprise.batch_task_detail');
        
        // 企业统计
        Route::get('stats', 'Enterprise/getStats')->name('user.enterprise.stats');
        Route::get('stats/chart', 'Enterprise/getStatsChart')->name('user.enterprise.stats_chart');
        Route::get('reports', 'Enterprise/getReports')->name('user.enterprise.reports');

    })->middleware(StationOpenMiddleware::class)
        ->middleware(UserAuthMiddleware::class, true)
        ->middleware(EnterpriseAuthMiddleware::class, 'approved')
        ->middleware(UserPermissionMiddleware::class, 'enterprise.manage');

    // ==================== 支付管理接口 ====================
    Route::group('payments', function () {
        // 支付订单
        Route::post('orders', 'Payment/createOrder')->name('user.payments.create_order');
        Route::get('orders', 'Payment/getOrders')->name('user.payments.orders');
        Route::get('orders/:id', 'Payment/getOrderDetail')->name('user.payments.order_detail');
        Route::put('orders/:id/pay', 'Payment/payOrder')->name('user.payments.pay');
        Route::put('orders/:id/refund', 'Payment/refundOrder')->name('user.payments.refund');
        
        // 支付统计
        Route::get('stats', 'Payment/getStats')->name('user.payments.stats');
        Route::get('stats/chart', 'Payment/getStatsChart')->name('user.payments.stats_chart');
        
        // 结算管理
        Route::get('settlements', 'Payment/getSettlements')->name('user.payments.settlements');
        Route::get('settlements/:id', 'Payment/getSettlementDetail')->name('user.payments.settlement_detail');

    })->middleware(StationOpenMiddleware::class)
        ->middleware(UserAuthMiddleware::class, true)
        ->middleware(UserPermissionMiddleware::class, 'payment.pay');

    // ==================== 评价管理接口 ====================
    Route::group('reviews', function () {
        // 评价管理
        Route::post('', 'Review/createReview')->name('user.reviews.create');
        Route::get('', 'Review/getReviews')->name('user.reviews.list');
        Route::get(':id', 'Review/getReviewDetail')->name('user.reviews.detail');
        Route::put(':id', 'Review/updateReview')->name('user.reviews.update');
        Route::delete(':id', 'Review/deleteReview')->name('user.reviews.delete');
        
        // 评价统计
        Route::get('stats', 'Review/getStats')->name('user.reviews.stats');

    })->middleware(StationOpenMiddleware::class)
        ->middleware(UserAuthMiddleware::class, true)
        ->middleware(UserPermissionMiddleware::class, 'review.create');

    // ==================== 个人中心接口 ====================
    Route::group('profile', function () {
        // 基本信息
        Route::get('', 'Profile/getProfile')->name('user.profile.get');
        Route::put('', 'Profile/updateProfile')->name('user.profile.update');
        Route::post('avatar', 'Profile/updateAvatar')->name('user.profile.avatar');
        
        // 账户设置
        Route::get('settings', 'Profile/getSettings')->name('user.profile.settings');
        Route::put('settings', 'Profile/updateSettings')->name('user.profile.update_settings');
        
        // 通知设置
        Route::get('notifications', 'Profile/getNotificationSettings')->name('user.profile.notifications');
        Route::put('notifications', 'Profile/updateNotificationSettings')->name('user.profile.update_notifications');
        
        // 统计信息
        Route::get('stats', 'Profile/getStats')->name('user.profile.stats');
        Route::get('stats/chart', 'Profile/getStatsChart')->name('user.profile.stats_chart');

    })->middleware(StationOpenMiddleware::class)
        ->middleware(UserAuthMiddleware::class, true);

    // ==================== 消息通知接口 ====================
    Route::group('messages', function () {
        // 消息列表
        Route::get('', 'Message/getMessages')->name('user.messages.list');
        Route::get(':id', 'Message/getMessageDetail')->name('user.messages.detail');
        Route::get('unread/count', 'Message/getUnreadCount')->name('user.messages.unread_count');
        
        // 消息操作
        Route::put(':id/read', 'Message/markAsRead')->name('user.messages.read');
        Route::put('read-all', 'Message/markAllAsRead')->name('user.messages.read_all');
        Route::delete(':id', 'Message/deleteMessage')->name('user.messages.delete');

    })->middleware(StationOpenMiddleware::class)
        ->middleware(UserAuthMiddleware::class, true);

    // ==================== 帮助支持接口 ====================
    Route::group('support', function () {
        // 帮助文档
        Route::get('help', 'Support/getHelpList')->name('user.support.help');
        Route::get('help/:id', 'Support/getHelpDetail')->name('user.support.help_detail');
        
        // 常见问题
        Route::get('faq', 'Support/getFaqList')->name('user.support.faq');
        Route::get('faq/:id', 'Support/getFaqDetail')->name('user.support.faq_detail');
        
        // 意见反馈
        Route::post('feedback', 'Support/submitFeedback')->name('user.support.feedback');
        Route::get('feedback', 'Support/getFeedbackList')->name('user.support.feedback_list');

    })->middleware(StationOpenMiddleware::class)
        ->middleware(UserAuthMiddleware::class, false); // 可选登录

    // ==================== 工具接口 ====================
    Route::group('utils', function () {
        // 文件上传
        Route::post('upload/image', 'Utils/uploadImage')->name('user.utils.upload_image');
        Route::post('upload/video', 'Utils/uploadVideo')->name('user.utils.upload_video');
        Route::post('upload/file', 'Utils/uploadFile')->name('user.utils.upload_file');
        
        // 地理位置
        Route::get('location/cities', 'Utils/getCities')->name('user.utils.cities');
        Route::get('location/districts', 'Utils/getDistricts')->name('user.utils.districts');
        Route::post('location/geocode', 'Utils/geocode')->name('user.utils.geocode');
        
        // 其他工具
        Route::post('qrcode/generate', 'Utils/generateQrcode')->name('user.utils.qrcode');
        Route::get('version/check', 'Utils/checkVersion')->name('user.utils.version_check');

    })->middleware(StationOpenMiddleware::class)
        ->middleware(UserAuthMiddleware::class, true);

})->prefix('user.')->middleware(AllowOriginMiddleware::class);
