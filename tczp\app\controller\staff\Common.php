<?php
// +----------------------------------------------------------------------
// | 同城用工系统 - 师傅端
// +----------------------------------------------------------------------

namespace app\controller\staff;

use app\controller\BaseController;
use think\facade\Db;

/**
 * 公共控制器
 */
class Common extends BaseController
{
    /**
     * 获取系统配置
     */
    public function getConfig()
    {
        try {
            $config = [
                'app_name' => '同城用工师傅端',
                'version' => '1.0.0',
                'api_version' => 'v1.0',
                'upload_max_size' => 10 * 1024 * 1024, // 10MB
                'supported_image_types' => ['jpg', 'jpeg', 'png', 'gif'],
                'supported_video_types' => ['mp4', 'avi', 'mov'],
                'contact_phone' => '************',
                'contact_email' => '<EMAIL>',
                'privacy_url' => 'https://www.ywork.com/privacy',
                'terms_url' => 'https://www.ywork.com/terms',
            ];

            return $this->success($config);
        } catch (\Exception $e) {
            return $this->fail('获取配置失败：' . $e->getMessage());
        }
    }

    /**
     * 获取城市列表
     */
    public function getCityList()
    {
        try {
            $cities = Db::name('city')
                ->field('id, name, code, parent_id, level')
                ->where('status', 1)
                ->order('sort asc, id asc')
                ->select()
                ->toArray();

            // 构建树形结构
            $cityTree = $this->buildTree($cities, 0);

            return $this->success($cityTree);
        } catch (\Exception $e) {
            return $this->fail('获取城市列表失败：' . $e->getMessage());
        }
    }

    /**
     * 获取任务分类
     */
    public function getTaskCategories()
    {
        try {
            $categories = Db::name('task_category')
                ->field('id, name, icon, parent_id, sort, description')
                ->where('status', 1)
                ->order('sort asc, id asc')
                ->select()
                ->toArray();

            // 构建树形结构
            $categoryTree = $this->buildTree($categories, 0);

            return $this->success($categoryTree);
        } catch (\Exception $e) {
            return $this->fail('获取任务分类失败：' . $e->getMessage());
        }
    }

    /**
     * 构建树形结构
     */
    private function buildTree($data, $parentId = 0)
    {
        $tree = [];
        foreach ($data as $item) {
            if ($item['parent_id'] == $parentId) {
                $children = $this->buildTree($data, $item['id']);
                if ($children) {
                    $item['children'] = $children;
                }
                $tree[] = $item;
            }
        }
        return $tree;
    }

    /**
     * 成功响应
     */
    protected function success($data = [], $msg = 'success', $code = 200)
    {
        return json([
            'status' => $code,
            'msg' => $msg,
            'data' => $data,
            'time' => time()
        ]);
    }

    /**
     * 失败响应
     */
    protected function fail($msg = 'error', $code = 400, $data = [])
    {
        return json([
            'status' => $code,
            'msg' => $msg,
            'data' => $data,
            'time' => time()
        ]);
    }
} 