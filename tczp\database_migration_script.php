<?php
/**
 * 数据库迁移执行脚本
 * 用于执行师傅端系统优化的数据库迁移
 */

// 引入ThinkPHP框架
require_once __DIR__ . '/vendor/autoload.php';

use think\facade\Db;
use think\facade\Config;

try {
    echo "==================== 师傅端数据库优化迁移 ====================\n";
    echo "开始时间: " . date('Y-m-d H:i:s') . "\n\n";

    // 第一步：创建用户角色表
    echo "第一步：创建用户角色表...\n";
    
    // 删除已存在的表
    Db::execute("DROP TABLE IF EXISTS `user_roles`");
    
    // 创建用户角色表
    $sql = "CREATE TABLE `user_roles` (
        `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
        `role_code` varchar(50) NOT NULL COMMENT '角色代码',
        `role_name` varchar(100) NOT NULL COMMENT '角色名称',
        `description` varchar(255) DEFAULT NULL COMMENT '角色描述',
        `permissions` json DEFAULT NULL COMMENT '角色权限JSON',
        `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1启用，0禁用',
        `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
        `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
        PRIMARY KEY (`id`),
        UNIQUE KEY `role_code` (`role_code`),
        KEY `status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色表'";
    
    Db::execute($sql);
    echo "✅ 用户角色表创建成功\n";

    // 插入基础角色数据
    $currentTime = time();
    $roles = [
        [
            'role_code' => 'customer',
            'role_name' => '普通用户',
            'description' => '发布任务的用户',
            'permissions' => json_encode(['task.create', 'task.manage', 'payment.pay']),
            'create_time' => $currentTime
        ],
        [
            'role_code' => 'worker',
            'role_name' => '师傅',
            'description' => '接单的师傅用户',
            'permissions' => json_encode(['task.apply', 'task.execute', 'income.manage']),
            'create_time' => $currentTime
        ],
        [
            'role_code' => 'enterprise',
            'role_name' => '企业用户',
            'description' => '企业发布任务',
            'permissions' => json_encode(['task.batch_create', 'worker.invite', 'report.view']),
            'create_time' => $currentTime
        ],
        [
            'role_code' => 'admin',
            'role_name' => '管理员',
            'description' => '平台管理员',
            'permissions' => json_encode(['*']),
            'create_time' => $currentTime
        ]
    ];
    
    Db::name('user_roles')->insertAll($roles);
    echo "✅ 基础角色数据插入成功\n\n";

    // 第二步：创建用户角色关联表
    echo "第二步：创建用户角色关联表...\n";
    
    Db::execute("DROP TABLE IF EXISTS `user_role_relations`");
    
    $sql = "CREATE TABLE `user_role_relations` (
        `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
        `user_id` int(11) NOT NULL COMMENT '用户ID',
        `role_id` int(11) NOT NULL COMMENT '角色ID',
        `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1启用，0禁用',
        `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
        PRIMARY KEY (`id`),
        UNIQUE KEY `user_role` (`user_id`, `role_id`),
        KEY `user_id` (`user_id`),
        KEY `role_id` (`role_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表'";
    
    Db::execute($sql);
    echo "✅ 用户角色关联表创建成功\n\n";

    // 第三步：创建师傅档案表
    echo "第三步：创建师傅档案表...\n";
    
    Db::execute("DROP TABLE IF EXISTS `worker_profiles`");
    
    $sql = "CREATE TABLE `worker_profiles` (
        `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
        `user_id` int(11) NOT NULL COMMENT '用户ID',
        `worker_no` varchar(32) NOT NULL COMMENT '师傅编号',
        `real_name` varchar(50) NOT NULL COMMENT '真实姓名',
        `id_card` varchar(18) NOT NULL COMMENT '身份证号',
        `phone` varchar(15) NOT NULL COMMENT '手机号',
        `emergency_contact` varchar(15) DEFAULT NULL COMMENT '紧急联系人电话',
        `work_experience` int(11) NOT NULL DEFAULT '0' COMMENT '工作经验（年）',
        `service_areas` json DEFAULT NULL COMMENT '服务区域JSON',
        `work_status` enum('available','busy','offline') NOT NULL DEFAULT 'available' COMMENT '工作状态',
        `auth_status` enum('pending','reviewing','approved','rejected') NOT NULL DEFAULT 'pending' COMMENT '认证状态',
        `auth_time` int(11) NOT NULL DEFAULT '0' COMMENT '认证时间',
        `rating` decimal(3,2) NOT NULL DEFAULT '5.00' COMMENT '评分',
        `order_count` int(11) NOT NULL DEFAULT '0' COMMENT '接单数量',
        `completion_rate` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '完成率',
        `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
        `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
        PRIMARY KEY (`id`),
        UNIQUE KEY `user_id` (`user_id`),
        UNIQUE KEY `worker_no` (`worker_no`),
        KEY `auth_status` (`auth_status`),
        KEY `work_status` (`work_status`),
        KEY `rating` (`rating`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='师傅信息表'";
    
    Db::execute($sql);
    echo "✅ 师傅档案表创建成功\n\n";

    // 第四步：创建技能分类表
    echo "第四步：创建技能分类表...\n";
    
    Db::execute("DROP TABLE IF EXISTS `skill_categories`");
    
    $sql = "CREATE TABLE `skill_categories` (
        `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
        `parent_id` int(11) NOT NULL DEFAULT '0' COMMENT '父级ID',
        `category_name` varchar(100) NOT NULL COMMENT '分类名称',
        `category_code` varchar(50) NOT NULL COMMENT '分类代码',
        `icon` varchar(255) DEFAULT NULL COMMENT '分类图标',
        `description` varchar(255) DEFAULT NULL COMMENT '分类描述',
        `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
        `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1启用，0禁用',
        `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
        PRIMARY KEY (`id`),
        UNIQUE KEY `category_code` (`category_code`),
        KEY `parent_id` (`parent_id`),
        KEY `status` (`status`),
        KEY `sort` (`sort`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='技能分类表'";
    
    Db::execute($sql);
    echo "✅ 技能分类表创建成功\n";

    // 插入基础技能分类
    $categories = [
        ['parent_id' => 0, 'category_name' => '家政服务', 'category_code' => 'housekeeping', 'description' => '家庭清洁、保姆、月嫂等服务', 'sort' => 1, 'create_time' => $currentTime],
        ['parent_id' => 0, 'category_name' => '维修安装', 'category_code' => 'repair', 'description' => '家电维修、水电安装、装修等', 'sort' => 2, 'create_time' => $currentTime],
        ['parent_id' => 0, 'category_name' => '搬家运输', 'category_code' => 'moving', 'description' => '搬家、货运、配送等服务', 'sort' => 3, 'create_time' => $currentTime],
        ['parent_id' => 0, 'category_name' => '美容美发', 'category_code' => 'beauty', 'description' => '美发、美甲、化妆等服务', 'sort' => 4, 'create_time' => $currentTime],
        ['parent_id' => 0, 'category_name' => '教育培训', 'category_code' => 'education', 'description' => '家教、技能培训等', 'sort' => 5, 'create_time' => $currentTime],
        ['parent_id' => 0, 'category_name' => '健康护理', 'category_code' => 'healthcare', 'description' => '按摩、护理、康复等', 'sort' => 6, 'create_time' => $currentTime],
        ['parent_id' => 0, 'category_name' => '其他服务', 'category_code' => 'others', 'description' => '其他各类服务', 'sort' => 99, 'create_time' => $currentTime]
    ];
    
    Db::name('skill_categories')->insertAll($categories);
    echo "✅ 基础技能分类数据插入成功\n\n";

    // 第五步：创建技能表
    echo "第五步：创建技能表...\n";
    
    Db::execute("DROP TABLE IF EXISTS `skills_new`");
    
    $sql = "CREATE TABLE `skills_new` (
        `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '技能ID',
        `category_id` int(11) NOT NULL COMMENT '分类ID',
        `skill_name` varchar(100) NOT NULL COMMENT '技能名称',
        `skill_code` varchar(50) NOT NULL COMMENT '技能代码',
        `description` varchar(255) DEFAULT NULL COMMENT '技能描述',
        `icon` varchar(255) DEFAULT NULL COMMENT '技能图标',
        `price_range` varchar(50) DEFAULT NULL COMMENT '价格区间',
        `unit` varchar(20) DEFAULT NULL COMMENT '计价单位',
        `certification_required` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否需要认证',
        `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
        `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1启用，0禁用',
        `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
        PRIMARY KEY (`id`),
        UNIQUE KEY `skill_code` (`skill_code`),
        KEY `category_id` (`category_id`),
        KEY `status` (`status`),
        KEY `sort` (`sort`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='技能表'";
    
    Db::execute($sql);
    echo "✅ 技能表创建成功\n\n";

    echo "==================== 数据库结构创建完成 ====================\n";
    echo "完成时间: " . date('Y-m-d H:i:s') . "\n";
    echo "✅ 所有表结构创建成功！\n";
    echo "✅ 基础数据插入成功！\n\n";
    
    echo "下一步请执行数据迁移脚本来迁移现有数据。\n";

} catch (Exception $e) {
    echo "❌ 执行失败: " . $e->getMessage() . "\n";
    echo "错误文件: " . $e->getFile() . "\n";
    echo "错误行号: " . $e->getLine() . "\n";
}
