-- ==================== 数据迁移脚本 ====================
-- 用于将现有数据迁移到新的数据库结构中

-- 设置时间变量
SET @current_time = UNIX_TIMESTAMP();

-- ==================== 第一步：创建新表结构 ====================
-- 执行 database_optimization.sql 中的表结构

-- ==================== 第二步：迁移现有用户角色数据 ====================

-- 2.1 为现有用户分配角色（基于现有的identity字段）
INSERT INTO `user_role_relations` (`user_id`, `role_id`, `status`, `create_time`)
SELECT 
    u.uid,
    CASE 
        WHEN u.identity = 2 THEN (SELECT id FROM user_roles WHERE role_code = 'admin')
        WHEN u.is_channel = 1 THEN (SELECT id FROM user_roles WHERE role_code = 'enterprise')
        WHEN EXISTS (SELECT 1 FROM worker_skills WHERE worker_id = u.uid) THEN (SELECT id FROM user_roles WHERE role_code = 'worker')
        ELSE (SELECT id FROM user_roles WHERE role_code = 'customer')
    END as role_id,
    1,
    @current_time
FROM `user` u
WHERE u.uid > 0
AND NOT EXISTS (
    SELECT 1 FROM user_role_relations urr 
    WHERE urr.user_id = u.uid
);

-- ==================== 第三步：迁移师傅信息数据 ====================

-- 3.1 从现有数据创建师傅档案
INSERT INTO `worker_profiles` (
    `user_id`, `worker_no`, `real_name`, `id_card`, `phone`, 
    `work_experience`, `service_areas`, `auth_status`, 
    `create_time`, `update_time`
)
SELECT 
    u.uid,
    CONCAT('W', DATE_FORMAT(FROM_UNIXTIME(u.add_time), '%y%m%d'), LPAD(u.uid, 4, '0')) as worker_no,
    COALESCE(u.real_name, u.nickname, '') as real_name,
    COALESCE(u.card_id, '') as id_card,
    u.phone,
    COALESCE(ws.experience_years, 0) as work_experience,
    COALESCE(ws.service_areas, '[]') as service_areas,
    CASE 
        WHEN ua.status = 'approved' THEN 'approved'
        WHEN ua.status = 'rejected' THEN 'rejected'
        WHEN ua.status = 'pending' THEN 'reviewing'
        ELSE 'pending'
    END as auth_status,
    u.add_time as create_time,
    @current_time as update_time
FROM `user` u
INNER JOIN `user_role_relations` urr ON u.uid = urr.user_id
INNER JOIN `user_roles` ur ON urr.role_id = ur.id AND ur.role_code = 'worker'
LEFT JOIN `worker_skills` ws ON u.uid = ws.worker_id
LEFT JOIN `user_auth` ua ON u.uid = ua.user_id AND ua.auth_type = 'real_name'
WHERE u.uid > 0
GROUP BY u.uid;

-- ==================== 第四步：迁移技能分类数据 ====================

-- 4.1 创建基础技能分类
INSERT INTO `skill_categories` (`parent_id`, `category_name`, `category_code`, `description`, `sort`, `status`, `create_time`) VALUES
(0, '家政服务', 'housekeeping', '家庭清洁、保姆、月嫂等服务', 1, 1, @current_time),
(0, '维修安装', 'repair', '家电维修、水电安装、装修等', 2, 1, @current_time),
(0, '搬家运输', 'moving', '搬家、货运、配送等服务', 3, 1, @current_time),
(0, '美容美发', 'beauty', '美发、美甲、化妆等服务', 4, 1, @current_time),
(0, '教育培训', 'education', '家教、技能培训等', 5, 1, @current_time),
(0, '健康护理', 'healthcare', '按摩、护理、康复等', 6, 1, @current_time),
(0, '其他服务', 'others', '其他各类服务', 99, 1, @current_time);

-- 4.2 创建二级分类（家政服务）
SET @housekeeping_id = (SELECT id FROM skill_categories WHERE category_code = 'housekeeping');
INSERT INTO `skill_categories` (`parent_id`, `category_name`, `category_code`, `description`, `sort`, `status`, `create_time`) VALUES
(@housekeeping_id, '家庭清洁', 'house_cleaning', '日常清洁、深度清洁', 1, 1, @current_time),
(@housekeeping_id, '保姆服务', 'nanny', '住家保姆、钟点工', 2, 1, @current_time),
(@housekeeping_id, '月嫂育儿', 'childcare', '月嫂、育儿嫂、早教', 3, 1, @current_time),
(@housekeeping_id, '老人护理', 'elderly_care', '老人陪护、生活照料', 4, 1, @current_time);

-- 4.3 创建二级分类（维修安装）
SET @repair_id = (SELECT id FROM skill_categories WHERE category_code = 'repair');
INSERT INTO `skill_categories` (`parent_id`, `category_name`, `category_code`, `description`, `sort`, `status`, `create_time`) VALUES
(@repair_id, '家电维修', 'appliance_repair', '空调、洗衣机、冰箱等维修', 1, 1, @current_time),
(@repair_id, '水电安装', 'plumbing_electrical', '水管、电路安装维修', 2, 1, @current_time),
(@repair_id, '装修装饰', 'decoration', '室内装修、墙面处理', 3, 1, @current_time),
(@repair_id, '家具安装', 'furniture_assembly', '家具组装、安装', 4, 1, @current_time);

-- ==================== 第五步：创建基础技能数据 ====================

-- 5.1 家庭清洁技能
SET @house_cleaning_id = (SELECT id FROM skill_categories WHERE category_code = 'house_cleaning');
INSERT INTO `skills_new` (`category_id`, `skill_name`, `skill_code`, `description`, `price_range`, `unit`, `sort`, `status`, `create_time`) VALUES
(@house_cleaning_id, '日常保洁', 'daily_cleaning', '日常家庭清洁服务', '30-50元', '小时', 1, 1, @current_time),
(@house_cleaning_id, '深度清洁', 'deep_cleaning', '全屋深度清洁服务', '200-500元', '次', 2, 1, @current_time),
(@house_cleaning_id, '开荒保洁', 'move_in_cleaning', '新房开荒清洁', '300-800元', '次', 3, 1, @current_time),
(@house_cleaning_id, '玻璃清洁', 'window_cleaning', '门窗玻璃清洁', '5-10元', '平米', 4, 1, @current_time);

-- 5.2 家电维修技能
SET @appliance_repair_id = (SELECT id FROM skill_categories WHERE category_code = 'appliance_repair');
INSERT INTO `skills_new` (`category_id`, `skill_name`, `skill_code`, `description`, `price_range`, `unit`, `certification_required`, `sort`, `status`, `create_time`) VALUES
(@appliance_repair_id, '空调维修', 'ac_repair', '空调安装、维修、清洗', '100-300元', '次', 1, 1, 1, @current_time),
(@appliance_repair_id, '洗衣机维修', 'washer_repair', '洗衣机故障维修', '80-200元', '次', 1, 2, 1, @current_time),
(@appliance_repair_id, '冰箱维修', 'fridge_repair', '冰箱故障维修', '100-250元', '次', 1, 3, 1, @current_time),
(@appliance_repair_id, '热水器维修', 'heater_repair', '热水器安装维修', '100-200元', '次', 1, 4, 1, @current_time);

-- ==================== 第六步：迁移现有技能数据 ====================

-- 6.1 迁移师傅技能关联数据
INSERT INTO `worker_skills_new` (
    `worker_id`, `skill_id`, `skill_level`, `experience_years`, 
    `hourly_rate`, `auth_status`, `create_time`, `update_time`
)
SELECT 
    wp.id as worker_id,
    CASE 
        WHEN JSON_CONTAINS(ws.skill_tags, '"清洁"') THEN (SELECT id FROM skills_new WHERE skill_code = 'daily_cleaning')
        WHEN JSON_CONTAINS(ws.skill_tags, '"维修"') THEN (SELECT id FROM skills_new WHERE skill_code = 'ac_repair')
        WHEN JSON_CONTAINS(ws.skill_tags, '"保姆"') THEN (SELECT id FROM skills_new WHERE skill_code = 'daily_cleaning')
        ELSE (SELECT id FROM skills_new WHERE skill_code = 'daily_cleaning')
    END as skill_id,
    CASE 
        WHEN ws.experience_years >= 5 THEN 'expert'
        WHEN ws.experience_years >= 3 THEN 'advanced'
        WHEN ws.experience_years >= 1 THEN 'intermediate'
        ELSE 'beginner'
    END as skill_level,
    COALESCE(ws.experience_years, 0) as experience_years,
    50.00 as hourly_rate, -- 默认时薪
    'approved' as auth_status,
    @current_time as create_time,
    @current_time as update_time
FROM `worker_profiles` wp
INNER JOIN `user` u ON wp.user_id = u.uid
LEFT JOIN `worker_skills` ws ON u.uid = ws.worker_id
WHERE ws.worker_id IS NOT NULL;

-- ==================== 第七步：更新现有任务表 ====================

-- 7.1 为现有任务添加新字段的默认值
UPDATE `tasks` SET 
    `required_skills` = '[]',
    `difficulty_level` = 1,
    `estimated_duration` = 2,
    `worker_requirements` = '{}'
WHERE `required_skills` IS NULL;

-- ==================== 第八步：创建认证记录 ====================

-- 8.1 为已认证的师傅创建认证记录
INSERT INTO `worker_auth_records` (
    `worker_id`, `auth_type`, `auth_data`, `status`, 
    `submit_time`, `review_time`, `create_time`
)
SELECT 
    wp.id as worker_id,
    'identity' as auth_type,
    JSON_OBJECT(
        'real_name', wp.real_name,
        'id_card', wp.id_card,
        'phone', wp.phone
    ) as auth_data,
    wp.auth_status as status,
    wp.create_time as submit_time,
    wp.auth_time as review_time,
    @current_time as create_time
FROM `worker_profiles` wp
WHERE wp.auth_status IN ('approved', 'rejected');

-- ==================== 第九步：数据完整性检查 ====================

-- 9.1 检查数据迁移结果
SELECT 
    '用户角色关联' as table_name,
    COUNT(*) as record_count
FROM user_role_relations
UNION ALL
SELECT 
    '师傅档案' as table_name,
    COUNT(*) as record_count
FROM worker_profiles
UNION ALL
SELECT 
    '技能分类' as table_name,
    COUNT(*) as record_count
FROM skill_categories
UNION ALL
SELECT 
    '技能数据' as table_name,
    COUNT(*) as record_count
FROM skills_new
UNION ALL
SELECT 
    '师傅技能关联' as table_name,
    COUNT(*) as record_count
FROM worker_skills_new
UNION ALL
SELECT 
    '认证记录' as table_name,
    COUNT(*) as record_count
FROM worker_auth_records;

-- ==================== 第十步：创建索引优化 ====================

-- 10.1 为新表创建必要的索引
ALTER TABLE `worker_profiles` ADD INDEX `idx_auth_status` (`auth_status`);
ALTER TABLE `worker_profiles` ADD INDEX `idx_work_status` (`work_status`);
ALTER TABLE `worker_profiles` ADD INDEX `idx_rating` (`rating`);

ALTER TABLE `worker_skills_new` ADD INDEX `idx_auth_status` (`auth_status`);
ALTER TABLE `worker_skills_new` ADD INDEX `idx_skill_level` (`skill_level`);

ALTER TABLE `worker_auth_records` ADD INDEX `idx_auth_type_status` (`auth_type`, `status`);
ALTER TABLE `worker_auth_records` ADD INDEX `idx_submit_time` (`submit_time`);

-- 完成数据迁移
SELECT 'Database migration completed successfully!' as message;
