<?php

namespace app\jobs\work;


use app\services\work\WorkClientServices;
use crmeb\basic\BaseJobs;
use crmeb\traits\QueueTrait;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

/**
 * 同步客户
 * Class WorkClientJob
 * @package app\jobs\work
 */
class WorkClientJob extends BaseJobs
{

    use QueueTrait;

    /**
     * @param $page
     * @param $cursor
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function authClient($page, $cursor)
    {
        /** @var WorkClientServices $make */
        $make = app()->make(WorkClientServices::class);
        $make->authGetExternalcontact($page, $cursor);
        return true;
    }

    /**
     * 同步客户信息
     * @param $corpId
     * @param $externalUserID
     * @param $userId
     * @return bool
     */
    public function saveClientInfo($corpId, $externalUserID, $userId)
    {
        /** @var WorkClientServices $make */
        $make = app()->make(WorkClientServices::class);
        $make->saveOrUpdateClient($corpId, $externalUserID, $userId);
        return true;
    }

    /**
     * 设置客户标签
     * @param $markTag
     * @return bool
     */
    public function setLabel($markTag)
    {
        /** @var WorkClientServices $make */
        $make = app()->make(WorkClientServices::class);
        $make->setClientMarkTag($markTag);
        return true;
    }
}
