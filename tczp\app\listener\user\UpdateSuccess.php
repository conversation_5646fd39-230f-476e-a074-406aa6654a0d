<?php

namespace app\listener\user;


use app\services\user\UserServices;
use crmeb\interfaces\ListenerInterface;

/**
 * Class UpdateSuccess
 * @package app\listener\user
 */
class UpdateSuccess implements ListenerInterface
{

    /**
     * @param $event
     * @throws \Psr\SimpleCache\InvalidArgumentException
     */
    public function handle($event): void
    {
        /** @var UserServices $service */
        $service = app()->make(UserServices::class);
        $service->cacheTag()->clear();
    }
}
