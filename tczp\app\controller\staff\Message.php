<?php
// +----------------------------------------------------------------------
// | 同城用工系统 - 师傅端消息管理
// +----------------------------------------------------------------------

namespace app\controller\staff;

use app\controller\BaseAuth;
use think\facade\Db;

/**
 * 消息管理控制器
 */
class Message extends BaseAuth
{
    /**
     * 获取消息列表
     */
    public function getMessageList()
    {
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 20);
        $type = $this->request->get('type', '');

        try {
            $userId = $this->getUserId();
            $where = [['user_id', '=', $userId]];

            if ($type) {
                $where[] = ['type', '=', $type];
            }

            $list = Db::name('system_messages')
                ->where($where)
                ->order('create_time desc')
                ->page($page, $limit)
                ->select()
                ->toArray();

            $total = Db::name('system_messages')
                ->where($where)
                ->count();

            return $this->success([
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ]);
        } catch (\Exception $e) {
            return $this->fail('获取消息列表失败：' . $e->getMessage());
        }
    }

    /**
     * 标记消息已读
     */
    public function readMessage()
    {
        $messageId = $this->request->param('id', 0);

        if (!$messageId) {
            return $this->fail('消息ID不能为空');
        }

        try {
            $userId = $this->getUserId();

            // 检查消息是否属于当前用户
            $message = Db::name('system_messages')
                ->where([
                    'id' => $messageId,
                    'user_id' => $userId
                ])
                ->find();

            if (!$message) {
                return $this->fail('消息不存在');
            }

            if ($message['is_read'] == 1) {
                return $this->success([], '消息已读');
            }

            // 标记已读
            Db::name('system_messages')
                ->where('id', $messageId)
                ->update([
                    'is_read' => 1,
                    'read_time' => time()
                ]);

            return $this->success([], '消息已标记为已读');
        } catch (\Exception $e) {
            return $this->fail('标记消息已读失败：' . $e->getMessage());
        }
    }

    /**
     * 获取未读消息数量
     */
    public function getUnreadCount()
    {
        try {
            $userId = $this->getUserId();

            $count = Db::name('system_messages')
                ->where([
                    'user_id' => $userId,
                    'is_read' => 0
                ])
                ->count();

            // 按类型统计
            $typeCount = Db::name('system_messages')
                ->where([
                    'user_id' => $userId,
                    'is_read' => 0
                ])
                ->field('type, COUNT(*) as count')
                ->group('type')
                ->select()
                ->toArray();

            $result = [
                'total' => $count,
                'by_type' => []
            ];

            foreach ($typeCount as $item) {
                $result['by_type'][$item['type']] = $item['count'];
            }

            return $this->success($result);
        } catch (\Exception $e) {
            return $this->fail('获取未读消息数量失败：' . $e->getMessage());
        }
    }
} 