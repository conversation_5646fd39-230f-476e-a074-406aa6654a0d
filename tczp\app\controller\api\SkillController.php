<?php
declare (strict_types = 1);

namespace app\controller\api;

use app\Request;
use app\services\worker\WorkerSkillServices;
use think\annotation\Inject;

/**
 * 技能管理控制器
 * Class SkillController
 * @package app\controller\api\v1
 */
class SkillController
{
    /**
     * @var WorkerSkillServices
     */
    #[Inject]
    protected WorkerSkillServices $services;

    /**
     * 获取技能分类列表
     * @param Request $request
     * @return mixed
     */
    public function getSkillCategories(Request $request)
    {
        try {
            $result = $this->services->getSkillCategories();
            return app('json')->success($result);
        } catch (\Exception $e) {
            return app('json')->fail('获取技能分类失败：' . $e->getMessage());
        }
    }

    /**
     * 获取技能标签列表
     * @param Request $request
     * @return mixed
     */
    public function getSkillTags(Request $request)
    {
        $category_id = $request->get('category_id', 0);

        try {
            $result = $this->services->getSkillTags($category_id);
            return app('json')->success($result);
        } catch (\Exception $e) {
            return app('json')->fail('获取技能标签失败：' . $e->getMessage());
        }
    }

    /**
     * 获取师傅技能信息
     * @param Request $request
     * @return mixed
     */
    public function getWorkerSkills(Request $request)
    {
        $uid = $request->uid();

        try {
            $result = $this->services->getWorkerSkills($uid);
            return app('json')->success($result);
        } catch (\Exception $e) {
            return app('json')->fail('获取师傅技能失败：' . $e->getMessage());
        }
    }

    /**
     * 更新师傅技能
     * @param Request $request
     * @return mixed
     */
    public function updateWorkerSkills(Request $request)
    {
        $uid = $request->uid();
        [$skill_tags, $service_areas, $experience_years, $description] = $request->postMore([
            ['skill_tags', []],
            ['service_areas', []],
            ['experience_years', 0],
            ['description', '']
        ], true);

        try {
            $result = $this->services->updateWorkerSkills($uid, $skill_tags, $service_areas, $experience_years, $description);

            if ($result) {
                return app('json')->success('技能信息更新成功');
            } else {
                return app('json')->fail('技能信息更新失败');
            }
        } catch (\Exception $e) {
            return app('json')->fail('技能信息更新失败：' . $e->getMessage());
        }
    }

    /**
     * 获取师傅工作经历
     * @param Request $request
     * @return mixed
     */
    public function getWorkerExperience(Request $request)
    {
        $uid = $request->uid();

        try {
            $result = $this->services->getWorkerExperience($uid);
            return app('json')->success($result);
        } catch (\Exception $e) {
            return app('json')->fail('获取工作经历失败：' . $e->getMessage());
        }
    }

    /**
     * 添加工作经历
     * @param Request $request
     * @return mixed
     */
    public function addWorkerExperience(Request $request)
    {
        $uid = $request->uid();
        [$company, $position, $start_time, $end_time, $description, $images] = $request->postMore([
            ['company', ''],
            ['position', ''],
            ['start_time', ''],
            ['end_time', ''],
            ['description', ''],
            ['images', []]
        ], true);

        if (!$company || !$position || !$start_time) {
            return app('json')->fail('公司名称、职位和开始时间不能为空');
        }

        try {
            $result = $this->services->addWorkerExperience($uid, $company, $position, $start_time, $end_time, $description, $images);

            if ($result) {
                return app('json')->success('工作经历添加成功');
            } else {
                return app('json')->fail('工作经历添加失败');
            }
        } catch (\Exception $e) {
            return app('json')->fail('工作经历添加失败：' . $e->getMessage());
        }
    }

    /**
     * 更新工作经历
     * @param Request $request
     * @return mixed
     */
    public function updateWorkerExperience(Request $request)
    {
        $uid = $request->uid();
        [$id, $company, $position, $start_time, $end_time, $description, $images] = $request->postMore([
            ['id', 0],
            ['company', ''],
            ['position', ''],
            ['start_time', ''],
            ['end_time', ''],
            ['description', ''],
            ['images', []]
        ], true);

        if (!$id) {
            return app('json')->fail('经历ID不能为空');
        }

        try {
            $result = $this->services->updateWorkerExperience($uid, $id, $company, $position, $start_time, $end_time, $description, $images);

            if ($result) {
                return app('json')->success('工作经历更新成功');
            } else {
                return app('json')->fail('工作经历更新失败');
            }
        } catch (\Exception $e) {
            return app('json')->fail('工作经历更新失败：' . $e->getMessage());
        }
    }

    /**
     * 删除工作经历
     * @param Request $request
     * @return mixed
     */
    public function deleteWorkerExperience(Request $request)
    {
        $uid = $request->uid();
        $id = $request->post('id', 0);

        if (!$id) {
            return app('json')->fail('经历ID不能为空');
        }

        try {
            $result = $this->services->deleteWorkerExperience($uid, $id);

            if ($result) {
                return app('json')->success('工作经历删除成功');
            } else {
                return app('json')->fail('工作经历删除失败');
            }
        } catch (\Exception $e) {
            return app('json')->fail('工作经历删除失败：' . $e->getMessage());
        }
    }
}