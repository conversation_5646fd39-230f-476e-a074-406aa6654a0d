<?php

namespace app\jobs\user;


use app\services\user\UserServices;
use crmeb\basic\BaseJobs;
use crmeb\traits\QueueTrait;
use think\facade\Log;

/**
 * 清除到期svip
 * Class UserSvipJob
 * @package app\jobs\user
 */
class UserSvipJob extends BaseJobs
{
    use QueueTrait;

    /**
     * 执行清除到期积分
     * @param $openids
     * @return bool
     */
    public function doJob($uids)
    {
        if (!$uids || !is_array($uids)) {
            return true;
        }
        try {
            /** @var UserServices $userServices */
            $userServices = app()->make(UserServices::class);
            foreach ($uids as $uid) {
                $userServices->offMemberLevel($uid);
            }
        } catch (\Throwable $e) {

            response_log_write([
                'message' => '清除到期用户svip失败,失败原因:' . $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
        return true;
    }
}
