<?php

namespace app\listener\user;

use app\jobs\system\CapitalFlowJob;
use crmeb\interfaces\ListenerInterface;

/**
 * 用户充值退款事件
 * Class RechargeRefund
 * @package app\listener\user
 */
class RechargeRefund implements ListenerInterface
{
    /**
     * 用户充值事件
     * @param $event
     */
    public function handle($event): void
    {
        [$order, $data] = $event;

        //记录资金流水
        CapitalFlowJob::dispatch([$order, 'refund_recharge']);

        //提醒推送
        event('notice.notice', [['user_type' => strtolower($order['recharge_type']), 'data' => $data, 'UserRecharge' => $order], 'recharge_order_refund_status']);

    }
}
