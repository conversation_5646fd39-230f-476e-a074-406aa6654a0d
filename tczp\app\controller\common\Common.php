<?php
declare(strict_types=1);

namespace app\controller\common;

use app\controller\BaseControllerOptimized;
use app\services\common\CommonServices;
use app\services\common\SmsServices;
use app\services\common\UploadServices;
use think\Response;

/**
 * 公共接口控制器 - 完整版
 * Class Common
 * @package app\controller\common
 */
class Common extends BaseControllerOptimized
{
    /**
     * 公共服务
     * @var CommonServices
     */
    protected $commonService;
    
    /**
     * 短信服务
     * @var SmsServices
     */
    protected $smsService;
    
    /**
     * 上传服务
     * @var UploadServices
     */
    protected $uploadService;
    
    /**
     * 初始化
     */
    protected function initialize(): void
    {
        parent::initialize();
        $this->commonService = app()->make(CommonServices::class);
        $this->smsService = app()->make(SmsServices::class);
        $this->uploadService = app()->make(UploadServices::class);
    }

    /**
     * 获取系统配置
     * @return Response
     */
    public function getConfig(): Response
    {
        try {
            $config = $this->commonService->getSystemConfig();
            return $this->success($config);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取城市列表
     * @return Response
     */
    public function getCityList(): Response
    {
        try {
            $parentId = (int)$this->getParam('parent_id', 0);
            $level = (int)$this->getParam('level', 1);
            
            $cities = $this->commonService->getCityList($parentId, $level);
            return $this->success($cities);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取任务分类
     * @return Response
     */
    public function getTaskCategories(): Response
    {
        try {
            $parentId = (int)$this->getParam('parent_id', 0);
            $categories = $this->commonService->getTaskCategories($parentId);
            return $this->success($categories);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取技能分类
     * @return Response
     */
    public function getSkillCategories(): Response
    {
        try {
            $parentId = (int)$this->getParam('parent_id', 0);
            $categories = $this->commonService->getSkillCategories($parentId);
            return $this->success($categories);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 发送短信验证码
     * @return Response
     */
    public function sendSmsCode(): Response
    {
        try {
            $data = $this->getPost();
            
            // 验证必填字段
            $this->validate($data, [
                'phone' => 'require|mobile',
                'type' => 'require|in:register,login,reset_password,bind_phone'
            ]);
            
            // 检查发送频率
            if (!$this->smsService->checkSendFrequency($data['phone'], $data['type'])) {
                return $this->fail('发送过于频繁，请稍后再试');
            }
            
            $result = $this->smsService->sendVerifyCode($data['phone'], $data['type']);
            
            if ($result) {
                return $this->success([], '验证码发送成功');
            } else {
                return $this->fail('验证码发送失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 验证短信验证码
     * @return Response
     */
    public function verifySmsCode(): Response
    {
        try {
            $data = $this->getPost();
            
            // 验证必填字段
            $this->validate($data, [
                'phone' => 'require|mobile',
                'code' => 'require|length:6',
                'type' => 'require'
            ]);
            
            $result = $this->smsService->verifyCode($data['phone'], $data['code'], $data['type']);
            
            if ($result) {
                return $this->success([], '验证成功');
            } else {
                return $this->fail('验证码错误或已过期');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 上传图片
     * @return Response
     */
    public function uploadImage(): Response
    {
        try {
            $file = $this->request->file('file');
            
            if (!$file) {
                return $this->fail('请选择要上传的文件');
            }
            
            $result = $this->uploadService->uploadImage($file);
            
            if ($result) {
                return $this->success($result, '上传成功');
            } else {
                return $this->fail('上传失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 上传视频
     * @return Response
     */
    public function uploadVideo(): Response
    {
        try {
            $file = $this->request->file('file');
            
            if (!$file) {
                return $this->fail('请选择要上传的文件');
            }
            
            $result = $this->uploadService->uploadVideo($file);
            
            if ($result) {
                return $this->success($result, '上传成功');
            } else {
                return $this->fail('上传失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 上传文件
     * @return Response
     */
    public function uploadFile(): Response
    {
        try {
            $file = $this->request->file('file');
            $type = $this->getPost('type', 'document');
            
            if (!$file) {
                return $this->fail('请选择要上传的文件');
            }
            
            $result = $this->uploadService->uploadFile($file, $type);
            
            if ($result) {
                return $this->success($result, '上传成功');
            } else {
                return $this->fail('上传失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取地理位置信息
     * @return Response
     */
    public function getLocationInfo(): Response
    {
        try {
            $data = $this->getPost();
            
            // 验证必填字段
            $this->validate($data, [
                'latitude' => 'require|float',
                'longitude' => 'require|float'
            ]);
            
            $locationInfo = $this->commonService->getLocationInfo($data['latitude'], $data['longitude']);
            return $this->success($locationInfo);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 地址解析（地址转坐标）
     * @return Response
     */
    public function geocode(): Response
    {
        try {
            $address = $this->getPost('address', '');
            
            if (!$address) {
                return $this->fail('地址不能为空');
            }
            
            $result = $this->commonService->geocode($address);
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 逆地址解析（坐标转地址）
     * @return Response
     */
    public function reverseGeocode(): Response
    {
        try {
            $data = $this->getPost();
            
            // 验证必填字段
            $this->validate($data, [
                'latitude' => 'require|float',
                'longitude' => 'require|float'
            ]);
            
            $result = $this->commonService->reverseGeocode($data['latitude'], $data['longitude']);
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取二维码
     * @return Response
     */
    public function generateQrcode(): Response
    {
        try {
            $data = $this->getPost();
            
            // 验证必填字段
            $this->validate($data, [
                'content' => 'require',
                'size' => 'number|between:100,500'
            ]);
            
            $size = $data['size'] ?? 200;
            $result = $this->commonService->generateQrcode($data['content'], $size);
            
            if ($result) {
                return $this->success($result, '二维码生成成功');
            } else {
                return $this->fail('二维码生成失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取版本信息
     * @return Response
     */
    public function getVersionInfo(): Response
    {
        try {
            $platform = $this->getParam('platform', 'app');
            $versionInfo = $this->commonService->getVersionInfo($platform);
            return $this->success($versionInfo);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 检查版本更新
     * @return Response
     */
    public function checkVersion(): Response
    {
        try {
            $data = $this->getPost();
            
            // 验证必填字段
            $this->validate($data, [
                'platform' => 'require|in:android,ios,web',
                'version' => 'require'
            ]);
            
            $updateInfo = $this->commonService->checkVersion($data['platform'], $data['version']);
            return $this->success($updateInfo);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取帮助文档
     * @return Response
     */
    public function getHelpDocs(): Response
    {
        try {
            $categoryId = (int)$this->getParam('category_id', 0);
            $keyword = $this->getParam('keyword', '');
            
            $docs = $this->commonService->getHelpDocs($categoryId, $keyword);
            return $this->success($docs);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取常见问题
     * @return Response
     */
    public function getFaq(): Response
    {
        try {
            $categoryId = (int)$this->getParam('category_id', 0);
            $faq = $this->commonService->getFaq($categoryId);
            return $this->success($faq);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 提交意见反馈
     * @return Response
     */
    public function submitFeedback(): Response
    {
        try {
            $data = $this->getPost();
            
            // 验证必填字段
            $this->validate($data, [
                'type' => 'require|in:bug,suggestion,complaint,other',
                'content' => 'require|max:500',
                'contact' => 'max:100'
            ]);
            
            $data['user_id'] = $this->uid;
            $data['ip'] = $this->getClientIp();
            $data['user_agent'] = $this->getUserAgent();
            
            $result = $this->commonService->submitFeedback($data);
            
            if ($result) {
                return $this->success([], '反馈提交成功');
            } else {
                return $this->fail('提交失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取协议内容
     * @return Response
     */
    public function getAgreement(): Response
    {
        try {
            $type = $this->getParam('type', 'user');
            $agreement = $this->commonService->getAgreement($type);
            return $this->success($agreement);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取隐私政策
     * @return Response
     */
    public function getPrivacyPolicy(): Response
    {
        try {
            $policy = $this->commonService->getPrivacyPolicy();
            return $this->success($policy);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}
