<?php
declare(strict_types=1);

namespace app\http\middleware\staff;

use app\Request;
use app\services\staff\StaffAuthServices;
use crmeb\interfaces\MiddlewareInterface;
use think\Response;

/**
 * 师傅端权限控制中间件
 * Class StaffPermissionMiddleware
 * @package app\http\middleware\staff
 */
class StaffPermissionMiddleware implements MiddlewareInterface
{
    /**
     * 处理请求
     * @param Request $request
     * @param \Closure $next
     * @param string $permission 需要的权限
     * @return Response
     */
    public function handle(Request $request, \Closure $next, string $permission = '')
    {
        try {
            // 获取用户ID
            $uid = $request->uid ?? 0;
            
            if (!$uid) {
                return $this->forbiddenResponse('用户未登录');
            }
            
            // 如果没有指定权限，直接通过
            if (!$permission) {
                return $next($request);
            }
            
            // 检查权限
            if (!$this->checkPermission($uid, $permission)) {
                return $this->forbiddenResponse('权限不足');
            }
            
            return $next($request);
            
        } catch (\Exception $e) {
            return $this->forbiddenResponse($e->getMessage());
        }
    }
    
    /**
     * 检查权限
     * @param int $uid
     * @param string $permission
     * @return bool
     */
    private function checkPermission(int $uid, string $permission): bool
    {
        /** @var StaffAuthServices $staffAuthService */
        $staffAuthService = app()->make(StaffAuthServices::class);
        
        // 获取师傅权限
        $permissions = $staffAuthService->getWorkerPermissions($uid);
        
        // 检查是否有该权限
        return in_array($permission, $permissions) || in_array('*', $permissions);
    }
    
    /**
     * 返回权限不足响应
     * @param string $message
     * @return Response
     */
    private function forbiddenResponse(string $message): Response
    {
        return app('json')->make(403, $message);
    }
}
