<?php

namespace app\model\message\service;


use app\model\user\User;
use app\model\work\WorkMember;
use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
use think\Model;

/**
 * 客服
 * Class StoreService
 * @package app\model\message\service
 */
class StoreService extends BaseModel
{
    use ModelTrait;

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'store_service';

    /**
     * @var bool
     */
    protected $updateTime = false;

    /**
     * @return \think\model\relation\HasOne
     */
    public function workMember()
    {
        return $this->hasOne(WorkMember::class, 'uid', 'uid');
    }

    protected function getAddTimeAttr($value)
    {
        if ($value) return date('Y-m-d H:i:s', $value);
        return $value;
    }

    /**
     * 用户名一对多关联
     * @return mixed
     */
    public function user()
    {
        return $this->hasOne(User::class, 'uid', 'uid')->field(['uid', 'nickname'])->bind([
            'nickname' => 'nickname'
        ]);
    }

    /**
     * uid搜索器
     * @param Model $query
     * @param $value
     */
    public function searchUidAttr($query, $value)
    {
        $query->where('uid', $value);
    }

    /**
     * status搜索器
     * @param Model $query
     * @param $value
     */
    public function searchStatusAttr($query, $value)
    {
        $query->where('status', $value);
    }

    /**
     * status搜索器
     * @param Model $query
     * @param $value
     */
    public function searchAccountStatusAttr($query, $value)
    {
        $query->where('account_status', $value);
    }

    /**
     * account搜索器
     * @param Model $query
     * @param $value
     */
    public function searchAccountAttr($query, $value)
    {
        $query->where('account', $value);
    }

    /**
     * phone搜索器
     * @param Model $query
     * @param $value
     */
    public function searchPhoneAttr($query, $value)
    {
        $query->where('phone', $value);
    }

    /**
     * customer
     * @param Model $query
     * @param $value
     */
    public function searchCustomerAttr($query, $value)
    {
        $query->where('customer', $value);
    }

    /**
     * 用户昵称搜索器
     * @param Model $query
     * @param $value
     */
    public function searchNicknameAttr($query, $value)
    {
        if ($value) $query->whereLike('nickname', '%' . $value . '%');
    }

    /**
     * @param Model $query
     * @param $value
     */
    public function searchOnlineAttr($query, $value)
    {
        $query->where('online', $value);
    }

    /**
     * 用户uid搜索器
     * @param Model $query
     * @param $value
     */
    public function searchNoUidAttr($query, $value)
    {
        if ($value) $query->whereNotIn('uid', $value);
    }
}
