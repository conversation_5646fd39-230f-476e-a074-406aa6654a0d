/*
Navicat MySQL Data Transfer

Source Server         : ************_tczp
Source Server Version : 50740
Source Host           : ************:3306
Source Database       : tczp

Target Server Type    : MYSQL
Target Server Version : 50740
File Encoding         : 65001

Date: 2025-07-09 17:36:12
*/

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for agreement
-- ----------------------------
DROP TABLE IF EXISTS `agreement`;
CREATE TABLE `agreement` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '协议类型  1：会员协议',
  `title` varchar(200) NOT NULL DEFAULT '' COMMENT '协议名称',
  `content` text COMMENT '协议内容',
  `sort` int(10) NOT NULL DEFAULT '0' COMMENT '排序倒序',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '1：显示：0：不显示',
  `add_time` int(50) NOT NULL DEFAULT '0' COMMENT '添加时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `type` (`type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='会员协议';

-- ----------------------------
-- Table structure for ai_recommendations
-- ----------------------------
DROP TABLE IF EXISTS `ai_recommendations`;
CREATE TABLE `ai_recommendations` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `recommendation_type` varchar(30) NOT NULL COMMENT '推荐类型',
  `target_id` int(11) unsigned NOT NULL COMMENT '推荐目标ID',
  `score` decimal(5,4) NOT NULL COMMENT '推荐得分',
  `reasons` text COMMENT '推荐原因(JSON)',
  `algorithm_version` varchar(20) DEFAULT 'v1.0' COMMENT '算法版本',
  `is_accepted` tinyint(1) DEFAULT NULL COMMENT '是否被接受',
  `feedback_score` tinyint(3) DEFAULT NULL COMMENT '用户反馈评分',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `accepted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_recommendation_type` (`recommendation_type`),
  KEY `idx_score` (`score`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='智能推荐记录表';

-- ----------------------------
-- Table structure for ai_risk_assessments
-- ----------------------------
DROP TABLE IF EXISTS `ai_risk_assessments`;
CREATE TABLE `ai_risk_assessments` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `task_id` int(11) unsigned NOT NULL COMMENT '任务ID',
  `worker_id` int(11) unsigned DEFAULT NULL COMMENT '师傅ID',
  `employer_id` int(11) unsigned DEFAULT NULL COMMENT '雇主ID',
  `risk_scores` text COMMENT '风险得分详情(JSON)',
  `overall_risk` decimal(5,4) NOT NULL COMMENT '综合风险得分',
  `risk_level` varchar(20) NOT NULL COMMENT '风险等级',
  `pricing_adjustment` text COMMENT '定价调整建议(JSON)',
  `ml_model_version` varchar(20) DEFAULT 'v1.0' COMMENT '机器学习模型版本',
  `confidence_score` decimal(5,4) DEFAULT '0.8500' COMMENT '置信度得分',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_risk_level` (`risk_level`),
  KEY `idx_overall_risk` (`overall_risk`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI风险评估记录表';

-- ----------------------------
-- Table structure for article
-- ----------------------------
DROP TABLE IF EXISTS `article`;
CREATE TABLE `article` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '文章管理ID',
  `cid` varchar(255) NOT NULL DEFAULT '1' COMMENT '分类id',
  `title` varchar(255) NOT NULL DEFAULT '' COMMENT '文章标题',
  `author` varchar(255) NOT NULL DEFAULT '' COMMENT '文章作者',
  `image_input` varchar(255) NOT NULL DEFAULT '' COMMENT '文章图片',
  `synopsis` varchar(255) NOT NULL DEFAULT '' COMMENT '文章简介',
  `share_title` varchar(255) NOT NULL DEFAULT '' COMMENT '文章分享标题',
  `share_synopsis` varchar(255) NOT NULL DEFAULT '' COMMENT '文章分享简介',
  `visit` varchar(255) NOT NULL DEFAULT '0' COMMENT '浏览次数',
  `likes` int(10) NOT NULL DEFAULT '0' COMMENT '点赞量',
  `sort` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '排序',
  `url` varchar(255) NOT NULL DEFAULT '' COMMENT '原文链接',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态',
  `add_time` varchar(255) NOT NULL DEFAULT '' COMMENT '添加时间',
  `hide` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否隐藏',
  `admin_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '管理员id',
  `mer_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '商户id',
  `product_id` int(10) NOT NULL DEFAULT '0' COMMENT '商品关联id',
  `is_hot` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否热门(小程序)',
  `is_banner` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否轮播图(小程序)',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章管理表';

-- ----------------------------
-- Table structure for article_category
-- ----------------------------
DROP TABLE IF EXISTS `article_category`;
CREATE TABLE `article_category` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '文章分类id',
  `pid` int(11) NOT NULL DEFAULT '0' COMMENT '父级ID',
  `title` varchar(255) NOT NULL DEFAULT '' COMMENT '文章分类标题',
  `intr` varchar(255) NOT NULL DEFAULT '' COMMENT '文章分类简介',
  `image` varchar(255) NOT NULL DEFAULT '' COMMENT '文章分类图片',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态',
  `sort` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '排序',
  `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '1删除0未删除',
  `add_time` varchar(255) NOT NULL DEFAULT '0' COMMENT '添加时间',
  `hidden` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否隐藏',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章分类表';

-- ----------------------------
-- Table structure for article_content
-- ----------------------------
DROP TABLE IF EXISTS `article_content`;
CREATE TABLE `article_content` (
  `nid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '文章id',
  `content` text COMMENT '文章内容',
  UNIQUE KEY `nid` (`nid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章内容表';

-- ----------------------------
-- Table structure for blockchain_records
-- ----------------------------
DROP TABLE IF EXISTS `blockchain_records`;
CREATE TABLE `blockchain_records` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `record_type` varchar(30) NOT NULL COMMENT '记录类型',
  `reference_id` varchar(50) NOT NULL COMMENT '关联ID',
  `transaction_hash` varchar(100) NOT NULL COMMENT '交易哈希',
  `contract_address` varchar(50) DEFAULT '' COMMENT '合约地址',
  `data_hash` varchar(100) NOT NULL COMMENT '数据哈希',
  `block_data` longtext COMMENT '区块数据(JSON)',
  `block_number` int(11) unsigned DEFAULT NULL COMMENT '区块号',
  `confirmations` int(11) DEFAULT '0' COMMENT '确认数',
  `gas_used` int(11) DEFAULT '0' COMMENT '消耗Gas',
  `status` varchar(20) DEFAULT 'pending' COMMENT '状态',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `confirmed_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `transaction_hash` (`transaction_hash`),
  KEY `idx_record_type` (`record_type`),
  KEY `idx_reference_id` (`reference_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='区块链记录表';

-- ----------------------------
-- Table structure for cache
-- ----------------------------
DROP TABLE IF EXISTS `cache`;
CREATE TABLE `cache` (
  `key` varchar(32) NOT NULL DEFAULT '',
  `result` longtext COMMENT '缓存数据',
  `expire_time` int(11) NOT NULL DEFAULT '0' COMMENT '失效时间0=永久',
  `add_time` int(10) NOT NULL DEFAULT '0' COMMENT '缓存时间',
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信缓存表';

-- ----------------------------
-- Table structure for capital_flow
-- ----------------------------
DROP TABLE IF EXISTS `capital_flow`;
CREATE TABLE `capital_flow` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `flow_id` varchar(32) NOT NULL DEFAULT '' COMMENT '流水id',
  `order_id` varchar(50) NOT NULL DEFAULT '' COMMENT '关联id',
  `store_id` int(11) NOT NULL DEFAULT '0' COMMENT '门店id',
  `uid` int(11) NOT NULL DEFAULT '0' COMMENT '用户id',
  `nickname` varchar(255) NOT NULL DEFAULT '' COMMENT '昵称',
  `phone` varchar(20) NOT NULL DEFAULT '' COMMENT '电话',
  `price` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '交易金额',
  `trading_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '交易类型',
  `pay_type` varchar(32) NOT NULL DEFAULT '' COMMENT '支付类型',
  `mark` varchar(500) NOT NULL DEFAULT '' COMMENT '备注',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '交易时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资金流水表';

-- ----------------------------
-- Table structure for category
-- ----------------------------
DROP TABLE IF EXISTS `category`;
CREATE TABLE `category` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `pid` int(10) NOT NULL DEFAULT '0' COMMENT '上级id',
  `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '类型：0平台2:供应商',
  `relation_id` int(11) NOT NULL DEFAULT '0' COMMENT '门店、供应商id',
  `owner_id` int(10) NOT NULL DEFAULT '0' COMMENT '所属人，为全部',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '分类名称',
  `sort` int(10) NOT NULL DEFAULT '0' COMMENT '排序',
  `group` tinyint(1) NOT NULL DEFAULT '0' COMMENT '分类类型0=标签分类，1=快捷短语分类,2=商品标签分类，3=商品参数模版,4=企业渠道码',
  `other` text COLLATE utf8mb4_unicode_ci COMMENT '其他参数',
  `is_show` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否显示',
  `add_time` int(10) NOT NULL DEFAULT '0' COMMENT '添加时间',
  `integral_min` int(10) NOT NULL DEFAULT '0' COMMENT '积分分类最低',
  `integral_max` int(10) NOT NULL DEFAULT '0' COMMENT '积分分类最高',
  PRIMARY KEY (`id`),
  KEY `pid` (`pid`) USING BTREE,
  KEY `name` (`name`) USING BTREE,
  KEY `is_be` (`owner_id`,`type`,`id`) USING BTREE,
  KEY `group` (`group`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标签分类';

-- ----------------------------
-- Table structure for city_area
-- ----------------------------
DROP TABLE IF EXISTS `city_area`;
CREATE TABLE `city_area` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `path` varchar(128) NOT NULL DEFAULT '/' COMMENT '省市级别',
  `parent_id` int(11) NOT NULL DEFAULT '0' COMMENT '父级id',
  `type` varchar(32) NOT NULL DEFAULT '' COMMENT '类型',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '名称',
  `level` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '级别',
  `code` varchar(100) NOT NULL DEFAULT '' COMMENT '城市编码',
  `snum` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '子级个数',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `parent_id` (`parent_id`) USING BTREE,
  KEY `path` (`path`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=44708 DEFAULT CHARSET=utf8 COMMENT='城市数据表';

-- ----------------------------
-- Table structure for claim_evidence_files
-- ----------------------------
DROP TABLE IF EXISTS `claim_evidence_files`;
CREATE TABLE `claim_evidence_files` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `claim_id` int(11) unsigned NOT NULL COMMENT '理赔ID',
  `file_type` varchar(20) NOT NULL COMMENT '文件类型',
  `file_name` varchar(200) NOT NULL COMMENT '文件名',
  `file_path` varchar(500) NOT NULL COMMENT '文件路径',
  `file_size` int(11) DEFAULT '0' COMMENT '文件大小',
  `file_description` varchar(200) DEFAULT '' COMMENT '文件描述',
  `uploaded_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_claim_id` (`claim_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='理赔证据文件表';

-- ----------------------------
-- Table structure for claim_processing_logs
-- ----------------------------
DROP TABLE IF EXISTS `claim_processing_logs`;
CREATE TABLE `claim_processing_logs` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `claim_id` int(11) unsigned NOT NULL COMMENT '理赔ID',
  `status` varchar(20) NOT NULL COMMENT '状态',
  `message` text COMMENT '处理信息',
  `operator` varchar(50) DEFAULT 'system' COMMENT '操作人',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_claim_id` (`claim_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='理赔处理日志表';

-- ----------------------------
-- Table structure for community
-- ----------------------------
DROP TABLE IF EXISTS `community`;
CREATE TABLE `community` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '类型：0:平台1:门店2:用户',
  `relation_id` int(10) NOT NULL DEFAULT '0' COMMENT '关联平台管理门店ID、用户UID',
  `content_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '内容类型：1：图文2：视频',
  `title` varchar(255) NOT NULL DEFAULT '' COMMENT '标题',
  `image` varchar(255) NOT NULL DEFAULT '' COMMENT '封面图',
  `video_url` varchar(255) NOT NULL DEFAULT '' COMMENT '视频地址',
  `slider_image` text COMMENT '图集',
  `content` longtext COMMENT '内容详情',
  `topic_id` longtext COMMENT '关联话题ids',
  `product_id` longtext COMMENT '关联商品ids',
  `like_num` int(10) NOT NULL DEFAULT '0' COMMENT '点赞数量',
  `collect_num` int(10) NOT NULL DEFAULT '0' COMMENT '收藏数量',
  `play_num` int(10) NOT NULL DEFAULT '0' COMMENT '浏览播放数量',
  `comment_num` int(10) NOT NULL DEFAULT '0' COMMENT '评论数量',
  `share_num` int(10) NOT NULL DEFAULT '0' COMMENT '分享数量',
  `star` tinyint(1) DEFAULT '1' COMMENT '星级排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否显示',
  `is_recommend` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否推荐',
  `is_verify` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否审核：-2强制下架-1未通过0未审核1:通过',
  `refusal` varchar(255) NOT NULL DEFAULT '' COMMENT '拒绝理由',
  `sort` smallint(5) NOT NULL DEFAULT '0' COMMENT '排序',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `verify_time` int(10) NOT NULL DEFAULT '0' COMMENT '审核时间',
  `add_time` int(10) NOT NULL DEFAULT '0' COMMENT '添加时间',
  PRIMARY KEY (`id`),
  KEY `type` (`type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='社区帖子表';

-- ----------------------------
-- Table structure for community_comment
-- ----------------------------
DROP TABLE IF EXISTS `community_comment`;
CREATE TABLE `community_comment` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '类型：0:平台1:门店2:用户 3虚拟',
  `uid` int(11) NOT NULL DEFAULT '0' COMMENT '关联 id',
  `reply_id` int(11) NOT NULL DEFAULT '0' COMMENT '帖子评论 id',
  `reply_uid` int(11) NOT NULL DEFAULT '0' COMMENT '帖子评论 uid',
  `comment_reply_id` int(11) NOT NULL DEFAULT '0' COMMENT '评论回复id',
  `comment_reply_uid` int(11) NOT NULL DEFAULT '0' COMMENT '评论回复用户uid',
  `community_id` int(11) NOT NULL DEFAULT '0' COMMENT '社区内容id',
  `nickname` varchar(64) NOT NULL DEFAULT '' COMMENT '虚拟名称',
  `avatar` varchar(255) NOT NULL DEFAULT '' COMMENT '虚拟头像',
  `comment_num` int(10) NOT NULL DEFAULT '0' COMMENT '评论数量',
  `like_num` int(11) NOT NULL DEFAULT '0' COMMENT '点赞数量',
  `content` varchar(1000) NOT NULL DEFAULT '' COMMENT '评论内容',
  `ip` varchar(32) NOT NULL DEFAULT '' COMMENT '评论ip',
  `city` varchar(255) NOT NULL DEFAULT '' COMMENT '城市',
  `is_verify` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否审核：-2强制下架-1未通过0未审核1:通过',
  `is_show` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否显示 1 正常 2 下架',
  `is_reply` tinyint(1) NOT NULL DEFAULT '0' COMMENT '评论帖子',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `pid` (`reply_id`) USING BTREE,
  KEY `community_id` (`community_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='社区评价回复表';

-- ----------------------------
-- Table structure for community_record
-- ----------------------------
DROP TABLE IF EXISTS `community_record`;
CREATE TABLE `community_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `uid` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `relation_id` int(11) NOT NULL DEFAULT '0' COMMENT '关联用户ID',
  `type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '记录类型：1-点赞, 2-评论, 3-关注',
  `link_id` int(11) NOT NULL DEFAULT '0' COMMENT '关联帖子ID',
  `comment_id` int(11) NOT NULL DEFAULT '0' COMMENT '关联评论ID',
  `comment_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '评论类型：1-评论, 2-回复',
  `content` varchar(1000) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '内容',
  `is_viewed` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否已查看：0-未查看, 1-已查看',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间(时间戳)',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='社区记录表';

-- ----------------------------
-- Table structure for community_relevance
-- ----------------------------
DROP TABLE IF EXISTS `community_relevance`;
CREATE TABLE `community_relevance` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `left_id` int(11) NOT NULL DEFAULT '0',
  `right_id` int(11) NOT NULL DEFAULT '0',
  `type` varchar(32) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `left_id` (`left_id`) USING BTREE,
  KEY `right_id` (`right_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='社区关联表';

-- ----------------------------
-- Table structure for community_topic
-- ----------------------------
DROP TABLE IF EXISTS `community_topic`;
CREATE TABLE `community_topic` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '话题',
  `icon` varchar(128) NOT NULL DEFAULT '' COMMENT '图标',
  `sort` int(11) unsigned NOT NULL DEFAULT '0',
  `is_recommend` tinyint(11) NOT NULL DEFAULT '0' COMMENT '推荐',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '类型,1 后台,2 用户',
  `use_num` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '使用次数',
  `view_num` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '浏览量',
  `status` tinyint(2) NOT NULL DEFAULT '1' COMMENT '状态',
  `is_del` tinyint(2) NOT NULL DEFAULT '0',
  `add_time` int(10) NOT NULL DEFAULT '0' COMMENT '添加时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='社区话题';

-- ----------------------------
-- Table structure for community_user
-- ----------------------------
DROP TABLE IF EXISTS `community_user`;
CREATE TABLE `community_user` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) NOT NULL DEFAULT '2' COMMENT '类型：0:平台1:门店2:用户',
  `relation_id` int(10) NOT NULL DEFAULT '0' COMMENT '关联平台管理门店ID、用户UID',
  `nickname` varchar(255) NOT NULL DEFAULT '' COMMENT '昵称',
  `avatar` varchar(255) NOT NULL DEFAULT '' COMMENT '图像',
  `desc` varchar(255) NOT NULL DEFAULT '' COMMENT '简介',
  `community_num` int(10) NOT NULL DEFAULT '0' COMMENT '帖子数量',
  `follow_num` int(10) NOT NULL DEFAULT '0' COMMENT '关注数量',
  `fans_num` int(10) NOT NULL DEFAULT '0' COMMENT '粉丝数量',
  `friend_num` int(10) NOT NULL DEFAULT '0' COMMENT '好友数量',
  `like_num` int(10) NOT NULL DEFAULT '0' COMMENT '获得点赞数量',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `add_time` int(10) NOT NULL DEFAULT '0' COMMENT '添加时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='社区用户';

-- ----------------------------
-- Table structure for contract_signatures
-- ----------------------------
DROP TABLE IF EXISTS `contract_signatures`;
CREATE TABLE `contract_signatures` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `contract_id` int(11) unsigned NOT NULL COMMENT '合同ID',
  `signer_id` int(11) unsigned NOT NULL COMMENT '签署人ID',
  `signer_type` varchar(20) NOT NULL COMMENT '签署人类型',
  `signature_method` varchar(20) NOT NULL COMMENT '签署方式',
  `sign_time` timestamp NOT NULL COMMENT '签署时间',
  `sign_location` varchar(200) DEFAULT '' COMMENT '签署地点',
  `device_info` text COMMENT '设备信息',
  `platform_sign_id` varchar(100) DEFAULT '' COMMENT '第三方签署ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_contract_id` (`contract_id`),
  KEY `idx_signer_id` (`signer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合同签署记录表';

-- ----------------------------
-- Table structure for contract_signers
-- ----------------------------
DROP TABLE IF EXISTS `contract_signers`;
CREATE TABLE `contract_signers` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `contract_id` int(11) unsigned NOT NULL COMMENT '合同ID',
  `signer_id` int(11) unsigned NOT NULL COMMENT '签署人ID',
  `signer_type` varchar(20) NOT NULL COMMENT '签署人类型',
  `signer_name` varchar(50) NOT NULL COMMENT '签署人姓名',
  `signer_phone` varchar(20) NOT NULL COMMENT '签署人手机',
  `sign_order` tinyint(3) DEFAULT '1' COMMENT '签署顺序',
  `status` varchar(20) DEFAULT 'pending' COMMENT '签署状态',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_contract_id` (`contract_id`),
  KEY `idx_signer_id` (`signer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合同签署方表';

-- ----------------------------
-- Table structure for cross_border_payments
-- ----------------------------
DROP TABLE IF EXISTS `cross_border_payments`;
CREATE TABLE `cross_border_payments` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `payment_id` varchar(50) NOT NULL COMMENT '支付ID',
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `task_id` int(11) unsigned DEFAULT NULL COMMENT '任务ID',
  `original_amount` decimal(12,2) NOT NULL COMMENT '原始金额',
  `original_currency` varchar(3) NOT NULL COMMENT '原始货币',
  `local_amount` decimal(12,2) NOT NULL COMMENT '本地金额',
  `local_currency` varchar(3) NOT NULL COMMENT '本地货币',
  `exchange_rate` decimal(12,6) NOT NULL COMMENT '汇率',
  `cross_border_fee` decimal(10,2) DEFAULT '0.00' COMMENT '跨境手续费',
  `payment_channel` varchar(30) NOT NULL COMMENT '支付渠道',
  `from_region` varchar(10) NOT NULL COMMENT '源地区',
  `to_region` varchar(10) NOT NULL COMMENT '目标地区',
  `compliance_data` text COMMENT '合规数据(JSON)',
  `status` varchar(20) DEFAULT 'pending' COMMENT '状态',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `completed_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `payment_id` (`payment_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='跨境支付记录表';

-- ----------------------------
-- Table structure for decentralized_identities
-- ----------------------------
DROP TABLE IF EXISTS `decentralized_identities`;
CREATE TABLE `decentralized_identities` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `did` varchar(200) NOT NULL COMMENT '去中心化身份标识',
  `identity_type` varchar(20) NOT NULL COMMENT '身份类型',
  `verification_level` varchar(20) NOT NULL COMMENT '验证级别',
  `documents_hash` varchar(100) NOT NULL COMMENT '文档哈希',
  `blockchain_record_id` int(11) unsigned DEFAULT NULL COMMENT '区块链记录ID',
  `issuer` varchar(100) DEFAULT '' COMMENT '签发机构',
  `issued_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `expires_at` timestamp NULL DEFAULT NULL,
  `status` varchar(20) DEFAULT 'active' COMMENT '状态',
  PRIMARY KEY (`id`),
  UNIQUE KEY `did` (`did`),
  UNIQUE KEY `user_id` (`user_id`),
  KEY `idx_identity_type` (`identity_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='去中心化身份表';

-- ----------------------------
-- Table structure for electronic_contracts
-- ----------------------------
DROP TABLE IF EXISTS `electronic_contracts`;
CREATE TABLE `electronic_contracts` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `contract_number` varchar(50) NOT NULL COMMENT '合同编号',
  `task_id` int(11) unsigned NOT NULL COMMENT '任务ID',
  `employer_id` int(11) unsigned NOT NULL COMMENT '雇主ID',
  `worker_id` int(11) unsigned NOT NULL COMMENT '师傅ID',
  `contract_type` varchar(20) DEFAULT 'standard' COMMENT '合同类型',
  `contract_content` longtext COMMENT '合同内容',
  `platform_name` varchar(20) DEFAULT '' COMMENT '第三方平台',
  `platform_contract_id` varchar(100) DEFAULT '' COMMENT '第三方合同ID',
  `status` varchar(20) DEFAULT 'draft' COMMENT '合同状态',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `completed_at` timestamp NULL DEFAULT NULL,
  `expired_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `contract_number` (`contract_number`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='电子合同表';

-- ----------------------------
-- Table structure for exchange_rates
-- ----------------------------
DROP TABLE IF EXISTS `exchange_rates`;
CREATE TABLE `exchange_rates` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `from_currency` varchar(3) NOT NULL COMMENT '源货币',
  `to_currency` varchar(3) NOT NULL COMMENT '目标货币',
  `rate` decimal(12,6) NOT NULL COMMENT '汇率',
  `rate_date` date NOT NULL COMMENT '汇率日期',
  `data_source` varchar(30) DEFAULT 'api' COMMENT '数据来源',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `currencies_date` (`from_currency`,`to_currency`,`rate_date`),
  KEY `idx_rate_date` (`rate_date`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COMMENT='汇率记录表';

-- ----------------------------
-- Table structure for help_documents
-- ----------------------------
DROP TABLE IF EXISTS `help_documents`;
CREATE TABLE `help_documents` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '文档ID',
  `title` varchar(255) NOT NULL DEFAULT '' COMMENT '文档标题',
  `content` text NOT NULL COMMENT '文档内容',
  `category` varchar(100) NOT NULL DEFAULT '' COMMENT '文档分类',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `views` int(11) NOT NULL DEFAULT '0' COMMENT '浏览次数',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1启用，0禁用',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `category` (`category`),
  KEY `status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COMMENT='帮助文档表';

-- ----------------------------
-- Table structure for i18n_texts
-- ----------------------------
DROP TABLE IF EXISTS `i18n_texts`;
CREATE TABLE `i18n_texts` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `text_key` varchar(100) NOT NULL COMMENT '文本键值',
  `language` varchar(10) NOT NULL COMMENT '语言代码',
  `text_value` text NOT NULL COMMENT '文本内容',
  `category` varchar(30) DEFAULT 'general' COMMENT '分类',
  `is_system` tinyint(1) DEFAULT '0' COMMENT '是否系统文本',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `text_key_language` (`text_key`,`language`),
  KEY `idx_language` (`language`),
  KEY `idx_category` (`category`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COMMENT='多语言文本表';

-- ----------------------------
-- Table structure for insurance_business_stats
-- ----------------------------
DROP TABLE IF EXISTS `insurance_business_stats`;
CREATE TABLE `insurance_business_stats` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `stat_date` date NOT NULL COMMENT '统计日期',
  `total_policies` int(11) DEFAULT '0' COMMENT '总保单数',
  `total_premium` decimal(12,2) DEFAULT '0.00' COMMENT '总保费',
  `total_claims` int(11) DEFAULT '0' COMMENT '总理赔数',
  `total_claim_amount` decimal(12,2) DEFAULT '0.00' COMMENT '总理赔金额',
  `platform_commission` decimal(12,2) DEFAULT '0.00' COMMENT '平台佣金',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `stat_date` (`stat_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='保险业务统计表';

-- ----------------------------
-- Table structure for insurance_claims
-- ----------------------------
DROP TABLE IF EXISTS `insurance_claims`;
CREATE TABLE `insurance_claims` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `claim_number` varchar(50) NOT NULL COMMENT '理赔单号',
  `policy_number` varchar(50) NOT NULL COMMENT '保单号',
  `policy_record_id` int(11) unsigned NOT NULL COMMENT '保险记录ID',
  `claim_type` varchar(30) NOT NULL COMMENT '理赔类型',
  `claim_amount` decimal(10,2) NOT NULL COMMENT '理赔金额',
  `settlement_amount` decimal(10,2) DEFAULT '0.00' COMMENT '结算金额',
  `incident_description` text COMMENT '事故描述',
  `incident_date` datetime NOT NULL COMMENT '事故发生时间',
  `incident_location` varchar(200) DEFAULT '' COMMENT '事故地点',
  `contact_phone` varchar(20) DEFAULT '' COMMENT '联系电话',
  `applicant_id` int(11) unsigned NOT NULL COMMENT '申请人ID',
  `status` varchar(20) DEFAULT 'pending' COMMENT '状态',
  `rejection_reason` text COMMENT '拒绝原因',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `approved_at` timestamp NULL DEFAULT NULL,
  `rejected_at` timestamp NULL DEFAULT NULL,
  `settled_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `claim_number` (`claim_number`),
  KEY `idx_policy_number` (`policy_number`),
  KEY `idx_applicant` (`applicant_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='保险理赔记录表';

-- ----------------------------
-- Table structure for insurance_plans
-- ----------------------------
DROP TABLE IF EXISTS `insurance_plans`;
CREATE TABLE `insurance_plans` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `plan_code` varchar(50) NOT NULL COMMENT '方案代码',
  `plan_name` varchar(100) NOT NULL COMMENT '方案名称',
  `plan_type` varchar(20) NOT NULL COMMENT '方案类型(basic,standard,premium)',
  `insurance_company` varchar(50) NOT NULL COMMENT '保险公司',
  `coverage_details` text COMMENT '保障详情(JSON)',
  `premium_amount` decimal(10,2) NOT NULL COMMENT '保费金额',
  `coverage_period` int(11) DEFAULT '365' COMMENT '保障期限(天)',
  `applicable_categories` text COMMENT '适用任务分类(JSON)',
  `risk_level` varchar(20) DEFAULT 'medium' COMMENT '风险等级',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `plan_code` (`plan_code`),
  KEY `idx_plan_type` (`plan_type`),
  KEY `idx_active` (`is_active`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='保险方案配置表';

-- ----------------------------
-- Table structure for legal_regulations
-- ----------------------------
DROP TABLE IF EXISTS `legal_regulations`;
CREATE TABLE `legal_regulations` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `region` varchar(10) NOT NULL COMMENT '地区代码',
  `business_type` varchar(30) NOT NULL COMMENT '业务类型',
  `regulation_type` varchar(30) NOT NULL COMMENT '法规类型',
  `title` varchar(200) NOT NULL COMMENT '法规标题',
  `content` longtext COMMENT '法规内容',
  `effective_date` date NOT NULL COMMENT '生效日期',
  `update_date` date DEFAULT NULL COMMENT '更新日期',
  `authority` varchar(100) DEFAULT '' COMMENT '发布机构',
  `compliance_level` varchar(20) DEFAULT 'mandatory' COMMENT '合规级别',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否有效',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_region` (`region`),
  KEY `idx_business_type` (`business_type`),
  KEY `idx_effective_date` (`effective_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='法律法规表';

-- ----------------------------
-- Table structure for platform_revenue_records
-- ----------------------------
DROP TABLE IF EXISTS `platform_revenue_records`;
CREATE TABLE `platform_revenue_records` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `task_id` int(11) unsigned NOT NULL COMMENT '任务ID',
  `revenue_type` varchar(30) NOT NULL COMMENT '收入类型',
  `amount` decimal(10,2) NOT NULL COMMENT '收入金额',
  `source_user_id` int(11) unsigned NOT NULL COMMENT '来源用户ID',
  `description` varchar(200) DEFAULT '' COMMENT '收入描述',
  `settlement_status` varchar(20) DEFAULT 'pending' COMMENT '结算状态',
  `settlement_time` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_revenue_type` (`revenue_type`),
  KEY `idx_settlement_status` (`settlement_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='平台收入记录表';

-- ----------------------------
-- Table structure for print_document
-- ----------------------------
DROP TABLE IF EXISTS `print_document`;
CREATE TABLE `print_document` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '平台选择，1易联云，2飞鹅云',
  `supplier_id` int(11) NOT NULL DEFAULT '0' COMMENT '供应商id，0为平台',
  `print_name` varchar(255) NOT NULL DEFAULT '' COMMENT '打印机名称',
  `yly_user_id` varchar(255) NOT NULL DEFAULT '' COMMENT '易联云用户id',
  `yly_app_id` varchar(255) NOT NULL DEFAULT '' COMMENT '易联云应用ID',
  `yly_app_secret` varchar(255) NOT NULL DEFAULT '' COMMENT '易联云应用密钥',
  `yly_sn` varchar(255) NOT NULL DEFAULT '' COMMENT '易联云终端号',
  `fey_user` varchar(255) NOT NULL DEFAULT '' COMMENT '飞鹅云user',
  `fey_ukey` varchar(255) NOT NULL DEFAULT '' COMMENT '飞鹅云ukey',
  `fey_sn` varchar(255) NOT NULL DEFAULT '' COMMENT '飞鹅云SN',
  `times` int(11) NOT NULL DEFAULT '0' COMMENT '打印联数',
  `print_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '打印时机，1支付后，2下单后',
  `print_content` longtext COMMENT '打印内容',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '申请时间',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '申请状态0申请，1同意，2拒绝',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='小票打印机列表';

-- ----------------------------
-- Table structure for promoter_apply
-- ----------------------------
DROP TABLE IF EXISTS `promoter_apply`;
CREATE TABLE `promoter_apply` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `uid` int(11) NOT NULL DEFAULT '0' COMMENT '用户uid',
  `nickname` varchar(255) NOT NULL DEFAULT '' COMMENT '用户昵称',
  `real_name` varchar(255) NOT NULL DEFAULT '' COMMENT '用户名称',
  `phone` varchar(32) NOT NULL DEFAULT '0' COMMENT '用户电话',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '申请状态0申请，1同意，2拒绝',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '申请时间',
  `status_time` int(11) NOT NULL DEFAULT '0' COMMENT '审核时间',
  `refusal_reason` varchar(1000) NOT NULL DEFAULT '' COMMENT '拒绝理由',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='推广员申请表';

-- ----------------------------
-- Table structure for qrcode
-- ----------------------------
DROP TABLE IF EXISTS `qrcode`;
CREATE TABLE `qrcode` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '微信二维码ID',
  `third_type` varchar(32) NOT NULL DEFAULT '' COMMENT '二维码类型',
  `third_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
  `ticket` varchar(255) NOT NULL DEFAULT '' COMMENT '二维码参数',
  `expire_seconds` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '二维码有效时间',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态',
  `add_time` varchar(255) NOT NULL DEFAULT '0' COMMENT '添加时间',
  `url` varchar(255) NOT NULL DEFAULT '' COMMENT '微信访问url',
  `qrcode_url` varchar(255) NOT NULL DEFAULT '' COMMENT '微信二维码url',
  `scan` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '被扫的次数',
  `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '二维码所属平台1=小程序，2=公众号，3=H5',
  PRIMARY KEY (`id`),
  UNIQUE KEY `third_type` (`third_type`,`third_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信二维码管理表';

-- ----------------------------
-- Table structure for queue_auxiliary
-- ----------------------------
DROP TABLE IF EXISTS `queue_auxiliary`;
CREATE TABLE `queue_auxiliary` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `binding_id` int(10) NOT NULL DEFAULT '0' COMMENT '绑定id',
  `relation_id` int(10) NOT NULL DEFAULT '0' COMMENT '关联id',
  `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '类型0=客服转接辅助，1=商品和分类辅助，2=优惠券和商品辅助',
  `other` varchar(2048) NOT NULL DEFAULT '' COMMENT '其他数据为json',
  `status` int(1) unsigned NOT NULL DEFAULT '0' COMMENT '数据状态 0：未执行，1：成功， 2：失败， 3:删除',
  `update_time` int(10) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `add_time` int(10) NOT NULL DEFAULT '0' COMMENT '添加时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='辅助表';

-- ----------------------------
-- Table structure for queue_list
-- ----------------------------
DROP TABLE IF EXISTS `queue_list`;
CREATE TABLE `queue_list` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '队列类型  1：批量发放用户优惠券 2 : 批量设置用户分组, 3:批量设置用户标签, 4:批量下架商品, 5:批量删除商品规格, 6:批量删除订单, 7:批量手动发货, 8:批量打印电子面单, 9:批量配送, 10:批量虚拟发货',
  `source` varchar(5) NOT NULL DEFAULT 'admin' COMMENT '来源 前端：home ，后端：admin',
  `execute_key` varchar(512) NOT NULL DEFAULT '' COMMENT '队列执行数据缓存key',
  `title` varchar(200) NOT NULL DEFAULT '' COMMENT ' 队列名称',
  `queue_in_value` text COMMENT '队列参数',
  `sort` int(10) NOT NULL DEFAULT '0' COMMENT '排序倒序',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0 :尚未执行，1：正在执行，2：执行成功, 3:执行失败,4: 删除的异常队列',
  `first_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '首次执行时间',
  `again_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '再次执行时间',
  `finish_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '完成时间',
  `surplus_num` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '剩余未执行数量',
  `total_num` int(10) unsigned NOT NULL DEFAULT '0' COMMENT ' 总数量',
  `is_del` tinyint(1) NOT NULL DEFAULT '0',
  `add_time` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`,`type`,`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务列表';

-- ----------------------------
-- Table structure for skills
-- ----------------------------
DROP TABLE IF EXISTS `skills`;
CREATE TABLE `skills` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '技能ID',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '技能名称',
  `category` varchar(100) NOT NULL DEFAULT '' COMMENT '技能分类',
  `description` varchar(255) DEFAULT NULL COMMENT '技能描述',
  `icon` varchar(255) DEFAULT NULL COMMENT '技能图标',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1启用，0禁用',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `category` (`category`),
  KEY `status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COMMENT='技能表';

-- ----------------------------
-- Table structure for sms_record
-- ----------------------------
DROP TABLE IF EXISTS `sms_record`;
CREATE TABLE `sms_record` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '短信发送记录编号',
  `uid` varchar(255) NOT NULL DEFAULT '' COMMENT '短信平台账号',
  `phone` char(11) NOT NULL DEFAULT '' COMMENT '接受短信的手机号',
  `content` text COMMENT '短信内容',
  `add_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '发送短信时间',
  `add_ip` varchar(16) NOT NULL DEFAULT '' COMMENT '添加记录ip',
  `template` varchar(255) NOT NULL DEFAULT '' COMMENT '短信模板ID',
  `resultcode` int(6) unsigned NOT NULL DEFAULT '0' COMMENT '状态码 100=成功,130=失败,131=空号,132=停机,133=关机,134=无状态',
  `record_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '发送记录id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='短信发送记录表';

-- ----------------------------
-- Table structure for system_admin
-- ----------------------------
DROP TABLE IF EXISTS `system_admin`;
CREATE TABLE `system_admin` (
  `id` smallint(5) unsigned NOT NULL AUTO_INCREMENT COMMENT '后台管理员表ID',
  `account` varchar(32) NOT NULL DEFAULT '' COMMENT '后台管理员账号',
  `admin_type` tinyint(2) NOT NULL DEFAULT '1' COMMENT '管理员类型 1平台 2门店 4供应商',
  `relation_id` int(10) NOT NULL DEFAULT '0' COMMENT '关联ID',
  `head_pic` varchar(255) NOT NULL DEFAULT '',
  `pwd` varchar(100) NOT NULL DEFAULT '' COMMENT '后台管理员密码',
  `real_name` varchar(16) NOT NULL DEFAULT '' COMMENT '后台管理员姓名',
  `phone` varchar(32) NOT NULL DEFAULT '' COMMENT '用户电话',
  `roles` varchar(128) NOT NULL DEFAULT '' COMMENT '后台管理员权限(menus_id)',
  `last_ip` varchar(16) NOT NULL DEFAULT '' COMMENT '后台管理员最后一次登录ip',
  `last_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '后台管理员最后一次登录时间',
  `add_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '后台管理员添加时间',
  `login_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '登录次数',
  `level` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '后台管理员级别',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '后台管理员状态 1有效0无效',
  `is_way` tinyint(1) NOT NULL DEFAULT '0' COMMENT '图片上传方式 0=本地上传 1=网络图片 2=扫码上传',
  `division_id` int(11) NOT NULL DEFAULT '0' COMMENT '事业部id',
  `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `account` (`account`) USING BTREE,
  KEY `status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='后台管理员表';

-- ----------------------------
-- Table structure for system_attachment
-- ----------------------------
DROP TABLE IF EXISTS `system_attachment`;
CREATE TABLE `system_attachment` (
  `att_id` int(10) NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) DEFAULT '1' COMMENT '类型：1平台2:门店',
  `file_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '文件类型：1:图片2：视频',
  `relation_id` int(10) NOT NULL DEFAULT '0' COMMENT '关联id',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '附件名称',
  `att_dir` varchar(200) NOT NULL DEFAULT '' COMMENT '附件路径',
  `satt_dir` varchar(200) NOT NULL DEFAULT '' COMMENT '压缩图片路径',
  `att_size` char(30) NOT NULL DEFAULT '' COMMENT '附件大小',
  `att_type` char(30) NOT NULL DEFAULT '' COMMENT '附件类型',
  `pid` int(10) NOT NULL DEFAULT '0' COMMENT '分类ID0编辑器,1商品图片,2拼团图片,3砍价图片,4秒杀图片,5文章图片,6组合数据图',
  `time` int(11) NOT NULL DEFAULT '0' COMMENT '上传时间',
  `image_type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '图片上传类型 1本地 2七牛云 3OSS 4COS ',
  `module_type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '图片上传模块类型 1 后台上传 2 用户生成',
  `real_name` varchar(255) NOT NULL DEFAULT '' COMMENT '原始文件名',
  `scan_token` varchar(32) NOT NULL DEFAULT '' COMMENT '扫码上传的token',
  PRIMARY KEY (`att_id`),
  KEY `time` (`time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=60 DEFAULT CHARSET=utf8mb4 COMMENT='附件管理表';

-- ----------------------------
-- Table structure for system_attachment_category
-- ----------------------------
DROP TABLE IF EXISTS `system_attachment_category`;
CREATE TABLE `system_attachment_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) DEFAULT '1' COMMENT '类型：1平台2:门店',
  `file_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '文件类型：1:图片2：视频',
  `relation_id` int(10) NOT NULL DEFAULT '0' COMMENT '关联id',
  `pid` int(11) NOT NULL DEFAULT '0' COMMENT '父级ID',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '分类名称',
  `enname` varchar(50) NOT NULL DEFAULT '' COMMENT '分类目录',
  PRIMARY KEY (`id`),
  UNIQUE KEY `id` (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='附件分类表';

-- ----------------------------
-- Table structure for system_city
-- ----------------------------
DROP TABLE IF EXISTS `system_city`;
CREATE TABLE `system_city` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `city_id` int(11) NOT NULL DEFAULT '0' COMMENT '城市id',
  `level` int(11) NOT NULL DEFAULT '0' COMMENT '省市级别',
  `parent_id` int(11) NOT NULL DEFAULT '0' COMMENT '父级id',
  `area_code` varchar(30) NOT NULL DEFAULT '' COMMENT '区号',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '名称',
  `merger_name` varchar(255) NOT NULL DEFAULT '' COMMENT '合并名称',
  `lng` varchar(50) NOT NULL DEFAULT '' COMMENT '经度',
  `lat` varchar(50) NOT NULL DEFAULT '' COMMENT '纬度',
  `is_show` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否展示',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3971 DEFAULT CHARSET=utf8mb4 COMMENT='城市表';

-- ----------------------------
-- Table structure for system_config
-- ----------------------------
DROP TABLE IF EXISTS `system_config`;
CREATE TABLE `system_config` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '配置id',
  `is_store` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0=总后台,1=门店',
  `menu_name` varchar(255) NOT NULL DEFAULT '' COMMENT '字段名称',
  `type` varchar(255) NOT NULL DEFAULT '' COMMENT '类型(文本框,单选按钮...)',
  `input_type` varchar(20) NOT NULL DEFAULT 'input' COMMENT '表单类型',
  `config_tab_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '配置分类id',
  `parameter` varchar(255) NOT NULL DEFAULT '' COMMENT '规则 单选框和多选框',
  `upload_type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '上传文件格式1单图2多图3文件',
  `required` varchar(255) NOT NULL DEFAULT '' COMMENT '规则',
  `width` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '多行文本框的宽度',
  `high` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '多行文框的高度',
  `value` varchar(5000) NOT NULL DEFAULT '' COMMENT '默认值',
  `info` varchar(255) NOT NULL DEFAULT '' COMMENT '配置名称',
  `desc` varchar(255) NOT NULL DEFAULT '' COMMENT '配置简介',
  `sort` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否隐藏',
  PRIMARY KEY (`id`),
  KEY `is_store` (`is_store`) USING BTREE,
  KEY `config_tab_id` (`config_tab_id`) USING BTREE,
  KEY `menu_name` (`menu_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=557 DEFAULT CHARSET=utf8mb4 COMMENT='配置表';

-- ----------------------------
-- Table structure for system_config_tab
-- ----------------------------
DROP TABLE IF EXISTS `system_config_tab`;
CREATE TABLE `system_config_tab` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '配置分类id',
  `is_store` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0=总后台,1=门店',
  `pid` int(11) NOT NULL DEFAULT '0' COMMENT '上级分类id',
  `title` varchar(255) NOT NULL DEFAULT '' COMMENT '配置分类名称',
  `eng_title` varchar(255) NOT NULL DEFAULT '' COMMENT '配置分类英文名称',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '配置分类状态',
  `info` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '配置分类是否显示',
  `icon` varchar(30) NOT NULL DEFAULT '' COMMENT '图标',
  `type` int(2) NOT NULL DEFAULT '0' COMMENT '配置类型',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  PRIMARY KEY (`id`),
  KEY `pid` (`pid`) USING BTREE,
  KEY `is_store` (`is_store`) USING BTREE,
  KEY `eng_title` (`eng_title`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=91 DEFAULT CHARSET=utf8mb4 COMMENT='配置分类表';

-- ----------------------------
-- Table structure for system_file
-- ----------------------------
DROP TABLE IF EXISTS `system_file`;
CREATE TABLE `system_file` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '文件对比ID',
  `cthash` char(32) NOT NULL DEFAULT '' COMMENT '文件内容',
  `filename` varchar(255) NOT NULL DEFAULT '' COMMENT '文价名称',
  `atime` char(12) NOT NULL DEFAULT '' COMMENT '上次访问时间',
  `mtime` char(12) NOT NULL DEFAULT '' COMMENT '上次修改时间',
  `ctime` char(12) NOT NULL DEFAULT '' COMMENT '上次改变时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文件对比表';

-- ----------------------------
-- Table structure for system_group
-- ----------------------------
DROP TABLE IF EXISTS `system_group`;
CREATE TABLE `system_group` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '组合数据ID',
  `cate_id` int(11) NOT NULL DEFAULT '0' COMMENT '分类id',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '数据组名称',
  `info` varchar(256) NOT NULL DEFAULT '' COMMENT '数据提示',
  `config_name` varchar(50) NOT NULL DEFAULT '' COMMENT '数据字段',
  `fields` text COMMENT '数据组字段以及类型（json数据）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `config_name` (`config_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=70 DEFAULT CHARSET=utf8mb4 COMMENT='组合数据表';

-- ----------------------------
-- Table structure for system_group_data
-- ----------------------------
DROP TABLE IF EXISTS `system_group_data`;
CREATE TABLE `system_group_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '组合数据详情ID',
  `gid` int(11) NOT NULL DEFAULT '0' COMMENT '对应的数据组id',
  `value` text COMMENT '数据组对应的数据值（json数据）',
  `add_time` int(10) NOT NULL DEFAULT '0' COMMENT '添加数据时间',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '数据排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态（1：开启；2：关闭；）',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=384 DEFAULT CHARSET=utf8mb4 COMMENT='组合数据详情表';

-- ----------------------------
-- Table structure for system_log
-- ----------------------------
DROP TABLE IF EXISTS `system_log`;
CREATE TABLE `system_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '管理员操作记录ID',
  `store_id` int(10) NOT NULL DEFAULT '0' COMMENT '门店id',
  `admin_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '管理员id',
  `admin_name` varchar(64) NOT NULL DEFAULT '' COMMENT '管理员姓名',
  `path` varchar(128) NOT NULL DEFAULT '' COMMENT '链接',
  `page` varchar(64) NOT NULL DEFAULT '' COMMENT '行为',
  `method` varchar(12) NOT NULL DEFAULT '' COMMENT '访问类型',
  `ip` varchar(16) NOT NULL DEFAULT '' COMMENT '登录IP',
  `type` varchar(32) NOT NULL DEFAULT '' COMMENT '类型',
  `add_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '操作时间',
  `merchant_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '商户id',
  PRIMARY KEY (`id`),
  KEY `admin_id` (`admin_id`) USING BTREE,
  KEY `add_time` (`add_time`) USING BTREE,
  KEY `type` (`type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员操作记录表';

-- ----------------------------
-- Table structure for system_menus
-- ----------------------------
DROP TABLE IF EXISTS `system_menus`;
CREATE TABLE `system_menus` (
  `id` smallint(5) unsigned NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `pid` smallint(5) unsigned NOT NULL DEFAULT '0' COMMENT '父级id',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '菜单类型1:平台2:门店',
  `icon` varchar(50) NOT NULL DEFAULT '' COMMENT '图标',
  `menu_name` varchar(32) NOT NULL DEFAULT '' COMMENT '按钮名',
  `module` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '模块名',
  `controller` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '控制器',
  `action` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '方法名',
  `api_url` varchar(100) NOT NULL DEFAULT '' COMMENT 'api接口地址',
  `methods` varchar(10) NOT NULL DEFAULT '' COMMENT '提交方式POST GET PUT DELETE',
  `params` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '[]' COMMENT '参数',
  `sort` int(10) NOT NULL DEFAULT '1' COMMENT '排序',
  `is_show` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '是否为隐藏菜单0=隐藏菜单,1=显示菜单',
  `is_show_path` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为隐藏菜单供前台使用',
  `access` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '子管理员是否可用',
  `menu_path` varchar(128) NOT NULL DEFAULT '' COMMENT '路由名称 前端使用',
  `path` varchar(255) NOT NULL DEFAULT '' COMMENT '路径',
  `auth_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为菜单 1菜单 2功能',
  `header` varchar(50) NOT NULL DEFAULT '' COMMENT '顶部菜单标示',
  `is_header` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否顶部菜单1是0否',
  `unique_auth` varchar(150) NOT NULL DEFAULT '' COMMENT '前台唯一标识',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  KEY `type` (`type`) USING BTREE,
  KEY `pid` (`pid`) USING BTREE,
  KEY `is_show` (`is_show`) USING BTREE,
  KEY `access` (`access`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1669 DEFAULT CHARSET=utf8mb4 COMMENT='菜单表';

-- ----------------------------
-- Table structure for system_menus_relevance
-- ----------------------------
DROP TABLE IF EXISTS `system_menus_relevance`;
CREATE TABLE `system_menus_relevance` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `menu_id` int(11) NOT NULL DEFAULT '0' COMMENT '菜单id',
  `keyword` varchar(255) NOT NULL COMMENT '关键词',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=392 DEFAULT CHARSET=utf8mb4 COMMENT='菜单关键词搜索';

-- ----------------------------
-- Table structure for system_notice
-- ----------------------------
DROP TABLE IF EXISTS `system_notice`;
CREATE TABLE `system_notice` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '通知模板id',
  `title` varchar(64) NOT NULL DEFAULT '' COMMENT '通知标题',
  `type` varchar(64) NOT NULL DEFAULT '' COMMENT '通知类型',
  `icon` varchar(16) NOT NULL DEFAULT '' COMMENT '图标',
  `url` varchar(64) NOT NULL DEFAULT '' COMMENT '链接',
  `table_title` varchar(256) NOT NULL DEFAULT '' COMMENT '通知数据',
  `template` varchar(64) NOT NULL DEFAULT '' COMMENT '通知模板',
  `push_admin` varchar(128) NOT NULL DEFAULT '' COMMENT '通知管理员id',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态',
  PRIMARY KEY (`id`),
  UNIQUE KEY `type` (`type`) USING BTREE,
  KEY `status` (`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通知模板表';

-- ----------------------------
-- Table structure for system_notice_admin
-- ----------------------------
DROP TABLE IF EXISTS `system_notice_admin`;
CREATE TABLE `system_notice_admin` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '通知记录ID',
  `notice_type` varchar(64) NOT NULL DEFAULT '' COMMENT '通知类型',
  `admin_id` smallint(5) unsigned NOT NULL DEFAULT '0' COMMENT '通知的管理员',
  `link_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '关联ID',
  `table_data` text COMMENT '通知的数据',
  `is_click` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '点击次数',
  `is_visit` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '访问次数',
  `visit_time` int(11) NOT NULL DEFAULT '0' COMMENT '访问时间',
  `add_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '通知时间',
  PRIMARY KEY (`id`),
  KEY `admin_id` (`admin_id`,`notice_type`) USING BTREE,
  KEY `add_time` (`add_time`) USING BTREE,
  KEY `is_visit` (`is_visit`) USING BTREE,
  KEY `is_click` (`is_click`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通知记录表';

-- ----------------------------
-- Table structure for system_notification
-- ----------------------------
DROP TABLE IF EXISTS `system_notification`;
CREATE TABLE `system_notification` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `mark` varchar(50) NOT NULL DEFAULT '' COMMENT '标识',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '通知类型',
  `title` varchar(100) NOT NULL DEFAULT '' COMMENT '通知场景说明',
  `is_system` tinyint(1) NOT NULL DEFAULT '0' COMMENT '站内信（0：不存在，1：开启，2：关闭）',
  `is_app` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'APP推送（0：不存在，1：开启，2：关闭）',
  `is_wechat` tinyint(1) NOT NULL DEFAULT '0' COMMENT '公众号模板消息（0：不存在，1：开启，2：关闭）',
  `is_routine` tinyint(1) NOT NULL DEFAULT '0' COMMENT '小程序订阅消息（0：不存在，1：开启，2：关闭）',
  `is_sms` tinyint(1) NOT NULL DEFAULT '0' COMMENT '发送短信（0：不存在，1：开启，2：关闭）',
  `is_ent_wechat` tinyint(1) NOT NULL DEFAULT '0' COMMENT '企业微信群通知（0：不存在，1：开启，2：关闭）',
  `system_title` varchar(256) NOT NULL DEFAULT '' COMMENT '站内信标题',
  `system_text` varchar(512) NOT NULL DEFAULT '' COMMENT '系统消息id',
  `app_id` int(11) NOT NULL DEFAULT '0' COMMENT 'app推送id',
  `wechat_id` varchar(50) NOT NULL DEFAULT '0' COMMENT '模板消息id',
  `routine_id` varchar(50) NOT NULL DEFAULT '0' COMMENT '订阅消息id',
  `sms_id` varchar(50) NOT NULL DEFAULT '' COMMENT '短信id',
  `sms_text` varchar(255) NOT NULL DEFAULT '' COMMENT '短信模版内容',
  `ent_wechat_text` varchar(512) NOT NULL DEFAULT '' COMMENT '企业微信消息',
  `variable` varchar(256) NOT NULL DEFAULT '' COMMENT '变量',
  `url` varchar(512) NOT NULL DEFAULT '' COMMENT '群机器人链接',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '类型（1：用户，2：管理员）',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=41 DEFAULT CHARSET=utf8 COMMENT='通知设置';

-- ----------------------------
-- Table structure for system_notifications
-- ----------------------------
DROP TABLE IF EXISTS `system_notifications`;
CREATE TABLE `system_notifications` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '通知ID',
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `title` varchar(200) NOT NULL COMMENT '通知标题',
  `content` text NOT NULL COMMENT '通知内容',
  `type` varchar(50) NOT NULL DEFAULT 'system' COMMENT '通知类型',
  `related_id` int(11) unsigned DEFAULT NULL COMMENT '关联ID',
  `related_type` varchar(50) DEFAULT NULL COMMENT '关联类型',
  `is_read` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否已读',
  `read_time` int(11) unsigned DEFAULT NULL COMMENT '阅读时间',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `type` (`type`),
  KEY `is_read` (`is_read`),
  KEY `create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统通知表';

-- ----------------------------
-- Table structure for system_role
-- ----------------------------
DROP TABLE IF EXISTS `system_role`;
CREATE TABLE `system_role` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '身份管理id',
  `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '类型：0平台2:供应商',
  `relation_id` int(11) NOT NULL DEFAULT '0' COMMENT '门店、供应商id',
  `role_name` varchar(32) NOT NULL DEFAULT '' COMMENT '身份管理名称',
  `rules` longtext COMMENT '身份管理权限(menus_id)',
  `level` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态',
  PRIMARY KEY (`id`),
  KEY `status` (`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='身份管理表';

-- ----------------------------
-- Table structure for system_storage
-- ----------------------------
DROP TABLE IF EXISTS `system_storage`;
CREATE TABLE `system_storage` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `access_key` varchar(100) NOT NULL DEFAULT '' COMMENT 'access_key',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '1=本地存储,2=七牛,3=oss,4=cos',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '空间名',
  `region` varchar(100) NOT NULL DEFAULT '' COMMENT '地域',
  `acl` enum('private','public-read','public-read-write') NOT NULL DEFAULT 'public-read' COMMENT '权限',
  `domain` varchar(100) NOT NULL DEFAULT '' COMMENT '空间域名',
  `cname` varchar(255) NOT NULL DEFAULT '' COMMENT 'CNAME值',
  `is_ssl` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0=http,1=https',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `add_time` int(10) NOT NULL DEFAULT '0' COMMENT '添加事件',
  `update_time` int(10) NOT NULL DEFAULT '0' COMMENT '更新事件',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='云储存';

-- ----------------------------
-- Table structure for system_timer
-- ----------------------------
DROP TABLE IF EXISTS `system_timer`;
CREATE TABLE `system_timer` (
  `id` int(12) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '定时器名称',
  `mark` varchar(50) NOT NULL DEFAULT '' COMMENT '标识',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '周期状态 1=N分钟 2=N小时 3=每小时 4=每天 5=N天 6=每星期 7=每月 8=每年',
  `title` varchar(255) NOT NULL DEFAULT '' COMMENT '任务说明',
  `is_open` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否开启',
  `cycle` varchar(255) NOT NULL DEFAULT '' COMMENT '执行周期',
  `last_execution_time` int(12) NOT NULL DEFAULT '0' COMMENT '上次执行时间',
  `update_execution_time` int(12) NOT NULL DEFAULT '0' COMMENT '修改时间',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `add_time` int(12) NOT NULL DEFAULT '0' COMMENT '添加时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8 COMMENT='定时器';

-- ----------------------------
-- Table structure for task_applications
-- ----------------------------
DROP TABLE IF EXISTS `task_applications`;
CREATE TABLE `task_applications` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `task_id` int(11) NOT NULL DEFAULT '0' COMMENT '任务ID',
  `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '申请师傅用户ID',
  `message` text COMMENT '申请留言',
  `status` enum('pending','approved','rejected','in_progress','completed','cancelled') NOT NULL DEFAULT 'pending' COMMENT '申请状态',
  `start_time` int(11) NOT NULL DEFAULT '0' COMMENT '开始时间',
  `end_time` int(11) NOT NULL DEFAULT '0' COMMENT '结束时间',
  `work_hours` decimal(5,2) DEFAULT NULL COMMENT '工作时长',
  `total_amount` decimal(10,2) DEFAULT NULL COMMENT '总金额',
  `completion_note` text COMMENT '完成备注',
  `cancel_reason` varchar(255) DEFAULT NULL COMMENT '取消原因',
  `cancel_time` int(11) NOT NULL DEFAULT '0' COMMENT '取消时间',
  `settlement_status` enum('pending','completed') NOT NULL DEFAULT 'pending' COMMENT '结算状态',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `task_id` (`task_id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务申请表';

-- ----------------------------
-- Table structure for task_category
-- ----------------------------
DROP TABLE IF EXISTS `task_category`;
CREATE TABLE `task_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `parent_id` int(11) NOT NULL DEFAULT '0' COMMENT '父级分类ID',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '分类名称',
  `icon` varchar(255) NOT NULL DEFAULT '' COMMENT '分类图标',
  `description` varchar(255) NOT NULL DEFAULT '' COMMENT '分类描述',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1启用，0禁用',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `parent_id` (`parent_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COMMENT='任务分类表';

-- ----------------------------
-- Table structure for task_insurance_records
-- ----------------------------
DROP TABLE IF EXISTS `task_insurance_records`;
CREATE TABLE `task_insurance_records` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `task_id` int(11) unsigned NOT NULL COMMENT '任务ID',
  `worker_id` int(11) unsigned NOT NULL COMMENT '师傅ID',
  `employer_id` int(11) unsigned NOT NULL COMMENT '雇主ID',
  `insurance_plan_id` int(11) unsigned NOT NULL COMMENT '保险方案ID',
  `policy_number` varchar(50) DEFAULT '' COMMENT '保单号',
  `premium_amount` decimal(10,2) NOT NULL COMMENT '保费金额',
  `payer_type` varchar(20) DEFAULT 'employer' COMMENT '付费方',
  `coverage_start` datetime NOT NULL COMMENT '保障开始时间',
  `coverage_end` datetime NOT NULL COMMENT '保障结束时间',
  `status` varchar(20) DEFAULT 'pending' COMMENT '状态',
  `purchase_time` timestamp NULL DEFAULT NULL COMMENT '购买时间',
  `insurance_document` varchar(500) DEFAULT '' COMMENT '保险文档',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `task_worker` (`task_id`,`worker_id`),
  KEY `idx_policy_number` (`policy_number`),
  KEY `idx_worker_id` (`worker_id`),
  KEY `idx_employer_id` (`employer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务保险记录表';

-- ----------------------------
-- Table structure for task_matching_records
-- ----------------------------
DROP TABLE IF EXISTS `task_matching_records`;
CREATE TABLE `task_matching_records` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `task_id` int(11) unsigned NOT NULL COMMENT '任务ID',
  `worker_id` int(11) unsigned NOT NULL COMMENT '师傅ID',
  `match_score` decimal(5,3) DEFAULT '0.000' COMMENT '匹配分数',
  `distance_score` decimal(5,3) DEFAULT '0.000' COMMENT '距离分数',
  `skill_score` decimal(5,3) DEFAULT '0.000' COMMENT '技能分数',
  `rating_score` decimal(5,3) DEFAULT '0.000' COMMENT '评分分数',
  `availability_score` decimal(5,3) DEFAULT '0.000' COMMENT '可用性分数',
  `price_score` decimal(5,3) DEFAULT '0.000' COMMENT '价格分数',
  `actual_distance` decimal(8,2) DEFAULT '0.00' COMMENT '实际距离(公里)',
  `is_recommended` tinyint(1) DEFAULT '0' COMMENT '是否已推荐',
  `recommended_at` timestamp NULL DEFAULT NULL COMMENT '推荐时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_matching_task_id` (`task_id`),
  KEY `idx_matching_worker_id` (`worker_id`),
  KEY `idx_matching_score` (`match_score`),
  KEY `idx_matching_recommended` (`is_recommended`,`recommended_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务匹配记录表';

-- ----------------------------
-- Table structure for task_progress_logs
-- ----------------------------
DROP TABLE IF EXISTS `task_progress_logs`;
CREATE TABLE `task_progress_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '进度日志ID',
  `task_id` int(11) NOT NULL DEFAULT '0' COMMENT '任务ID',
  `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID（师傅）',
  `status` enum('published','in_progress','paused','completed','cancelled') NOT NULL DEFAULT 'published' COMMENT '状态',
  `progress` tinyint(3) NOT NULL DEFAULT '0' COMMENT '进度百分比(0-100)',
  `description` text COMMENT '进度描述',
  `images` json DEFAULT NULL COMMENT '进度图片JSON',
  `location` json DEFAULT NULL COMMENT '位置信息JSON',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `task_id` (`task_id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`),
  KEY `create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务进度日志表';

-- ----------------------------
-- Table structure for task_protection_options
-- ----------------------------
DROP TABLE IF EXISTS `task_protection_options`;
CREATE TABLE `task_protection_options` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `task_id` int(11) unsigned NOT NULL COMMENT '任务ID',
  `protection_type` varchar(20) NOT NULL DEFAULT 'basic' COMMENT '保障类型(basic,protected,premium)',
  `insurance_required` tinyint(1) DEFAULT '0' COMMENT '是否需要保险',
  `contract_required` tinyint(1) DEFAULT '0' COMMENT '是否需要电子合同',
  `insurance_plan_id` int(11) unsigned DEFAULT NULL COMMENT '保险方案ID',
  `contract_type` varchar(20) DEFAULT 'standard' COMMENT '合同类型(standard,custom)',
  `insurance_payer` varchar(20) DEFAULT 'employer' COMMENT '保险费承担方(employer,worker,split)',
  `insurance_cost` decimal(10,2) DEFAULT '0.00' COMMENT '保险费用',
  `contract_cost` decimal(10,2) DEFAULT '0.00' COMMENT '合同费用',
  `platform_fee_rate` decimal(5,4) DEFAULT '0.0500' COMMENT '平台手续费率',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `task_id` (`task_id`),
  KEY `idx_protection_type` (`protection_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务保障选项表';

-- ----------------------------
-- Table structure for task_reviews
-- ----------------------------
DROP TABLE IF EXISTS `task_reviews`;
CREATE TABLE `task_reviews` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '评价ID',
  `task_id` int(11) NOT NULL DEFAULT '0' COMMENT '任务ID',
  `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '评价者用户ID（企业）',
  `worker_id` int(11) NOT NULL DEFAULT '0' COMMENT '被评价师傅用户ID',
  `rating` tinyint(1) NOT NULL DEFAULT '5' COMMENT '评分：1-5星',
  `review_content` text COMMENT '评价内容',
  `reply_content` text COMMENT '回复内容',
  `reply_time` int(11) NOT NULL DEFAULT '0' COMMENT '回复时间',
  `images` text COMMENT '评价图片JSON',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `task_id` (`task_id`),
  KEY `user_id` (`user_id`),
  KEY `worker_id` (`worker_id`),
  KEY `rating` (`rating`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务评价表';

-- ----------------------------
-- Table structure for task_skills
-- ----------------------------
DROP TABLE IF EXISTS `task_skills`;
CREATE TABLE `task_skills` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `task_id` int(11) unsigned NOT NULL COMMENT '任务ID',
  `skill_id` int(11) unsigned NOT NULL COMMENT '技能ID',
  `required_level` tinyint(1) DEFAULT '1' COMMENT '要求等级(1-5)',
  `is_required` tinyint(1) DEFAULT '1' COMMENT '是否必需(0:可选 1:必需)',
  `weight` decimal(3,2) DEFAULT '1.00' COMMENT '权重(用于匹配算法)',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `task_skill_unique` (`task_id`,`skill_id`),
  KEY `idx_task_skills_task_id` (`task_id`),
  KEY `idx_task_skills_skill_id` (`skill_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务技能关联表';

-- ----------------------------
-- Table structure for tasks
-- ----------------------------
DROP TABLE IF EXISTS `tasks`;
CREATE TABLE `tasks` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '发布者用户ID',
  `task_title` varchar(255) NOT NULL DEFAULT '' COMMENT '任务标题',
  `task_description` text NOT NULL COMMENT '任务描述',
  `task_category_id` int(11) NOT NULL DEFAULT '0' COMMENT '任务分类ID',
  `required_workers` int(11) NOT NULL DEFAULT '1' COMMENT '需要师傅数量',
  `task_location` varchar(255) NOT NULL DEFAULT '' COMMENT '任务地点',
  `latitude` decimal(10,6) DEFAULT NULL COMMENT '纬度',
  `longitude` decimal(10,6) DEFAULT NULL COMMENT '经度',
  `hourly_rate` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '时薪',
  `urgency_level` tinyint(1) NOT NULL DEFAULT '1' COMMENT '紧急程度：1普通，2紧急，3特急',
  `start_time` int(11) NOT NULL DEFAULT '0' COMMENT '开始时间',
  `end_time` int(11) NOT NULL DEFAULT '0' COMMENT '结束时间',
  `status` enum('published','in_progress','completed','cancelled') NOT NULL DEFAULT 'published' COMMENT '任务状态',
  `images` text COMMENT '任务图片JSON',
  `contact_name` varchar(100) NOT NULL DEFAULT '' COMMENT '联系人姓名',
  `contact_phone` varchar(20) NOT NULL DEFAULT '' COMMENT '联系电话',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `task_category_id` (`task_category_id`),
  KEY `status` (`status`),
  KEY `urgency_level` (`urgency_level`),
  KEY `location` (`latitude`,`longitude`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务表';

-- ----------------------------
-- Table structure for template_message
-- ----------------------------
DROP TABLE IF EXISTS `template_message`;
CREATE TABLE `template_message` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '模板id',
  `notification_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '模版通知场景ID,多个',
  `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0=订阅消息,1=微信模板消息',
  `tempkey` char(50) NOT NULL DEFAULT '' COMMENT '模板编号',
  `name` char(100) NOT NULL DEFAULT '' COMMENT '模板名',
  `kid` varchar(255) NOT NULL DEFAULT '',
  `content` varchar(1000) NOT NULL DEFAULT '' COMMENT '回复内容',
  `example` varchar(300) NOT NULL DEFAULT '' COMMENT '模版示例',
  `tempid` char(100) NOT NULL DEFAULT '' COMMENT '模板ID',
  `add_time` varchar(15) NOT NULL DEFAULT '' COMMENT '添加时间',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=48 DEFAULT CHARSET=utf8mb4 COMMENT='微信模板';

-- ----------------------------
-- Table structure for trust_certificates
-- ----------------------------
DROP TABLE IF EXISTS `trust_certificates`;
CREATE TABLE `trust_certificates` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `certificate_id` varchar(50) NOT NULL COMMENT '证书ID',
  `trust_level` varchar(20) NOT NULL COMMENT '信任等级',
  `trust_score` decimal(5,2) NOT NULL COMMENT '信任得分',
  `blockchain_proofs` text COMMENT '区块链证明(JSON)',
  `certificate_data` text COMMENT '证书数据(JSON)',
  `qr_code` text COMMENT '二维码数据',
  `issued_by` varchar(100) DEFAULT 'Ywork Trust System' COMMENT '签发方',
  `issued_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `valid_until` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否有效',
  PRIMARY KEY (`id`),
  UNIQUE KEY `certificate_id` (`certificate_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_trust_level` (`trust_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='信任证书表';

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user` (
  `uid` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户id',
  `account` varchar(32) NOT NULL DEFAULT '' COMMENT '用户账号',
  `pwd` varchar(32) NOT NULL DEFAULT '' COMMENT '用户密码',
  `real_name` varchar(25) NOT NULL DEFAULT '' COMMENT '真实姓名',
  `birthday` int(11) NOT NULL DEFAULT '0' COMMENT '生日',
  `card_id` varchar(20) NOT NULL DEFAULT '' COMMENT '身份证号码',
  `mark` varchar(255) NOT NULL DEFAULT '' COMMENT '用户备注',
  `partner_id` int(11) NOT NULL DEFAULT '0' COMMENT '合伙人id',
  `group_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户分组id',
  `nickname` varchar(60) NOT NULL DEFAULT '' COMMENT '用户昵称',
  `avatar` varchar(256) NOT NULL DEFAULT '' COMMENT '用户头像',
  `phone` char(15) NOT NULL DEFAULT '' COMMENT '手机号码',
  `add_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '添加时间',
  `add_ip` varchar(16) NOT NULL DEFAULT '' COMMENT '添加ip',
  `last_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '最后一次登录时间',
  `last_ip` varchar(16) NOT NULL DEFAULT '' COMMENT '最后一次登录ip',
  `now_money` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '用户余额',
  `brokerage_price` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '佣金金额',
  `integral` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户剩余积分',
  `exp` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '会员经验',
  `sign_num` int(11) NOT NULL DEFAULT '0' COMMENT '连续签到天数',
  `sign_remind` tinyint(1) NOT NULL DEFAULT '0' COMMENT '签到提醒状态',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '1为正常，0为禁止',
  `level` int(11) NOT NULL DEFAULT '0' COMMENT '等级',
  `agent_level` int(10) NOT NULL DEFAULT '0' COMMENT '分销等级',
  `spread_open` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否有推广资格',
  `spread_uid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '推广元id',
  `spread_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '推广员关联时间',
  `spread_lottery` int(10) NOT NULL DEFAULT '1' COMMENT '推广获取抽奖次数',
  `work_uid` int(11) NOT NULL DEFAULT '0' COMMENT '绑定企业微信成员uid',
  `work_userid` varchar(64) NOT NULL DEFAULT '' COMMENT '绑定企业微信成员uid',
  `user_type` varchar(32) NOT NULL DEFAULT '' COMMENT '用户类型',
  `is_promoter` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否为推广员',
  `pay_count` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户购买次数',
  `spread_count` int(11) NOT NULL DEFAULT '0' COMMENT '下级人数',
  `clean_time` int(11) NOT NULL DEFAULT '0' COMMENT '清理会员时间',
  `addres` varchar(255) NOT NULL DEFAULT '' COMMENT '详细地址',
  `adminid` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '管理员编号 ',
  `login_type` varchar(36) NOT NULL DEFAULT '' COMMENT '用户登陆类型，h5,wechat,routine',
  `login_city` varchar(255) NOT NULL DEFAULT '' COMMENT '登录城市',
  `record_phone` varchar(11) NOT NULL DEFAULT '' COMMENT '记录临时电话',
  `is_money_level` tinyint(1) NOT NULL DEFAULT '0' COMMENT '会员来源  0: 购买商品升级   1：花钱购买的会员2: 会员卡领取',
  `is_ever_level` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否永久性会员  0: 非永久会员  1：永久会员',
  `overdue_time` int(20) NOT NULL DEFAULT '0' COMMENT '会员到期时间',
  `uniqid` varchar(32) NOT NULL DEFAULT '',
  `bar_code` varchar(32) NOT NULL DEFAULT '' COMMENT '条形码值',
  `rand_code` int(6) NOT NULL DEFAULT '0' COMMENT '随机code，用于确认余额支付',
  `sex` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0:其他,1:男,2:女',
  `provincials` varchar(255) NOT NULL DEFAULT '' COMMENT '省市区',
  `province` int(10) NOT NULL DEFAULT '0' COMMENT '省ID',
  `city` int(10) NOT NULL DEFAULT '0' COMMENT '市ID',
  `area` int(10) NOT NULL DEFAULT '0' COMMENT '	区ID',
  `street` int(10) NOT NULL DEFAULT '0' COMMENT '街道ID',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `delete_time` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  `extend_info` longtext COMMENT '用户补充信息',
  `level_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '用户等级是否激活',
  `level_extend_info` longtext COMMENT '激活会员卡补充信息',
  `is_first_order` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否购买首单优惠：0：未购买1已购买',
  `is_newcomer` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否购买新人专享：0：未购买1已购买',
  `replace_order_num` varchar(32) NOT NULL DEFAULT '' COMMENT '事业部/代理商名称',
  `division_name` varchar(32) NOT NULL DEFAULT '' COMMENT '事业部/代理商名称',
  `division_type` int(11) NOT NULL DEFAULT '0' COMMENT '代理类型：0普通，1事业部，2代理，3员工',
  `division_status` int(11) NOT NULL DEFAULT '0' COMMENT '代理状态',
  `division_id` int(11) NOT NULL DEFAULT '0' COMMENT '事业部id',
  `agent_id` int(11) NOT NULL DEFAULT '0' COMMENT '代理商id',
  `staff_id` int(11) NOT NULL DEFAULT '0' COMMENT '员工id',
  `division_percent` int(11) NOT NULL DEFAULT '0' COMMENT '分佣比例',
  `division_end_time` int(11) NOT NULL DEFAULT '0' COMMENT '事业部/代理/员工修改时间',
  `division_change_time` int(11) NOT NULL DEFAULT '0' COMMENT '事业部/代理/员工结束时间',
  `division_invite` int(11) NOT NULL DEFAULT '0' COMMENT '代理商邀请码',
  `identity` tinyint(1) NOT NULL DEFAULT '0' COMMENT '显示身份;0:普通用户,1 渠道商,2 管理员',
  `is_channel` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否是采购商',
  PRIMARY KEY (`uid`),
  KEY `account` (`account`) USING BTREE,
  KEY `spreaduid` (`spread_uid`) USING BTREE,
  KEY `level` (`level`) USING BTREE,
  KEY `status` (`status`) USING BTREE,
  KEY `work_uid` (`work_uid`) USING BTREE,
  KEY `is_promoter` (`is_promoter`,`phone`) USING BTREE,
  KEY `phone` (`phone`) USING BTREE,
  KEY `index_0` (`delete_time`) USING BTREE,
  KEY `add_time_delete_sex` (`add_time`,`delete_time`,`sex`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- ----------------------------
-- Table structure for user_address
-- ----------------------------
DROP TABLE IF EXISTS `user_address`;
CREATE TABLE `user_address` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户地址id',
  `uid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
  `real_name` varchar(32) NOT NULL DEFAULT '' COMMENT '收货人姓名',
  `phone` varchar(16) NOT NULL DEFAULT '' COMMENT '收货人电话',
  `province` varchar(64) NOT NULL DEFAULT '' COMMENT '收货人所在省',
  `city` varchar(64) NOT NULL DEFAULT '' COMMENT '收货人所在市',
  `street` varchar(100) NOT NULL DEFAULT '' COMMENT '所在镇/街道',
  `city_id` int(11) NOT NULL DEFAULT '0' COMMENT '城市id',
  `district` varchar(64) NOT NULL DEFAULT '' COMMENT '收货人所在区',
  `detail` varchar(256) NOT NULL DEFAULT '' COMMENT '收货人详细地址',
  `post_code` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '邮编',
  `longitude` varchar(16) NOT NULL DEFAULT '0' COMMENT '经度',
  `latitude` varchar(16) NOT NULL DEFAULT '0' COMMENT '纬度',
  `is_default` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否默认',
  `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
  `add_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '添加时间',
  PRIMARY KEY (`id`),
  KEY `uid` (`uid`) USING BTREE,
  KEY `is_default` (`is_default`) USING BTREE,
  KEY `is_del` (`is_del`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户地址表';

-- ----------------------------
-- Table structure for user_auth
-- ----------------------------
DROP TABLE IF EXISTS `user_auth`;
CREATE TABLE `user_auth` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '认证ID',
  `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `auth_type` enum('real_name','skill') NOT NULL COMMENT '认证类型：实名认证，技能认证',
  `auth_data` text NOT NULL COMMENT '认证数据JSON',
  `status` enum('pending','approved','rejected') NOT NULL DEFAULT 'pending' COMMENT '认证状态',
  `reject_reason` varchar(255) DEFAULT NULL COMMENT '拒绝原因',
  `reviewer_id` int(11) NOT NULL DEFAULT '0' COMMENT '审核员ID',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `review_time` int(11) NOT NULL DEFAULT '0' COMMENT '审核时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `auth_type` (`auth_type`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户认证表';

-- ----------------------------
-- Table structure for user_auth_files
-- ----------------------------
DROP TABLE IF EXISTS `user_auth_files`;
CREATE TABLE `user_auth_files` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '文件ID',
  `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `file_type` varchar(50) NOT NULL DEFAULT '' COMMENT '文件类型',
  `file_path` varchar(255) NOT NULL DEFAULT '' COMMENT '文件路径',
  `file_name` varchar(255) NOT NULL DEFAULT '' COMMENT '文件名',
  `file_size` int(11) NOT NULL DEFAULT '0' COMMENT '文件大小',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `file_type` (`file_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户认证文件表';

-- ----------------------------
-- Table structure for user_badges
-- ----------------------------
DROP TABLE IF EXISTS `user_badges`;
CREATE TABLE `user_badges` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `badge_type` varchar(50) NOT NULL COMMENT '徽章类型',
  `badge_name` varchar(100) NOT NULL COMMENT '徽章名称',
  `badge_icon` varchar(100) DEFAULT '' COMMENT '徽章图标',
  `badge_description` varchar(200) DEFAULT '' COMMENT '徽章描述',
  `earned_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '获得时间',
  `is_displayed` tinyint(1) DEFAULT '1' COMMENT '是否显示',
  `sort` int(11) DEFAULT '0' COMMENT '显示排序',
  PRIMARY KEY (`id`),
  KEY `idx_badges_user_id` (`user_id`),
  KEY `idx_badges_type` (`badge_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户徽章表';

-- ----------------------------
-- Table structure for user_bill
-- ----------------------------
DROP TABLE IF EXISTS `user_bill`;
CREATE TABLE `user_bill` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户账单id',
  `uid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户uid',
  `link_id` varchar(32) NOT NULL DEFAULT '0' COMMENT '关联id',
  `pm` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0 = 支出 1 = 获得',
  `title` varchar(64) NOT NULL DEFAULT '' COMMENT '账单标题',
  `category` varchar(64) NOT NULL DEFAULT '' COMMENT '明细种类',
  `type` varchar(64) NOT NULL DEFAULT '' COMMENT '明细类型',
  `number` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '明细数字',
  `balance` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '剩余',
  `mark` varchar(512) NOT NULL DEFAULT '' COMMENT '备注',
  `add_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '添加时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '0 = 带确定 1 = 有效 -1 = 无效',
  `take` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0 = 未收货 1 = 已收货',
  `frozen_time` int(12) NOT NULL DEFAULT '0' COMMENT '积分冻结时间',
  `obtain_time` int(11) NOT NULL DEFAULT '0' COMMENT '获得时间(用于发布帖子时间)',
  PRIMARY KEY (`id`),
  KEY `openid` (`uid`) USING BTREE,
  KEY `status` (`status`) USING BTREE,
  KEY `add_time` (`add_time`) USING BTREE,
  KEY `pm` (`pm`) USING BTREE,
  KEY `type` (`category`,`type`,`link_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户账单表';

-- ----------------------------
-- Table structure for user_enter
-- ----------------------------
DROP TABLE IF EXISTS `user_enter`;
CREATE TABLE `user_enter` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '商户申请ID',
  `uid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `province` varchar(32) NOT NULL DEFAULT '' COMMENT '商户所在省',
  `city` varchar(32) NOT NULL DEFAULT '' COMMENT '商户所在市',
  `district` varchar(32) NOT NULL DEFAULT '' COMMENT '商户所在区',
  `address` varchar(256) NOT NULL DEFAULT '' COMMENT '商户详细地址',
  `merchant_name` varchar(256) NOT NULL DEFAULT '' COMMENT '商户名称',
  `link_user` varchar(32) NOT NULL DEFAULT '',
  `link_tel` varchar(16) NOT NULL DEFAULT '' COMMENT '商户电话',
  `charter` varchar(512) NOT NULL DEFAULT '' COMMENT '商户证书',
  `add_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '添加时间',
  `apply_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '审核时间',
  `success_time` int(11) NOT NULL DEFAULT '0' COMMENT '通过时间',
  `fail_message` varchar(256) NOT NULL DEFAULT '' COMMENT '未通过原因',
  `fail_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '未通过时间',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '-1 审核未通过 0未审核 1审核通过',
  `is_lock` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0 = 开启 1= 关闭',
  `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uid` (`uid`) USING BTREE,
  KEY `province` (`province`,`city`,`district`) USING BTREE,
  KEY `is_lock` (`is_lock`) USING BTREE,
  KEY `is_del` (`is_del`) USING BTREE,
  KEY `status` (`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商户申请表';

-- ----------------------------
-- Table structure for user_extract
-- ----------------------------
DROP TABLE IF EXISTS `user_extract`;
CREATE TABLE `user_extract` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `uid` int(10) unsigned NOT NULL DEFAULT '0',
  `real_name` varchar(64) NOT NULL DEFAULT '' COMMENT '名称',
  `extract_type` varchar(32) NOT NULL DEFAULT 'bank' COMMENT 'bank = 银行卡 alipay = 支付宝wx=微信',
  `bank_code` varchar(32) NOT NULL DEFAULT '0' COMMENT '银行卡',
  `bank_address` varchar(256) NOT NULL DEFAULT '' COMMENT '开户地址',
  `alipay_code` varchar(64) NOT NULL DEFAULT '' COMMENT '支付宝账号',
  `extract_price` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '提现金额',
  `extract_fee` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '手续费金额',
  `mark` varchar(512) NOT NULL DEFAULT '',
  `balance` decimal(12,2) unsigned NOT NULL DEFAULT '0.00',
  `fail_msg` varchar(128) NOT NULL DEFAULT '' COMMENT '无效原因',
  `fail_time` int(10) unsigned NOT NULL DEFAULT '0',
  `add_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '添加时间',
  `status` tinyint(2) NOT NULL DEFAULT '0' COMMENT '-1 未通过 0 审核中 1 已提现',
  `wechat` varchar(15) NOT NULL DEFAULT '' COMMENT '微信号',
  `qrcode_url` varchar(255) NOT NULL DEFAULT '' COMMENT '二维码地址',
  `user_name` varchar(64) NOT NULL DEFAULT '' COMMENT '真实姓名',
  `order_id` varchar(32) NOT NULL DEFAULT '' COMMENT '订单号',
  `wechat_state` varchar(32) NOT NULL DEFAULT '' COMMENT 'v3转账状态码',
  `package_info` varchar(1000) NOT NULL DEFAULT '' COMMENT 'v3转账支付收款页的package',
  `fail_reason` varchar(32) NOT NULL DEFAULT '' COMMENT 'v3转账失败原因',
  `transfer_bill_no` varchar(256) NOT NULL DEFAULT '' COMMENT 'v3微信转账单号',
  `channel_type` varchar(10) NOT NULL DEFAULT '' COMMENT '提现渠道(小程序:routine;公众号:h5;app)',
  PRIMARY KEY (`id`),
  KEY `extract_type` (`extract_type`) USING BTREE,
  KEY `status` (`status`) USING BTREE,
  KEY `add_time` (`add_time`) USING BTREE,
  KEY `openid` (`uid`) USING BTREE,
  KEY `fail_time` (`fail_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户提现表';

-- ----------------------------
-- Table structure for user_feedback
-- ----------------------------
DROP TABLE IF EXISTS `user_feedback`;
CREATE TABLE `user_feedback` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '反馈ID',
  `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `type` enum('bug','suggestion','complaint','other') NOT NULL DEFAULT 'suggestion' COMMENT '反馈类型',
  `title` varchar(255) NOT NULL DEFAULT '' COMMENT '反馈标题',
  `content` text NOT NULL COMMENT '反馈内容',
  `images` text COMMENT '反馈图片JSON',
  `contact_info` varchar(255) DEFAULT NULL COMMENT '联系方式',
  `status` enum('pending','processing','resolved','closed') NOT NULL DEFAULT 'pending' COMMENT '处理状态',
  `admin_reply` text COMMENT '管理员回复',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `type` (`type`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户反馈表';

-- ----------------------------
-- Table structure for user_friends
-- ----------------------------
DROP TABLE IF EXISTS `user_friends`;
CREATE TABLE `user_friends` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `uid` int(10) NOT NULL DEFAULT '0' COMMENT '用户uid',
  `friends_uid` int(10) NOT NULL DEFAULT '0' COMMENT '好友uid',
  `add_time` int(10) NOT NULL DEFAULT '0' COMMENT '添加时间',
  PRIMARY KEY (`id`),
  KEY `uid` (`uid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户好友关系';

-- ----------------------------
-- Table structure for user_group
-- ----------------------------
DROP TABLE IF EXISTS `user_group`;
CREATE TABLE `user_group` (
  `id` smallint(5) unsigned NOT NULL AUTO_INCREMENT,
  `group_name` varchar(64) NOT NULL DEFAULT '' COMMENT '用户分组名称',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户分组表';

-- ----------------------------
-- Table structure for user_income
-- ----------------------------
DROP TABLE IF EXISTS `user_income`;
CREATE TABLE `user_income` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `task_id` int(11) unsigned DEFAULT NULL COMMENT '任务ID',
  `income_type` varchar(20) NOT NULL COMMENT '收入类型(task:任务收入 bonus:奖励 other:其他)',
  `amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '收入金额',
  `platform_fee` decimal(10,2) DEFAULT '0.00' COMMENT '平台费用',
  `actual_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实际到账金额',
  `description` varchar(200) DEFAULT '' COMMENT '收入描述',
  `status` varchar(20) DEFAULT 'pending' COMMENT '状态(pending:待结算 completed:已结算)',
  `settled_at` timestamp NULL DEFAULT NULL COMMENT '结算时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_income_user_id` (`user_id`),
  KEY `idx_income_task_id` (`task_id`),
  KEY `idx_income_type` (`income_type`),
  KEY `idx_income_status` (`status`),
  KEY `idx_income_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户收入记录表';

-- ----------------------------
-- Table structure for user_label
-- ----------------------------
DROP TABLE IF EXISTS `user_label`;
CREATE TABLE `user_label` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '类型：0平台2:供应商',
  `relation_id` int(11) NOT NULL DEFAULT '0' COMMENT '门店、供应商id',
  `label_cate` int(10) NOT NULL DEFAULT '0' COMMENT '标签分类',
  `label_name` varchar(255) NOT NULL DEFAULT '' COMMENT '标签名称',
  `tag_id` varchar(64) NOT NULL DEFAULT '' COMMENT '企业微信同步标签id',
  PRIMARY KEY (`id`),
  KEY `label_cate` (`label_cate`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户标签表';

-- ----------------------------
-- Table structure for user_label_relation
-- ----------------------------
DROP TABLE IF EXISTS `user_label_relation`;
CREATE TABLE `user_label_relation` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '类型：0平台2:供应商',
  `relation_id` int(11) NOT NULL DEFAULT '0' COMMENT '门店、供应商id',
  `label_id` int(11) NOT NULL DEFAULT '0' COMMENT '标签ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户标签关联表';

-- ----------------------------
-- Table structure for user_login_logs
-- ----------------------------
DROP TABLE IF EXISTS `user_login_logs`;
CREATE TABLE `user_login_logs` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `action` varchar(20) NOT NULL COMMENT '操作类型(login/logout/register)',
  `ip` varchar(45) NOT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `remark` varchar(255) DEFAULT '' COMMENT '备注',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `action` (`action`),
  KEY `create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户登录日志表';

-- ----------------------------
-- Table structure for user_money
-- ----------------------------
DROP TABLE IF EXISTS `user_money`;
CREATE TABLE `user_money` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户余额id',
  `uid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户uid',
  `link_id` varchar(32) NOT NULL DEFAULT '0' COMMENT '关联id',
  `type` varchar(64) NOT NULL DEFAULT '' COMMENT '明细类型',
  `title` varchar(64) NOT NULL DEFAULT '' COMMENT '账单标题',
  `number` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '明细数字',
  `balance` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '剩余',
  `pm` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0 = 支出 1 = 获得',
  `mark` varchar(512) NOT NULL DEFAULT '' COMMENT '备注',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '0 = 带确定 1 = 有效 -1 = 无效',
  `add_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '添加时间',
  PRIMARY KEY (`id`),
  KEY `uid` (`uid`) USING BTREE,
  KEY `status` (`status`) USING BTREE,
  KEY `add_time` (`add_time`) USING BTREE,
  KEY `pm` (`pm`) USING BTREE,
  KEY `type` (`type`,`link_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户账单表';

-- ----------------------------
-- Table structure for user_notice
-- ----------------------------
DROP TABLE IF EXISTS `user_notice`;
CREATE TABLE `user_notice` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` text COMMENT '接收消息的用户id（类型：json数据）',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '消息通知类型（1：系统消息；2：用户通知）',
  `user` varchar(20) NOT NULL DEFAULT '' COMMENT '发送人',
  `title` varchar(20) NOT NULL DEFAULT '' COMMENT '通知消息的标题信息',
  `content` varchar(500) NOT NULL DEFAULT '' COMMENT '通知消息的内容',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '通知消息发送的时间',
  `is_send` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否发送（0：未发送；1：已发送）',
  `send_time` int(11) NOT NULL DEFAULT '0' COMMENT '发送时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户通知表';

-- ----------------------------
-- Table structure for user_notice_see
-- ----------------------------
DROP TABLE IF EXISTS `user_notice_see`;
CREATE TABLE `user_notice_see` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nid` int(11) NOT NULL DEFAULT '0' COMMENT '查看的通知id',
  `uid` int(11) NOT NULL DEFAULT '0' COMMENT '查看通知的用户id',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '查看通知的时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户通知发送记录表';

-- ----------------------------
-- Table structure for user_portfolio
-- ----------------------------
DROP TABLE IF EXISTS `user_portfolio`;
CREATE TABLE `user_portfolio` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `title` varchar(100) NOT NULL COMMENT '作品标题',
  `description` text COMMENT '作品描述',
  `images` text COMMENT '作品图片(JSON数组)',
  `task_category_id` int(11) unsigned DEFAULT NULL COMMENT '任务分类ID',
  `completion_date` date DEFAULT NULL COMMENT '完成日期',
  `client_name` varchar(50) DEFAULT '' COMMENT '客户名称(可选)',
  `project_duration` varchar(20) DEFAULT '' COMMENT '项目工期',
  `difficulty_level` tinyint(1) DEFAULT '1' COMMENT '难度等级(1-5)',
  `sort` int(11) DEFAULT '0' COMMENT '排序权重',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态(0:隐藏 1:显示)',
  `views` int(11) DEFAULT '0' COMMENT '查看次数',
  `likes` int(11) DEFAULT '0' COMMENT '点赞次数',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_portfolio_user_id` (`user_id`),
  KEY `idx_portfolio_category` (`task_category_id`),
  KEY `idx_portfolio_status_sort` (`status`,`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户作品集表';

-- ----------------------------
-- Table structure for user_preferences
-- ----------------------------
DROP TABLE IF EXISTS `user_preferences`;
CREATE TABLE `user_preferences` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `language` varchar(10) DEFAULT 'zh-CN' COMMENT '首选语言',
  `currency` varchar(3) DEFAULT 'CNY' COMMENT '首选货币',
  `timezone` varchar(50) DEFAULT 'Asia/Shanghai' COMMENT '时区',
  `date_format` varchar(20) DEFAULT 'Y-m-d' COMMENT '日期格式',
  `notification_language` varchar(10) DEFAULT 'zh-CN' COMMENT '通知语言',
  `ai_recommendations` tinyint(1) DEFAULT '1' COMMENT '是否启用AI推荐',
  `blockchain_features` tinyint(1) DEFAULT '1' COMMENT '是否启用区块链功能',
  `cross_border_services` tinyint(1) DEFAULT '0' COMMENT '是否启用跨境服务',
  `privacy_level` varchar(20) DEFAULT 'standard' COMMENT '隐私级别',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  KEY `idx_language` (`language`),
  KEY `idx_currency` (`currency`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户偏好设置表';

-- ----------------------------
-- Table structure for user_recharge
-- ----------------------------
DROP TABLE IF EXISTS `user_recharge`;
CREATE TABLE `user_recharge` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `store_id` int(10) NOT NULL DEFAULT '0' COMMENT '门店id',
  `uid` int(10) NOT NULL DEFAULT '0' COMMENT '充值用户UID',
  `staff_id` int(10) NOT NULL DEFAULT '0' COMMENT '店员ID',
  `order_id` varchar(32) NOT NULL DEFAULT '' COMMENT '订单号',
  `trade_no` varchar(100) NOT NULL DEFAULT '' COMMENT '微信订单号',
  `price` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '充值金额',
  `give_price` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '购买赠送金额',
  `recharge_type` varchar(32) NOT NULL DEFAULT '' COMMENT '充值类型',
  `auth_code` varchar(50) NOT NULL DEFAULT '' COMMENT '收款码条码值',
  `paid` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否充值',
  `pay_time` int(10) NOT NULL DEFAULT '0' COMMENT '充值支付时间',
  `add_time` int(12) NOT NULL DEFAULT '0' COMMENT '充值时间',
  `refund_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '退款金额',
  `channel_type` varchar(255) NOT NULL DEFAULT '' COMMENT '用户访问端标识',
  `remarks` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_id` (`order_id`) USING BTREE,
  KEY `uid` (`uid`) USING BTREE,
  KEY `recharge_type` (`recharge_type`) USING BTREE,
  KEY `paid` (`paid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户充值表';

-- ----------------------------
-- Table structure for user_relation
-- ----------------------------
DROP TABLE IF EXISTS `user_relation`;
CREATE TABLE `user_relation` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `uid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `relation_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '关联ID',
  `type` varchar(32) NOT NULL DEFAULT '' COMMENT '类型(收藏(collect）、点赞(like))',
  `category` varchar(32) NOT NULL DEFAULT '' COMMENT '某种类型的商品(普通商品、秒杀商品、文章)',
  `add_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '添加时间',
  PRIMARY KEY (`id`),
  KEY `type` (`type`) USING BTREE,
  KEY `category` (`category`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品点赞和收藏表';

-- ----------------------------
-- Table structure for user_search
-- ----------------------------
DROP TABLE IF EXISTS `user_search`;
CREATE TABLE `user_search` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `uid` int(10) NOT NULL DEFAULT '0' COMMENT '用户uid',
  `keyword` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '搜索关键词',
  `vicword` varchar(1000) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '关键词分词',
  `num` int(8) NOT NULL DEFAULT '1' COMMENT '搜索次数',
  `result` text CHARACTER SET utf8 COMMENT '搜索结果',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除',
  `add_time` int(10) NOT NULL DEFAULT '0' COMMENT '时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户搜索记录表';

-- ----------------------------
-- Table structure for user_services
-- ----------------------------
DROP TABLE IF EXISTS `user_services`;
CREATE TABLE `user_services` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `skill_id` int(11) unsigned NOT NULL COMMENT '技能ID',
  `name` varchar(100) NOT NULL COMMENT '服务名称',
  `description` text COMMENT '服务描述',
  `price_range` varchar(50) DEFAULT '' COMMENT '价格区间',
  `unit` varchar(20) DEFAULT '' COMMENT '计价单位',
  `min_price` decimal(10,2) DEFAULT '0.00' COMMENT '最低价格',
  `max_price` decimal(10,2) DEFAULT '0.00' COMMENT '最高价格',
  `service_time` varchar(50) DEFAULT '' COMMENT '服务时长',
  `included_items` text COMMENT '包含项目(JSON)',
  `excluded_items` text COMMENT '不包含项目(JSON)',
  `requirements` text COMMENT '服务要求',
  `sort` int(11) DEFAULT '0' COMMENT '排序权重',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态(0:下架 1:上架)',
  `order_count` int(11) DEFAULT '0' COMMENT '订单数量',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_services_user_id` (`user_id`),
  KEY `idx_services_skill_id` (`skill_id`),
  KEY `idx_services_status_sort` (`status`,`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户服务项目表';

-- ----------------------------
-- Table structure for user_settings
-- ----------------------------
DROP TABLE IF EXISTS `user_settings`;
CREATE TABLE `user_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `notification_task` tinyint(1) NOT NULL DEFAULT '1' COMMENT '任务通知：1开启，0关闭',
  `notification_system` tinyint(1) NOT NULL DEFAULT '1' COMMENT '系统通知：1开启，0关闭',
  `notification_review` tinyint(1) NOT NULL DEFAULT '1' COMMENT '评价通知：1开启，0关闭',
  `work_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '工作状态：1接单中，0暂停接单',
  `auto_accept_distance` int(11) NOT NULL DEFAULT '5' COMMENT '自动接单距离（公里）',
  `preferred_work_time` varchar(100) DEFAULT NULL COMMENT '偏好工作时间',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户设置表';

-- ----------------------------
-- Table structure for user_skills
-- ----------------------------
DROP TABLE IF EXISTS `user_skills`;
CREATE TABLE `user_skills` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `skill_id` int(11) NOT NULL DEFAULT '0' COMMENT '技能ID',
  `level` enum('beginner','intermediate','advanced','expert') NOT NULL DEFAULT 'beginner' COMMENT '技能等级',
  `certificate_url` varchar(255) DEFAULT NULL COMMENT '证书图片',
  `status` enum('pending','approved','rejected') NOT NULL DEFAULT 'pending' COMMENT '认证状态',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `skill_id` (`skill_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户技能表';

-- ----------------------------
-- Table structure for user_tokens
-- ----------------------------
DROP TABLE IF EXISTS `user_tokens`;
CREATE TABLE `user_tokens` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `token` varchar(64) NOT NULL COMMENT 'Token值',
  `expire_time` int(11) unsigned NOT NULL COMMENT '过期时间',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `token` (`token`),
  KEY `user_id` (`user_id`),
  KEY `expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户token表';

-- ----------------------------
-- Table structure for user_visit
-- ----------------------------
DROP TABLE IF EXISTS `user_visit`;
CREATE TABLE `user_visit` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT '0' COMMENT '用户uid',
  `url` varchar(255) NOT NULL DEFAULT '' COMMENT '访问路径',
  `ip` varchar(255) NOT NULL DEFAULT '' COMMENT '用户ip',
  `stay_time` int(11) NOT NULL DEFAULT '0' COMMENT '页面停留时间(秒)',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '访问时间',
  `channel_type` varchar(255) NOT NULL DEFAULT '' COMMENT '用户访问端标识',
  `province` varchar(255) NOT NULL DEFAULT '' COMMENT '用户省份',
  PRIMARY KEY (`id`),
  KEY `time` (`channel_type`,`add_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户访问表';

-- ----------------------------
-- Table structure for user_wallet
-- ----------------------------
DROP TABLE IF EXISTS `user_wallet`;
CREATE TABLE `user_wallet` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `available_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '可用金额',
  `frozen_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '冻结金额',
  `total_income` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总收入',
  `total_withdraw` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总提现',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户钱包表';

-- ----------------------------
-- Table structure for user_withdraw
-- ----------------------------
DROP TABLE IF EXISTS `user_withdraw`;
CREATE TABLE `user_withdraw` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '提现ID',
  `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '提现金额',
  `bank_card` varchar(50) NOT NULL DEFAULT '' COMMENT '银行卡号',
  `bank_name` varchar(100) NOT NULL DEFAULT '' COMMENT '银行名称',
  `account_name` varchar(100) NOT NULL DEFAULT '' COMMENT '账户姓名',
  `status` enum('pending','approved','rejected','completed') NOT NULL DEFAULT 'pending' COMMENT '提现状态',
  `reject_reason` varchar(255) DEFAULT NULL COMMENT '拒绝原因',
  `reviewer_id` int(11) NOT NULL DEFAULT '0' COMMENT '审核员ID',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `review_time` int(11) NOT NULL DEFAULT '0' COMMENT '审核时间',
  `complete_time` int(11) NOT NULL DEFAULT '0' COMMENT '完成时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户提现表';

-- ----------------------------
-- Table structure for websocket_connections
-- ----------------------------
DROP TABLE IF EXISTS `websocket_connections`;
CREATE TABLE `websocket_connections` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `fd` int(11) unsigned NOT NULL COMMENT '连接标识',
  `user_type` varchar(20) NOT NULL DEFAULT '' COMMENT '用户类型',
  `connect_time` int(11) unsigned NOT NULL COMMENT '连接时间',
  `disconnect_time` int(11) unsigned DEFAULT NULL COMMENT '断开时间',
  `ip_address` varchar(45) NOT NULL DEFAULT '' COMMENT 'IP地址',
  `user_agent` varchar(500) NOT NULL DEFAULT '' COMMENT '用户代理',
  `status` enum('connected','disconnected') NOT NULL DEFAULT 'connected' COMMENT '状态',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `fd` (`fd`),
  KEY `connect_time` (`connect_time`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='WebSocket连接记录表';

-- ----------------------------
-- Table structure for wechat_card
-- ----------------------------
DROP TABLE IF EXISTS `wechat_card`;
CREATE TABLE `wechat_card` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `card_id` varchar(50) NOT NULL DEFAULT '' COMMENT '卡券ID（微信返回）',
  `card_type` varchar(20) NOT NULL DEFAULT 'member_card' COMMENT '卡券类型：默认会员卡',
  `code_type` varchar(20) NOT NULL DEFAULT '' COMMENT '码类型',
  `brand_name` varchar(50) NOT NULL DEFAULT '' COMMENT '商户名称',
  `title` varchar(50) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '卡券名称',
  `color` varchar(15) NOT NULL DEFAULT '' COMMENT '颜色',
  `notice` varchar(20) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '卡券使用提醒',
  `description` varchar(255) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '描述',
  `center_title` varchar(255) NOT NULL DEFAULT '' COMMENT '卡券中部居中的按钮，仅在卡券激活后且可用状态 时显示',
  `center_sub_title` varchar(255) NOT NULL DEFAULT '' COMMENT '显示在入口下方的提示语 ， 仅在卡券激活后且可用状态时显示',
  `center_url` varchar(255) NOT NULL DEFAULT '' COMMENT '顶部居中的url ，仅在卡券激活后且可用状态时显示',
  `service_phone` varchar(30) NOT NULL DEFAULT '' COMMENT '联系方式',
  `logo_url` varchar(255) NOT NULL DEFAULT '' COMMENT 'logo URL',
  `background_pic_url` varchar(255) NOT NULL DEFAULT '' COMMENT '背景图',
  `prerogative` text CHARACTER SET utf8mb4 COMMENT '特权说明',
  `especial` longtext CHARACTER SET utf8mb4 COMMENT '特别追加参数',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='微信卡券表';

-- ----------------------------
-- Table structure for wechat_key
-- ----------------------------
DROP TABLE IF EXISTS `wechat_key`;
CREATE TABLE `wechat_key` (
  `id` mediumint(8) NOT NULL AUTO_INCREMENT,
  `reply_id` mediumint(8) NOT NULL DEFAULT '0' COMMENT '回复内容id',
  `keys` varchar(64) NOT NULL DEFAULT '' COMMENT '关键词',
  PRIMARY KEY (`id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COMMENT='微信回复关键词辅助表';

-- ----------------------------
-- Table structure for wechat_media
-- ----------------------------
DROP TABLE IF EXISTS `wechat_media`;
CREATE TABLE `wechat_media` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '微信视频音频id',
  `type` varchar(16) NOT NULL DEFAULT '' COMMENT '回复类型',
  `path` varchar(128) NOT NULL DEFAULT '' COMMENT '文件路径',
  `media_id` varchar(64) NOT NULL DEFAULT '' COMMENT '微信服务器返回的id',
  `url` varchar(256) NOT NULL DEFAULT '' COMMENT '地址',
  `temporary` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否永久或者临时 0永久1临时',
  `add_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '添加时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `type` (`type`,`media_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信回复表';

-- ----------------------------
-- Table structure for wechat_message
-- ----------------------------
DROP TABLE IF EXISTS `wechat_message`;
CREATE TABLE `wechat_message` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户行为记录id',
  `openid` varchar(100) NOT NULL DEFAULT '' COMMENT '用户openid',
  `type` varchar(100) NOT NULL DEFAULT '' COMMENT '操作类型',
  `result` varchar(512) NOT NULL DEFAULT '' COMMENT '操作详细记录',
  `add_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '操作时间',
  PRIMARY KEY (`id`),
  KEY `openid` (`openid`) USING BTREE,
  KEY `type` (`type`) USING BTREE,
  KEY `add_time` (`add_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户行为记录表';

-- ----------------------------
-- Table structure for wechat_news_category
-- ----------------------------
DROP TABLE IF EXISTS `wechat_news_category`;
CREATE TABLE `wechat_news_category` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '图文消息管理ID',
  `cate_name` varchar(255) NOT NULL DEFAULT '' COMMENT '图文名称',
  `sort` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态',
  `new_id` varchar(255) NOT NULL DEFAULT '' COMMENT '文章id',
  `add_time` varchar(255) NOT NULL DEFAULT '' COMMENT '添加时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图文消息管理表';

-- ----------------------------
-- Table structure for wechat_qrcode
-- ----------------------------
DROP TABLE IF EXISTS `wechat_qrcode`;
CREATE TABLE `wechat_qrcode` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `uid` int(11) NOT NULL DEFAULT '0' COMMENT '用户id',
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '二维码名称',
  `image` varchar(500) NOT NULL DEFAULT '' COMMENT '二维码图片',
  `cate_id` int(11) NOT NULL DEFAULT '0' COMMENT '分类id',
  `label_id` varchar(32) NOT NULL DEFAULT '' COMMENT '标签id',
  `type` varchar(32) NOT NULL DEFAULT '' COMMENT '回复类型',
  `content` text COMMENT '回复内容',
  `data` text COMMENT '发送数据',
  `follow` int(11) NOT NULL DEFAULT '0' COMMENT '关注人数',
  `scan` int(11) NOT NULL DEFAULT '0' COMMENT '扫码人数',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
  `continue_time` int(11) NOT NULL DEFAULT '0' COMMENT '有效期',
  `end_time` int(11) NOT NULL DEFAULT '0' COMMENT '到期时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='二维码表';

-- ----------------------------
-- Table structure for wechat_qrcode_cate
-- ----------------------------
DROP TABLE IF EXISTS `wechat_qrcode_cate`;
CREATE TABLE `wechat_qrcode_cate` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `cate_name` varchar(255) NOT NULL DEFAULT '' COMMENT '渠道码分类名称',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8 COMMENT='二维码类型表';

-- ----------------------------
-- Table structure for wechat_qrcode_record
-- ----------------------------
DROP TABLE IF EXISTS `wechat_qrcode_record`;
CREATE TABLE `wechat_qrcode_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `qid` int(11) NOT NULL DEFAULT '0' COMMENT '渠道码id',
  `uid` int(11) NOT NULL DEFAULT '0' COMMENT '用户id',
  `is_follow` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否关注',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '扫码时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='渠道码扫码记录表';

-- ----------------------------
-- Table structure for wechat_reply
-- ----------------------------
DROP TABLE IF EXISTS `wechat_reply`;
CREATE TABLE `wechat_reply` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT COMMENT '微信关键字回复id',
  `type` varchar(32) NOT NULL DEFAULT '' COMMENT '回复类型',
  `data` text COMMENT '回复数据',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '0=不可用  1 =可用',
  `hide` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否隐藏',
  PRIMARY KEY (`id`),
  KEY `type` (`type`) USING BTREE,
  KEY `status` (`status`) USING BTREE,
  KEY `hide` (`hide`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信关键字回复表';

-- ----------------------------
-- Table structure for wechat_user
-- ----------------------------
DROP TABLE IF EXISTS `wechat_user`;
CREATE TABLE `wechat_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '微信用户id',
  `unionid` varchar(30) NOT NULL DEFAULT '' COMMENT '只有在用户将公众号绑定到微信开放平台帐号后，才会出现该字段',
  `openid` varchar(100) NOT NULL DEFAULT '' COMMENT '用户的标识，对当前公众号唯一',
  `nickname` varchar(64) NOT NULL DEFAULT '' COMMENT '用户的昵称',
  `headimgurl` varchar(256) NOT NULL DEFAULT '' COMMENT '用户头像',
  `sex` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '用户的性别，值为1时是男性，值为2时是女性，值为0时是未知',
  `city` varchar(64) NOT NULL DEFAULT '' COMMENT '用户所在城市',
  `language` varchar(64) NOT NULL DEFAULT '' COMMENT '用户的语言，简体中文为zh_CN',
  `province` varchar(64) NOT NULL DEFAULT '' COMMENT '用户所在省份',
  `country` varchar(64) NOT NULL DEFAULT '' COMMENT '用户所在国家',
  `remark` varchar(256) NOT NULL DEFAULT '' COMMENT '公众号运营者对粉丝的备注，公众号运营者可在微信公众平台用户管理界面对粉丝添加备注',
  `groupid` smallint(5) unsigned NOT NULL DEFAULT '0' COMMENT '用户所在的分组ID（兼容旧的用户分组接口）',
  `tagid_list` varchar(256) NOT NULL DEFAULT '' COMMENT '用户被打上的标签ID列表',
  `subscribe` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '用户是否订阅该公众号标识',
  `subscribe_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '关注公众号时间',
  `add_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '添加时间',
  `second` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '二级推荐人',
  `user_type` varchar(32) NOT NULL DEFAULT 'wechat' COMMENT '用户类型',
  `is_complete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '授权信息是否完整',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `openid` (`openid`) USING BTREE,
  KEY `groupid` (`groupid`) USING BTREE,
  KEY `subscribe_time` (`subscribe_time`) USING BTREE,
  KEY `add_time` (`add_time`) USING BTREE,
  KEY `subscribe` (`subscribe`) USING BTREE,
  KEY `unionid` (`unionid`) USING BTREE,
  KEY `uid` (`uid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信用户表';

-- ----------------------------
-- Table structure for worker_experience
-- ----------------------------
DROP TABLE IF EXISTS `worker_experience`;
CREATE TABLE `worker_experience` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '经历ID',
  `worker_id` int(11) NOT NULL DEFAULT '0' COMMENT '师傅ID',
  `company` varchar(100) NOT NULL DEFAULT '' COMMENT '公司名称',
  `position` varchar(50) NOT NULL DEFAULT '' COMMENT '职位',
  `start_time` varchar(20) NOT NULL DEFAULT '' COMMENT '开始时间',
  `end_time` varchar(20) NOT NULL DEFAULT '' COMMENT '结束时间',
  `description` text COMMENT '工作描述',
  `images` json DEFAULT NULL COMMENT '工作图片JSON',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `worker_id` (`worker_id`),
  KEY `start_time` (`start_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='师傅工作经历表';

-- ----------------------------
-- Table structure for worker_skills
-- ----------------------------
DROP TABLE IF EXISTS `worker_skills`;
CREATE TABLE `worker_skills` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '技能ID',
  `worker_id` int(11) NOT NULL DEFAULT '0' COMMENT '师傅ID',
  `skill_tags` json DEFAULT NULL COMMENT '技能标签JSON',
  `service_areas` json DEFAULT NULL COMMENT '服务区域JSON',
  `experience_years` tinyint(3) NOT NULL DEFAULT '0' COMMENT '工作经验年数',
  `description` text COMMENT '技能描述',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `worker_id` (`worker_id`),
  KEY `experience_years` (`experience_years`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='师傅技能表';
