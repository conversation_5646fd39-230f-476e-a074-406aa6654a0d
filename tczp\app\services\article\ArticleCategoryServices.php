<?php

namespace app\services\article;

use app\dao\article\ArticleCategoryDao;
use app\services\BaseServices;
use app\services\wechat\WechatNewsCategoryServices;
use crmeb\exceptions\AdminException;
use crmeb\services\FormBuilder as Form;
use think\annotation\Inject;
use think\facade\Route as Url;

/**
 * 文章分类
 * Class ArticleCategoryServices
 * @package app\services\article
 * @mixin ArticleCategoryDao
 */
class ArticleCategoryServices extends BaseServices
{
    /**
     * @var ArticleCategoryDao
     */
    #[Inject]
    protected ArticleCategoryDao $dao;

    /**
     * 获取文章分类列表
     * @param array $where
     * @return array
     */
    public function getList(array $where)
    {
        [$page, $limit] = $this->getPageValue();
        $list = $this->dao->getList($where, $page, $limit);
        $count = $this->dao->count($where);
        return compact('list', 'count');
    }

    /**
     * 生成创建修改表单
     * @param int $id
     * @return array
     * @throws \FormBuilder\Exception\FormBuilderException
     */
    public function createForm(int $id)
    {
        $method = 'POST';
        $url = '/cms/category';
        $title = '添加分类';
        if ($id) {
            $info = $this->dao->get($id);
            $method = 'PUT';
            $url = $url . '/' . $id;
            $title = '修改分类';
        }
        $f = array();
        $f[] = Form::hidden('id', $info['id'] ?? 0);
        $f[] = Form::input('title', '分类名称', $info['title'] ?? '')->maxlength(20)->required();
        $f[] = Form::input('intr', '分类简介', $info['intr'] ?? '')->type('textarea')->required();
        $f[] = Form::frameImage('image', '分类图片', Url::buildUrl('admin/widget.images/index', array('fodder' => 'image')), $info['image'] ?? '')->icon('ios-add')->width('960px')->height('505px')->modal(['footer-hide' => true]);
        $f[] = Form::number('sort', '排序', (int)($info['sort'] ?? 0))->min(0);
        $f[] = Form::radio('status', '状态', $info['status'] ?? 1)->options([['value' => 1, 'label' => '显示'], ['value' => 0, 'label' => '隐藏']]);
        return create_form($title, $f, Url::buildUrl($url), $method);
    }

    /**
     * 保存
     * @param array $data
     */
    public function save(array $data)
    {
        $this->dao->save($data);
    }

    /**
     * 修改
     * @param array $data
     */
    public function update(array $data)
    {
        $this->dao->update($data['id'], $data);
    }

    /**
     * 删除
     * @param int $id
     */
    public function del(int $id)
    {
        /** @var WechatNewsCategoryServices $services */
		$services = app()->make(WechatNewsCategoryServices::class);
		$ids = $services->getNewIds();
        /** @var ArticleServices $articleService */
        $articleService = app()->make(ArticleServices::class);
        $count = $articleService->count(['cid' => $id, 'ids' => $ids]);
        if ($count > 0) {
            throw new AdminException('该分类下有文章，无法删除！');
        } else {
            $this->dao->delete($id);
        }
    }

    /**
     * 修改状态
     * @param int $id
     * @param int $status
     */
    public function setStatus(int $id, int $status)
    {
        $this->dao->update($id, ['status' => $status]);
    }
}
