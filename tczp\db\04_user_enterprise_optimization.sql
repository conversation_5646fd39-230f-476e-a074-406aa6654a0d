-- ==================== 用户端（企业端）数据库优化 ====================
-- 文件: 04_user_enterprise_optimization.sql
-- 描述: 用户端（企业端）系统优化所需的数据库结构
-- 执行顺序: 第四步执行（在师傅端优化之后）
-- 作者: TCZP Team
-- 日期: 2024-12-19

-- 设置时间变量
SET @current_time = UNIX_TIMESTAMP();

-- ==================== 1. 企业信息表 ====================
DROP TABLE IF EXISTS `eb_enterprise_profiles`;
CREATE TABLE `eb_enterprise_profiles` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `enterprise_no` varchar(32) NOT NULL COMMENT '企业编号',
  `enterprise_name` varchar(200) NOT NULL COMMENT '企业名称',
  `enterprise_type` enum('individual','company','government','institution') NOT NULL DEFAULT 'company' COMMENT '企业类型',
  `legal_person` varchar(50) NOT NULL COMMENT '法人代表',
  `contact_person` varchar(50) NOT NULL COMMENT '联系人',
  `contact_phone` varchar(15) NOT NULL COMMENT '联系电话',
  `contact_email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
  `business_license` varchar(50) NOT NULL COMMENT '营业执照号',
  `license_images` json DEFAULT NULL COMMENT '营业执照图片JSON',
  `enterprise_address` varchar(500) NOT NULL COMMENT '企业地址',
  `business_scope` text DEFAULT NULL COMMENT '经营范围',
  `registered_capital` decimal(15,2) DEFAULT NULL COMMENT '注册资本',
  `establishment_date` int(11) DEFAULT NULL COMMENT '成立时间',
  `credit_code` varchar(50) DEFAULT NULL COMMENT '统一社会信用代码',
  `tax_number` varchar(50) DEFAULT NULL COMMENT '税号',
  `bank_account` varchar(50) DEFAULT NULL COMMENT '银行账户',
  `bank_name` varchar(100) DEFAULT NULL COMMENT '开户银行',
  `auth_status` enum('pending','reviewing','approved','rejected') NOT NULL DEFAULT 'pending' COMMENT '认证状态',
  `auth_time` int(11) NOT NULL DEFAULT '0' COMMENT '认证时间',
  `reviewer_id` int(11) NOT NULL DEFAULT '0' COMMENT '审核员ID',
  `reject_reason` varchar(500) DEFAULT NULL COMMENT '拒绝原因',
  `credit_rating` enum('A','B','C','D') DEFAULT NULL COMMENT '信用等级',
  `credit_score` int(11) NOT NULL DEFAULT '100' COMMENT '信用分数',
  `task_count` int(11) NOT NULL DEFAULT '0' COMMENT '发布任务数',
  `completed_count` int(11) NOT NULL DEFAULT '0' COMMENT '完成任务数',
  `total_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '总交易金额',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  UNIQUE KEY `enterprise_no` (`enterprise_no`),
  UNIQUE KEY `business_license` (`business_license`),
  KEY `auth_status` (`auth_status`),
  KEY `enterprise_type` (`enterprise_type`),
  KEY `credit_rating` (`credit_rating`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业信息表';

-- ==================== 2. 企业认证记录表 ====================
DROP TABLE IF EXISTS `eb_enterprise_auth_records`;
CREATE TABLE `eb_enterprise_auth_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `enterprise_id` int(11) NOT NULL COMMENT '企业ID',
  `auth_type` enum('basic','qualification','credit') NOT NULL COMMENT '认证类型',
  `auth_data` json NOT NULL COMMENT '认证数据JSON',
  `status` enum('pending','reviewing','approved','rejected','expired') NOT NULL DEFAULT 'pending' COMMENT '认证状态',
  `submit_time` int(11) NOT NULL DEFAULT '0' COMMENT '提交时间',
  `review_time` int(11) NOT NULL DEFAULT '0' COMMENT '审核时间',
  `reviewer_id` int(11) NOT NULL DEFAULT '0' COMMENT '审核员ID',
  `reject_reason` varchar(500) DEFAULT NULL COMMENT '拒绝原因',
  `expire_time` int(11) NOT NULL DEFAULT '0' COMMENT '过期时间',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `enterprise_id` (`enterprise_id`),
  KEY `auth_type` (`auth_type`),
  KEY `status` (`status`),
  KEY `submit_time` (`submit_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业认证记录表';

-- ==================== 3. 任务模板表 ====================
DROP TABLE IF EXISTS `eb_task_templates`;
CREATE TABLE `eb_task_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `template_data` json NOT NULL COMMENT '模板数据JSON',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `use_count` int(11) NOT NULL DEFAULT '0' COMMENT '使用次数',
  `is_public` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否公开',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `category_id` (`category_id`),
  KEY `is_public` (`is_public`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务模板表';

-- ==================== 4. 师傅收藏表 ====================
DROP TABLE IF EXISTS `eb_worker_favorites`;
CREATE TABLE `eb_worker_favorites` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `worker_id` int(11) NOT NULL COMMENT '师傅ID',
  `tags` varchar(200) DEFAULT NULL COMMENT '标签',
  `notes` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_worker` (`user_id`, `worker_id`),
  KEY `user_id` (`user_id`),
  KEY `worker_id` (`worker_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='师傅收藏表';

-- ==================== 5. 任务评价表 ====================
DROP TABLE IF EXISTS `eb_task_reviews`;
CREATE TABLE `eb_task_reviews` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `task_id` int(11) NOT NULL COMMENT '任务ID',
  `user_id` int(11) NOT NULL COMMENT '评价用户ID',
  `worker_id` int(11) NOT NULL COMMENT '被评价师傅ID',
  `rating` tinyint(1) NOT NULL DEFAULT '5' COMMENT '评分1-5',
  `service_rating` tinyint(1) NOT NULL DEFAULT '5' COMMENT '服务评分',
  `quality_rating` tinyint(1) NOT NULL DEFAULT '5' COMMENT '质量评分',
  `speed_rating` tinyint(1) NOT NULL DEFAULT '5' COMMENT '速度评分',
  `review_content` text DEFAULT NULL COMMENT '评价内容',
  `review_images` json DEFAULT NULL COMMENT '评价图片JSON',
  `reply_content` text DEFAULT NULL COMMENT '回复内容',
  `reply_time` int(11) NOT NULL DEFAULT '0' COMMENT '回复时间',
  `is_anonymous` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否匿名',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `task_id` (`task_id`),
  KEY `user_id` (`user_id`),
  KEY `worker_id` (`worker_id`),
  KEY `rating` (`rating`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务评价表';

-- ==================== 6. 支付订单表 ====================
DROP TABLE IF EXISTS `eb_payment_orders`;
CREATE TABLE `eb_payment_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单号',
  `task_id` int(11) NOT NULL COMMENT '任务ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `worker_id` int(11) NOT NULL COMMENT '师傅ID',
  `payment_type` enum('deposit','final','full') NOT NULL COMMENT '支付类型',
  `amount` decimal(10,2) NOT NULL COMMENT '支付金额',
  `platform_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '平台费用',
  `actual_amount` decimal(10,2) NOT NULL COMMENT '实际金额',
  `payment_method` varchar(20) NOT NULL COMMENT '支付方式',
  `payment_channel` varchar(50) DEFAULT NULL COMMENT '支付渠道',
  `transaction_id` varchar(100) DEFAULT NULL COMMENT '第三方交易号',
  `status` enum('pending','paid','failed','refunded','cancelled') NOT NULL DEFAULT 'pending' COMMENT '支付状态',
  `pay_time` int(11) NOT NULL DEFAULT '0' COMMENT '支付时间',
  `refund_time` int(11) NOT NULL DEFAULT '0' COMMENT '退款时间',
  `refund_reason` varchar(200) DEFAULT NULL COMMENT '退款原因',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `task_id` (`task_id`),
  KEY `user_id` (`user_id`),
  KEY `worker_id` (`worker_id`),
  KEY `status` (`status`),
  KEY `payment_type` (`payment_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付订单表';

-- ==================== 7. 结算记录表 ====================
DROP TABLE IF EXISTS `eb_settlement_records`;
CREATE TABLE `eb_settlement_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `settlement_no` varchar(32) NOT NULL COMMENT '结算单号',
  `task_id` int(11) NOT NULL COMMENT '任务ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `worker_id` int(11) NOT NULL COMMENT '师傅ID',
  `total_amount` decimal(10,2) NOT NULL COMMENT '总金额',
  `platform_fee` decimal(10,2) NOT NULL COMMENT '平台费用',
  `worker_amount` decimal(10,2) NOT NULL COMMENT '师傅收入',
  `tax_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '税费',
  `actual_amount` decimal(10,2) NOT NULL COMMENT '实际结算金额',
  `settlement_type` enum('auto','manual') NOT NULL DEFAULT 'auto' COMMENT '结算类型',
  `status` enum('pending','processing','completed','failed') NOT NULL DEFAULT 'pending' COMMENT '结算状态',
  `settlement_time` int(11) NOT NULL DEFAULT '0' COMMENT '结算时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `settlement_no` (`settlement_no`),
  KEY `task_id` (`task_id`),
  KEY `user_id` (`user_id`),
  KEY `worker_id` (`worker_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='结算记录表';

-- ==================== 8. 优化现有任务表 ====================
-- 为任务表添加企业相关字段
ALTER TABLE `eb_tasks` 
ADD COLUMN `enterprise_id` int(11) DEFAULT NULL COMMENT '企业ID' AFTER `user_id`,
ADD COLUMN `task_type` enum('simple','complex','urgent','batch') NOT NULL DEFAULT 'simple' COMMENT '任务类型' AFTER `task_category_id`,
ADD COLUMN `payment_type` enum('hourly','fixed','negotiable') NOT NULL DEFAULT 'hourly' COMMENT '付费类型' AFTER `hourly_rate`,
ADD COLUMN `deposit_rate` decimal(5,2) NOT NULL DEFAULT '20.00' COMMENT '定金比例' AFTER `payment_type`,
ADD COLUMN `contact_person` varchar(50) DEFAULT NULL COMMENT '联系人' AFTER `task_location`,
ADD COLUMN `contact_phone` varchar(15) DEFAULT NULL COMMENT '联系电话' AFTER `contact_person`,
ADD COLUMN `preferred_workers` json DEFAULT NULL COMMENT '优选师傅JSON' AFTER `worker_requirements`,
ADD COLUMN `insurance_required` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否需要保险' AFTER `preferred_workers`,
ADD COLUMN `contract_required` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否需要合同' AFTER `insurance_required`;

-- 添加索引
ALTER TABLE `eb_tasks` ADD INDEX `idx_enterprise_id` (`enterprise_id`);
ALTER TABLE `eb_tasks` ADD INDEX `idx_task_type` (`task_type`);
ALTER TABLE `eb_tasks` ADD INDEX `idx_payment_type` (`payment_type`);

-- ==================== 9. 优化用户表 ====================
-- 为用户表添加企业相关字段
ALTER TABLE `eb_user`
ADD COLUMN `user_level` tinyint(1) NOT NULL DEFAULT '1' COMMENT '用户等级' AFTER `user_type`,
ADD COLUMN `credit_score` int(11) NOT NULL DEFAULT '100' COMMENT '信用分数' AFTER `user_level`,
ADD COLUMN `total_spent` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '总消费金额' AFTER `credit_score`,
ADD COLUMN `task_published` int(11) NOT NULL DEFAULT '0' COMMENT '发布任务数' AFTER `total_spent`,
ADD COLUMN `task_completed` int(11) NOT NULL DEFAULT '0' COMMENT '完成任务数' AFTER `task_published`,
ADD COLUMN `last_active_time` int(11) NOT NULL DEFAULT '0' COMMENT '最后活跃时间' AFTER `task_completed`;

-- 添加索引
ALTER TABLE `eb_user` ADD INDEX `idx_user_level` (`user_level`);
ALTER TABLE `eb_user` ADD INDEX `idx_credit_score` (`credit_score`);

-- ==================== 10. 创建视图 ====================
-- 企业完整信息视图
CREATE OR REPLACE VIEW `eb_enterprise_complete_info` AS
SELECT 
    ep.id,
    ep.user_id,
    ep.enterprise_no,
    ep.enterprise_name,
    ep.enterprise_type,
    ep.contact_person,
    ep.contact_phone,
    ep.auth_status,
    ep.credit_rating,
    ep.credit_score,
    ep.task_count,
    ep.completed_count,
    ep.total_amount,
    u.nickname,
    u.avatar,
    u.add_time as register_time,
    u.user_level,
    u.total_spent,
    ROUND((ep.completed_count / NULLIF(ep.task_count, 0)) * 100, 2) as completion_rate
FROM eb_enterprise_profiles ep
LEFT JOIN eb_user u ON ep.user_id = u.uid
WHERE ep.auth_status = 'approved';

-- ==================== 完成提示 ====================
SELECT 'User enterprise optimization completed successfully!' as message;
