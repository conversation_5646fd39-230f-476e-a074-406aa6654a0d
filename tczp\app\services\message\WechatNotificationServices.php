<?php
declare (strict_types = 1);

namespace app\services\message;

use app\services\BaseServices;
use app\services\wechat\WechatServices;
use think\facade\Log;
use think\facade\Cache;

/**
 * 微信公众号通知服务
 * Class WechatNotificationServices
 * @package app\services\message
 */
class WechatNotificationServices extends BaseServices
{
    /**
     * 微信模板消息配置
     */
    const WECHAT_TEMPLATES = [
        // 任务发布成功
        'TASK_PUBLISHED' => [
            'template_id' => 'OPENTM207791515',
            'data' => [
                'first' => '您的任务已成功发布！',
                'keyword1' => '{task_title}',      // 任务标题
                'keyword2' => '{publish_time}',    // 发布时间
                'keyword3' => '{task_address}',    // 任务地址
                'remark' => '等待师傅申请，我们会及时通知您。'
            ],
            'url' => '/pages/task/detail?id={task_id}',
            'color' => '#173177'
        ],
        
        // 师傅申请任务
        'TASK_APPLIED' => [
            'template_id' => 'OPENTM207791516',
            'data' => [
                'first' => '有师傅申请了您的任务！',
                'keyword1' => '{worker_name}',     // 师傅姓名
                'keyword2' => '{task_title}',      // 任务标题
                'keyword3' => '{apply_time}',      // 申请时间
                'keyword4' => '{worker_rating}',   // 师傅评分
                'remark' => '请及时查看师傅信息并确认。'
            ],
            'url' => '/pages/task/applications?id={task_id}',
            'color' => '#FF6B35'
        ],
        
        // 任务开始执行
        'TASK_STARTED' => [
            'template_id' => 'OPENTM207791517',
            'data' => [
                'first' => '师傅已开始执行您的任务！',
                'keyword1' => '{task_title}',      // 任务标题
                'keyword2' => '{worker_name}',     // 师傅姓名
                'keyword3' => '{start_time}',      // 开始时间
                'keyword4' => '{estimated_time}',  // 预计完成时间
                'remark' => '师傅正在为您服务，请保持电话畅通。'
            ],
            'url' => '/pages/task/progress?id={task_id}',
            'color' => '#34C759'
        ],
        
        // 任务完成
        'TASK_COMPLETED' => [
            'template_id' => 'OPENTM207791519',
            'data' => [
                'first' => '任务已完成！',
                'keyword1' => '{task_title}',      // 任务标题
                'keyword2' => '{worker_name}',     // 师傅姓名
                'keyword3' => '{complete_time}',   // 完成时间
                'keyword4' => '{total_amount}',    // 总金额
                'remark' => '请确认任务完成情况并进行评价。'
            ],
            'url' => '/pages/task/confirm?id={task_id}',
            'color' => '#34C759'
        ],
        
        // 支付成功
        'PAYMENT_SUCCESS' => [
            'template_id' => 'OPENTM207791520',
            'data' => [
                'first' => '支付成功！',
                'keyword1' => '{task_title}',      // 任务标题
                'keyword2' => '{amount}',          // 支付金额
                'keyword3' => '{payment_time}',    // 支付时间
                'keyword4' => '{payment_method}',  // 支付方式
                'remark' => '感谢您的使用，期待为您提供更好的服务。'
            ],
            'url' => '/pages/order/detail?id={order_id}',
            'color' => '#FF9500'
        ],
        
        // 师傅端 - 新任务通知
        'NEW_TASK_AVAILABLE' => [
            'template_id' => 'OPENTM207791521',
            'data' => [
                'first' => '附近有新任务！',
                'keyword1' => '{task_title}',      // 任务标题
                'keyword2' => '{task_address}',    // 任务地址
                'keyword3' => '{distance}',        // 距离
                'keyword4' => '{task_price}',      // 任务价格
                'remark' => '快来抢单吧！先到先得。'
            ],
            'url' => '/pages/task/detail?id={task_id}',
            'color' => '#FF6B35'
        ],
        
        // 师傅端 - 任务分配
        'TASK_ASSIGNED' => [
            'template_id' => 'OPENTM207791522',
            'data' => [
                'first' => '您有新的任务！',
                'keyword1' => '{task_title}',      // 任务标题
                'keyword2' => '{user_name}',       // 用户姓名
                'keyword3' => '{task_address}',    // 任务地址
                'keyword4' => '{contact_phone}',   // 联系电话
                'remark' => '请及时联系客户确认服务时间。'
            ],
            'url' => '/pages/task/detail?id={task_id}',
            'color' => '#34C759'
        ]
    ];

    /**
     * 发送微信模板消息
     * @param string $template_key 模板键名
     * @param int $user_id 用户ID
     * @param array $data 数据
     * @param array $options 选项
     * @return bool
     */
    public function sendTemplateMessage(string $template_key, int $user_id, array $data, array $options = []): bool
    {
        try {
            // 检查模板是否存在
            if (!isset(self::WECHAT_TEMPLATES[$template_key])) {
                Log::error("未知的微信模板: {$template_key}");
                return false;
            }

            $template = self::WECHAT_TEMPLATES[$template_key];
            
            // 获取用户openid
            $openid = $this->getUserOpenid($user_id);
            if (!$openid) {
                Log::warning("用户{$user_id}未绑定微信，无法发送模板消息");
                return false;
            }

            // 构建模板数据
            $templateData = $this->buildTemplateData($template['data'], $data);
            
            // 构建跳转链接
            $url = $this->buildTemplateUrl($template['url'], $data);
            
            // 发送模板消息
            $result = $this->sendWechatTemplate(
                $openid,
                $template['template_id'],
                $templateData,
                $url,
                $options['color'] ?? $template['color']
            );

            if ($result) {
                Log::info("微信模板消息发送成功", [
                    'template_key' => $template_key,
                    'user_id' => $user_id,
                    'openid' => $openid
                ]);
                return true;
            } else {
                Log::error("微信模板消息发送失败", [
                    'template_key' => $template_key,
                    'user_id' => $user_id
                ]);
                return false;
            }

        } catch (\Exception $e) {
            Log::error("发送微信模板消息异常: {$e->getMessage()}", [
                'template_key' => $template_key,
                'user_id' => $user_id,
                'data' => $data
            ]);
            return false;
        }
    }

    /**
     * 发送客服消息
     * @param int $user_id 用户ID
     * @param string $content 消息内容
     * @param string $msg_type 消息类型
     * @return bool
     */
    public function sendCustomerMessage(int $user_id, string $content, string $msg_type = 'text'): bool
    {
        try {
            $openid = $this->getUserOpenid($user_id);
            if (!$openid) {
                return false;
            }

            /** @var WechatServices $wechatService */
            $wechatService = app()->make(WechatServices::class);
            
            $message = [
                'touser' => $openid,
                'msgtype' => $msg_type,
                $msg_type => [
                    'content' => $content
                ]
            ];

            return $wechatService->sendCustomMessage($message);

        } catch (\Exception $e) {
            Log::error("发送客服消息失败: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * 获取用户openid
     * @param int $user_id
     * @return string|null
     */
    private function getUserOpenid(int $user_id): ?string
    {
        // 从缓存或数据库获取用户openid
        $cacheKey = "user_openid_{$user_id}";
        $openid = Cache::get($cacheKey);
        
        if (!$openid) {
            // 从数据库查询
            $userInfo = app()->make(\app\services\user\UserServices::class)->getUserInfo($user_id);
            $openid = $userInfo['openid'] ?? null;
            
            if ($openid) {
                Cache::set($cacheKey, $openid, 3600); // 缓存1小时
            }
        }
        
        return $openid;
    }

    /**
     * 构建模板数据
     * @param array $template
     * @param array $data
     * @return array
     */
    private function buildTemplateData(array $template, array $data): array
    {
        $result = [];
        
        foreach ($template as $key => $value) {
            if (is_string($value) && strpos($value, '{') !== false) {
                // 替换变量
                $result[$key] = [
                    'value' => $this->replaceVariables($value, $data),
                    'color' => '#173177'
                ];
            } else {
                $result[$key] = [
                    'value' => $value,
                    'color' => '#173177'
                ];
            }
        }
        
        return $result;
    }

    /**
     * 构建模板URL
     * @param string $url
     * @param array $data
     * @return string
     */
    private function buildTemplateUrl(string $url, array $data): string
    {
        return $this->replaceVariables($url, $data);
    }

    /**
     * 替换变量
     * @param string $template
     * @param array $data
     * @return string
     */
    private function replaceVariables(string $template, array $data): string
    {
        foreach ($data as $key => $value) {
            $template = str_replace('{' . $key . '}', (string)$value, $template);
        }
        return $template;
    }

    /**
     * 发送微信模板消息
     * @param string $openid
     * @param string $template_id
     * @param array $data
     * @param string $url
     * @param string $color
     * @return bool
     */
    private function sendWechatTemplate(string $openid, string $template_id, array $data, string $url, string $color): bool
    {
        try {
            /** @var WechatServices $wechatService */
            $wechatService = app()->make(WechatServices::class);
            
            $templateData = [
                'touser' => $openid,
                'template_id' => $template_id,
                'url' => $url,
                'topcolor' => $color,
                'data' => $data
            ];

            return $wechatService->sendTemplateMessage($templateData);

        } catch (\Exception $e) {
            Log::error("调用微信API发送模板消息失败: {$e->getMessage()}");
            return false;
        }
    }
}
