<?php
declare (strict_types=1);

namespace app\services\user;

use app\services\BaseServices;
use app\dao\user\UserVisitDao;
use think\annotation\Inject;
use think\facade\Log;

/**
 *
 * Class UserVisitServices
 * @package app\services\user
 * @mixin UserVisitDao
 */
class UserVisitServices extends BaseServices
{

    /**
     * @var UserVisitDao
     */
    #[Inject]
    protected UserVisitDao $dao;

    /**
     * 登录后记录访问记录
     * @param array|object $user
     * @return mixed
     */
    public function loginSaveVisit($user)
    {
        try {
            $data = [
                'url' => '/pages/index/index',
                'uid' => $user['uid'] ?? 0,
                'ip' => request()->ip(),
                'add_time' => time(),
                'province' => $user['province'] ?? '',
                'channel_type' => $user['user_type'] ?? 'h5'
            ];
            if (!$data['uid']) {
                return false;
            }
            return $this->dao->save($data);
        } catch (\Throwable $e) {
            Log::error('登录记录访问日志错误，错误原因：' . $e->getMessage());
        }
    }

}
