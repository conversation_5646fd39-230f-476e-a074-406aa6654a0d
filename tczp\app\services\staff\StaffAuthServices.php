<?php
declare(strict_types=1);

namespace app\services\staff;

use app\services\BaseServices;
use think\facade\Db;
use think\facade\Cache;

/**
 * 师傅认证服务类
 * Class StaffAuthServices
 * @package app\services\staff
 */
class StaffAuthServices extends BaseServices
{
    /**
     * 检查用户是否为师傅
     * @param int $uid
     * @return bool
     */
    public function isWorker(int $uid): bool
    {
        // 检查用户角色
        $hasWorkerRole = Db::name('user_role_relations')
            ->alias('urr')
            ->join('user_roles ur', 'urr.role_id = ur.id')
            ->where('urr.user_id', $uid)
            ->where('ur.role_code', 'worker')
            ->where('urr.status', 1)
            ->count() > 0;
            
        return $hasWorkerRole;
    }
    
    /**
     * 获取师傅信息
     * @param int $uid
     * @return array|null
     */
    public function getWorkerInfo(int $uid): ?array
    {
        $cacheKey = "worker_info_{$uid}";
        
        return Cache::remember($cacheKey, function () use ($uid) {
            return Db::name('worker_profiles')
                ->where('user_id', $uid)
                ->find();
        }, 300);
    }
    
    /**
     * 获取师傅权限
     * @param int $uid
     * @return array
     */
    public function getWorkerPermissions(int $uid): array
    {
        $cacheKey = "worker_permissions_{$uid}";
        
        return Cache::remember($cacheKey, function () use ($uid) {
            // 获取角色权限
            $rolePermissions = Db::name('user_role_relations')
                ->alias('urr')
                ->join('user_roles ur', 'urr.role_id = ur.id')
                ->where('urr.user_id', $uid)
                ->where('urr.status', 1)
                ->column('ur.permissions');
                
            $permissions = [];
            foreach ($rolePermissions as $rolePermission) {
                $perms = json_decode($rolePermission, true) ?: [];
                $permissions = array_merge($permissions, $perms);
            }
            
            return array_unique($permissions);
        }, 300);
    }
    
    /**
     * 创建师傅档案
     * @param int $uid
     * @param array $data
     * @return bool
     */
    public function createWorkerProfile(int $uid, array $data): bool
    {
        try {
            Db::startTrans();
            
            // 生成师傅编号
            $workerNo = $this->generateWorkerNo();
            
            // 创建师傅档案
            $profileData = [
                'user_id' => $uid,
                'worker_no' => $workerNo,
                'real_name' => $data['real_name'] ?? '',
                'id_card' => $data['id_card'] ?? '',
                'phone' => $data['phone'] ?? '',
                'emergency_contact' => $data['emergency_contact'] ?? '',
                'work_experience' => $data['work_experience'] ?? 0,
                'service_areas' => json_encode($data['service_areas'] ?? []),
                'auth_status' => 'pending',
                'create_time' => time(),
                'update_time' => time(),
            ];
            
            $profileId = Db::name('worker_profiles')->insertGetId($profileData);
            
            // 添加师傅角色
            $this->addWorkerRole($uid);
            
            Db::commit();
            
            // 清除缓存
            $this->clearWorkerCache($uid);
            
            return true;
            
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 更新师傅认证状态
     * @param int $uid
     * @param string $status
     * @param string $reason
     * @return bool
     */
    public function updateAuthStatus(int $uid, string $status, string $reason = ''): bool
    {
        $updateData = [
            'auth_status' => $status,
            'auth_time' => time(),
            'update_time' => time(),
        ];
        
        $result = Db::name('worker_profiles')
            ->where('user_id', $uid)
            ->update($updateData);
            
        if ($result) {
            // 记录认证日志
            $this->logAuthRecord($uid, 'identity', $status, $reason);
            
            // 清除缓存
            $this->clearWorkerCache($uid);
        }
        
        return $result > 0;
    }
    
    /**
     * 申请技能认证
     * @param int $uid
     * @param int $skillId
     * @param array $data
     * @return bool
     */
    public function applySkillAuth(int $uid, int $skillId, array $data): bool
    {
        try {
            $workerInfo = $this->getWorkerInfo($uid);
            if (!$workerInfo) {
                throw new \Exception('师傅信息不存在');
            }
            
            // 检查是否已申请过该技能
            $exists = Db::name('worker_skills_new')
                ->where('worker_id', $workerInfo['id'])
                ->where('skill_id', $skillId)
                ->count() > 0;
                
            if ($exists) {
                throw new \Exception('已申请过该技能认证');
            }
            
            // 创建技能认证申请
            $skillData = [
                'worker_id' => $workerInfo['id'],
                'skill_id' => $skillId,
                'skill_level' => $data['skill_level'] ?? 'beginner',
                'experience_years' => $data['experience_years'] ?? 0,
                'hourly_rate' => $data['hourly_rate'] ?? 0,
                'certificate_images' => json_encode($data['certificate_images'] ?? []),
                'work_samples' => json_encode($data['work_samples'] ?? []),
                'auth_status' => 'pending',
                'create_time' => time(),
                'update_time' => time(),
            ];
            
            $result = Db::name('worker_skills_new')->insert($skillData);
            
            if ($result) {
                // 记录认证日志
                $this->logAuthRecord($uid, 'skill', 'pending', '', $skillId);
            }
            
            return $result;
            
        } catch (\Exception $e) {
            throw $e;
        }
    }
    
    /**
     * 生成师傅编号
     * @return string
     */
    private function generateWorkerNo(): string
    {
        $prefix = 'W';
        $timestamp = date('ymd');
        $random = str_pad((string)mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
        
        return $prefix . $timestamp . $random;
    }
    
    /**
     * 添加师傅角色
     * @param int $uid
     * @return bool
     */
    private function addWorkerRole(int $uid): bool
    {
        // 获取师傅角色ID
        $roleId = Db::name('user_roles')
            ->where('role_code', 'worker')
            ->value('id');
            
        if (!$roleId) {
            throw new \Exception('师傅角色不存在');
        }
        
        // 检查是否已有该角色
        $exists = Db::name('user_role_relations')
            ->where('user_id', $uid)
            ->where('role_id', $roleId)
            ->count() > 0;
            
        if ($exists) {
            return true;
        }
        
        // 添加角色关联
        return Db::name('user_role_relations')->insert([
            'user_id' => $uid,
            'role_id' => $roleId,
            'status' => 1,
            'create_time' => time(),
        ]);
    }
    
    /**
     * 记录认证日志
     * @param int $uid
     * @param string $authType
     * @param string $status
     * @param string $reason
     * @param int $itemId
     */
    private function logAuthRecord(int $uid, string $authType, string $status, string $reason = '', int $itemId = 0): void
    {
        $workerInfo = $this->getWorkerInfo($uid);
        if (!$workerInfo) {
            return;
        }
        
        Db::name('worker_auth_records')->insert([
            'worker_id' => $workerInfo['id'],
            'auth_type' => $authType,
            'auth_item_id' => $itemId,
            'auth_data' => json_encode(['reason' => $reason]),
            'status' => $status,
            'submit_time' => time(),
            'create_time' => time(),
        ]);
    }
    
    /**
     * 清除师傅缓存
     * @param int $uid
     */
    private function clearWorkerCache(int $uid): void
    {
        Cache::delete("worker_info_{$uid}");
        Cache::delete("worker_permissions_{$uid}");
    }
}
