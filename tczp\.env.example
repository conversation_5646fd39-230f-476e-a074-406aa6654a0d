# ==================== 应用配置 ====================
APP_DEBUG = true
APP_TRACE = false

# ==================== 数据库配置 ====================
DATABASE_HOSTNAME = 127.0.0.1
DATABASE_DATABASE = tczp
DATABASE_USERNAME = root
DATABASE_PASSWORD = 
DATABASE_HOSTPORT = 3306
DATABASE_CHARSET = utf8mb4
DATABASE_PREFIX = 

# ==================== Redis配置 ====================
REDIS_HOSTNAME = 127.0.0.1
REDIS_PASSWORD = 
REDIS_HOSTPORT = 6379
REDIS_SELECT = 0

# ==================== 认证配置 ====================
# 系统授权密钥
AUTH_KEY = tczp_auth_2024

# JWT配置
JWT_SECRET = tczp_jwt_secret_2024
JWT_ISSUER = tczp.system
JWT_AUDIENCE = tczp.users

# API配置
API_KEY = tczp_api_key_2024
API_IP_WHITELIST = 

# 加密配置
ENCRYPT_KEY = tczp_encrypt_2024
PASSWORD_SALT = tczp_salt_2024

# ==================== 授权配置 ====================
# 授权类型 (commercial, trial, free)
LICENSE_TYPE = commercial

# 授权到期时间 (0表示永久授权)
LICENSE_EXPIRE = 0

# 授权域名限制 (多个域名用逗号分隔)
ALLOWED_DOMAINS = 

# 系统授权验证开关
SYSTEM_AUTH_ENABLED = false

# 授权服务器地址
AUTH_SERVER = 

# 产品密钥
PRODUCT_KEY = 

# 机器码
MACHINE_CODE = 

# 开发环境跳过授权验证
SKIP_AUTH_CHECK = true

# ==================== 微信配置 ====================
WECHAT_APP_ID = 
WECHAT_APP_SECRET = 
WECHAT_MCH_ID = 
WECHAT_KEY = 
WECHAT_APICLIENT_CERT = 
WECHAT_APICLIENT_KEY = 

# ==================== 支付宝配置 ====================
ALIPAY_APP_ID = 
ALIPAY_PRIVATE_KEY = 
ALIPAY_PUBLIC_KEY = 

# ==================== QQ配置 ====================
QQ_APP_ID = 
QQ_APP_KEY = 

# ==================== 短信配置 ====================
SMS_ACCOUNT = 
SMS_PASSWORD = 
SMS_SIGN = 

# ==================== 邮件配置 ====================
MAIL_TYPE = smtp
MAIL_SMTP_HOST = smtp.qq.com
MAIL_SMTP_PORT = 465
MAIL_SMTP_USERNAME = 
MAIL_SMTP_PASSWORD = 
MAIL_SMTP_SECURE = ssl
MAIL_FROM_NAME = TCZP系统

# ==================== 文件存储配置 ====================
UPLOAD_TYPE = local
# 本地存储
UPLOAD_LOCAL_PATH = /uploads
# 七牛云存储
QINIU_ACCESS_KEY = 
QINIU_SECRET_KEY = 
QINIU_BUCKET = 
QINIU_DOMAIN = 
# 阿里云OSS
OSS_ACCESS_KEY = 
OSS_SECRET_KEY = 
OSS_BUCKET = 
OSS_ENDPOINT = 
# 腾讯云COS
COS_SECRET_ID = 
COS_SECRET_KEY = 
COS_REGION = 
COS_BUCKET = 

# ==================== 队列配置 ====================
QUEUE_TYPE = redis
QUEUE_REDIS_HOST = 127.0.0.1
QUEUE_REDIS_PORT = 6379
QUEUE_REDIS_PASSWORD = 
QUEUE_REDIS_SELECT = 1

# ==================== 缓存配置 ====================
CACHE_TYPE = redis
CACHE_REDIS_HOST = 127.0.0.1
CACHE_REDIS_PORT = 6379
CACHE_REDIS_PASSWORD = 
CACHE_REDIS_SELECT = 2

# ==================== 日志配置 ====================
LOG_TYPE = file
LOG_PATH = ../runtime/log/
LOG_LEVEL = error

# ==================== Swoole配置 ====================
SWOOLE_HOST = 0.0.0.0
SWOOLE_PORT = 20199
SWOOLE_MODE = SWOOLE_PROCESS
SWOOLE_SOCK_TYPE = SWOOLE_SOCK_TCP
SWOOLE_WORKER_NUM = 4
SWOOLE_TASK_WORKER_NUM = 4
SWOOLE_MAX_REQUEST = 10000
SWOOLE_DAEMONIZE = false

# ==================== 其他配置 ====================
# 时区
TIMEZONE = Asia/Shanghai

# 语言
LANG = zh-cn

# 调试模式
DEBUG_MODE = true

# 错误报告级别
ERROR_REPORTING = E_ALL

# 会话配置
SESSION_AUTO_START = true
SESSION_TYPE = redis
SESSION_PREFIX = tczp_session_

# CSRF保护
CSRF_TOKEN = true

# 跨域配置
CORS_ALLOW_ORIGIN = *
CORS_ALLOW_METHODS = GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOW_HEADERS = *
