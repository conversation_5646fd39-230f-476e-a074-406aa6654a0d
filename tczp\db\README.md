# 师傅端数据库优化执行指南

## 📋 概述
本目录包含师傅端系统优化所需的数据库维护SQL文件。请按照指定顺序执行，确保数据库结构和数据的完整性。

## 📁 文件说明

### 1. `01_database_structure_optimization.sql`
- **功能**: 创建师傅端优化所需的新表结构
- **内容**: 
  - 用户角色表 (`eb_user_roles`)
  - 用户角色关联表 (`eb_user_role_relations`)
  - 师傅信息表 (`eb_worker_profiles`)
  - 技能分类表 (`eb_skill_categories`)
  - 技能表 (`eb_skills_new`)
  - 师傅技能关联表 (`eb_worker_skills_new`)
  - 认证记录表 (`eb_worker_auth_records`)
  - 现有表字段优化

### 2. `02_basic_data_initialization.sql`
- **功能**: 初始化基础数据
- **内容**:
  - 基础角色数据 (customer, worker, enterprise, admin)
  - 技能分类数据 (家政、维修、搬家等)
  - 具体技能数据 (清洁、维修、搬运等)
  - 创建视图和索引

### 3. `03_data_migration.sql`
- **功能**: 迁移现有数据到新结构
- **内容**:
  - 用户角色数据迁移
  - 师傅信息数据迁移
  - 技能数据迁移
  - 任务数据优化
  - 认证记录创建
  - 数据完整性检查

## 🚀 执行步骤

### 第一步：备份数据库
```bash
# 备份当前数据库
mysqldump -u root -p tczp > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 第二步：执行数据库结构优化
```bash
# 执行结构优化
mysql -u root -p tczp < 01_database_structure_optimization.sql
```

### 第三步：初始化基础数据
```bash
# 执行基础数据初始化
mysql -u root -p tczp < 02_basic_data_initialization.sql
```

### 第四步：执行数据迁移
```bash
# 执行数据迁移
mysql -u root -p tczp < 03_data_migration.sql
```

## ⚠️ 注意事项

### 执行前检查
1. **数据库备份**: 务必先备份数据库
2. **权限确认**: 确保MySQL用户有足够权限
3. **磁盘空间**: 确保有足够的磁盘空间
4. **业务停机**: 建议在业务低峰期执行

### 执行过程监控
1. **执行日志**: 记录每个步骤的执行结果
2. **错误处理**: 如遇错误，请停止执行并检查
3. **数据验证**: 每步执行后验证数据完整性

### 回滚准备
如果执行过程中出现问题，可以使用备份文件回滚：
```bash
# 回滚到备份状态
mysql -u root -p tczp < backup_YYYYMMDD_HHMMSS.sql
```

## 📊 执行后验证

### 1. 表结构验证
```sql
-- 检查新表是否创建成功
SHOW TABLES LIKE 'eb_%_new';
SHOW TABLES LIKE 'eb_user_roles';
SHOW TABLES LIKE 'eb_worker_%';
```

### 2. 数据完整性验证
```sql
-- 检查数据迁移结果
SELECT 
    '用户角色关联' as table_name,
    COUNT(*) as record_count
FROM eb_user_role_relations
UNION ALL
SELECT 
    '师傅档案' as table_name,
    COUNT(*) as record_count
FROM eb_worker_profiles
UNION ALL
SELECT 
    '技能数据' as table_name,
    COUNT(*) as record_count
FROM eb_skills_new;
```

### 3. 业务功能验证
- 师傅注册流程测试
- 技能认证流程测试
- 任务申请流程测试
- 权限控制测试

## 🔧 常见问题

### Q1: 执行时提示表已存在
**A**: 这是正常现象，SQL中使用了`DROP TABLE IF EXISTS`，会先删除已存在的表。

### Q2: 数据迁移后数量不匹配
**A**: 请检查原始数据的完整性，部分无效数据可能被过滤。

### Q3: 外键约束错误
**A**: 请确保按顺序执行SQL文件，先创建主表再创建关联表。

### Q4: 权限不足错误
**A**: 确保MySQL用户有CREATE、DROP、ALTER、INSERT等权限。

## 📞 技术支持

如果在执行过程中遇到问题，请：

1. **保留错误日志**: 记录完整的错误信息
2. **检查数据状态**: 确认当前数据库状态
3. **联系技术团队**: 提供详细的执行环境和错误信息

## 📈 性能优化建议

执行完成后，建议进行以下优化：

1. **重建索引统计**:
```sql
ANALYZE TABLE eb_worker_profiles;
ANALYZE TABLE eb_worker_skills_new;
ANALYZE TABLE eb_user_role_relations;
```

2. **清理查询缓存**:
```sql
RESET QUERY CACHE;
```

3. **监控性能指标**:
   - 查询响应时间
   - 索引使用率
   - 内存使用情况

---

**执行完成后，请继续进行第二阶段的中间件重构工作。**
