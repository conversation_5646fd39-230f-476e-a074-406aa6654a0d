<?php
namespace app\controller\admin\notification\sms;

use app\controller\admin\AuthController;
use app\services\message\sms\SmsAdminServices;
use app\services\serve\ServeServices;
use think\annotation\Inject;

/**
 * 短信配置
 * Class SmsConfig
 * @package app\admin\controller\sms
 */
class SmsConfig extends AuthController
{

    /**
     * @var SmsAdminServices
     */
    #[Inject]
    protected SmsAdminServices $services;


    /**
     * 保存短信配置
     * @return mixed
     */
    public function save_basics()
    {
        [$account, $token] = $this->request->postMore([
            ['sms_account', ''],
            ['sms_token', '']
        ], true);

        $this->validate(['sms_account' => $account, 'sms_token' => $token], \app\validate\admin\notification\SmsConfigValidate::class);
        if(sys_config('sms_account') && sys_config('sms_token')){
            return $this->success('保存成功');
        }
        if ($this->services->updateSmsConfig($account, $token)) {
            return $this->success('保存成功');
        } else {
            return $this->fail('保存失败');
        }
    }

}
