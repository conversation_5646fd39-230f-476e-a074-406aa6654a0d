<?php

namespace app\services;

use think\facade\Db;
use think\facade\Cache;

/**
 * 保险业务数据统计分析服务
 */
class InsuranceAnalyticsService
{
    /**
     * 获取保险业务总览
     */
    public function getBusinessOverview($dateRange = null)
    {
        $cacheKey = 'insurance_overview_' . md5(json_encode($dateRange));
        
        return Cache::remember($cacheKey, function() use ($dateRange) {
            $query = Db::name('task_insurance_records');
            
            if ($dateRange) {
                $query->whereBetweenTime('created_at', $dateRange['start'], $dateRange['end']);
            }
            
            // 基础统计
            $basicStats = $query->field('
                COUNT(*) as total_policies,
                SUM(premium_amount) as total_premium,
                COUNT(DISTINCT employer_id) as unique_employers,
                COUNT(DISTINCT worker_id) as unique_workers,
                COUNT(CASE WHEN status = "active" THEN 1 END) as active_policies
            ')->find();
            
            // 理赔统计
            $claimQuery = Db::name('insurance_claims');
            if ($dateRange) {
                $claimQuery->whereBetweenTime('created_at', $dateRange['start'], $dateRange['end']);
            }
            
            $claimStats = $claimQuery->field('
                COUNT(*) as total_claims,
                SUM(claim_amount) as total_claim_amount,
                SUM(CASE WHEN status = "approved" OR status = "settled" THEN settlement_amount ELSE 0 END) as total_settlement,
                COUNT(CASE WHEN status = "approved" OR status = "settled" THEN 1 END) as approved_claims
            ')->find();
            
            // 计算关键指标
            $claimRate = $basicStats['total_policies'] > 0 ? 
                round($claimStats['total_claims'] / $basicStats['total_policies'] * 100, 2) : 0;
            
            $approvalRate = $claimStats['total_claims'] > 0 ? 
                round($claimStats['approved_claims'] / $claimStats['total_claims'] * 100, 2) : 0;
            
            $lossRatio = $basicStats['total_premium'] > 0 ? 
                round($claimStats['total_settlement'] / $basicStats['total_premium'] * 100, 2) : 0;
            
            return [
                'basic_stats' => $basicStats,
                'claim_stats' => $claimStats,
                'key_metrics' => [
                    'claim_rate' => $claimRate,
                    'approval_rate' => $approvalRate,
                    'loss_ratio' => $lossRatio,
                    'avg_premium' => $basicStats['total_policies'] > 0 ? 
                        round($basicStats['total_premium'] / $basicStats['total_policies'], 2) : 0,
                    'avg_claim_amount' => $claimStats['total_claims'] > 0 ? 
                        round($claimStats['total_claim_amount'] / $claimStats['total_claims'], 2) : 0
                ]
            ];
        }, 1800); // 缓存30分钟
    }
    
    /**
     * 获取收入分析
     */
    public function getRevenueAnalysis($dateRange = null)
    {
        $query = Db::name('platform_revenue_records');
        
        if ($dateRange) {
            $query->whereBetweenTime('created_at', $dateRange['start'], $dateRange['end']);
        }
        
        // 按收入类型统计
        $revenueByType = $query->field('
            revenue_type,
            COUNT(*) as count,
            SUM(amount) as total_amount,
            AVG(amount) as avg_amount
        ')->group('revenue_type')->select()->toArray();
        
        // 月度收入趋势
        $monthlyRevenue = $query->field('
            DATE_FORMAT(created_at, "%Y-%m") as month,
            revenue_type,
            SUM(amount) as amount
        ')->group('month, revenue_type')
        ->order('month ASC')
        ->select()->toArray();
        
        // 计算总收入
        $totalRevenue = array_sum(array_column($revenueByType, 'total_amount'));
        
        // 保险佣金收入
        $insuranceCommission = array_sum(array_column(
            array_filter($revenueByType, function($item) {
                return $item['revenue_type'] === 'insurance_commission';
            }), 'total_amount'
        ));
        
        return [
            'total_revenue' => $totalRevenue,
            'insurance_commission' => $insuranceCommission,
            'revenue_by_type' => $revenueByType,
            'monthly_trend' => $this->formatMonthlyTrend($monthlyRevenue),
            'commission_rate' => $totalRevenue > 0 ? 
                round($insuranceCommission / $totalRevenue * 100, 2) : 0
        ];
    }
    
    /**
     * 获取用户行为分析
     */
    public function getUserBehaviorAnalysis($dateRange = null)
    {
        // 企业用户保险购买行为
        $employerBehavior = Db::name('task_insurance_records')
            ->alias('tir')
            ->join('task_protection_options tpo', 'tir.task_id = tpo.task_id')
            ->field('
                tpo.protection_type,
                COUNT(*) as count,
                COUNT(DISTINCT tir.employer_id) as unique_employers,
                AVG(tir.premium_amount) as avg_premium
            ')
            ->group('tpo.protection_type')
            ->select()->toArray();
        
        // 师傅参与保障任务统计
        $workerBehavior = Db::name('task_insurance_records')
            ->field('
                COUNT(*) as total_participations,
                COUNT(DISTINCT worker_id) as unique_workers,
                AVG(premium_amount) as avg_premium_per_task
            ')
            ->find();
        
        // 保险方案偏好
        $planPreference = Db::name('task_insurance_records')
            ->alias('tir')
            ->join('insurance_plans ip', 'tir.insurance_plan_id = ip.id')
            ->field('
                ip.plan_name,
                ip.plan_type,
                COUNT(*) as usage_count,
                SUM(tir.premium_amount) as total_premium
            ')
            ->group('tir.insurance_plan_id')
            ->order('usage_count DESC')
            ->select()->toArray();
        
        return [
            'employer_behavior' => $employerBehavior,
            'worker_behavior' => $workerBehavior,
            'plan_preference' => $planPreference,
            'insights' => $this->generateUserInsights($employerBehavior, $planPreference)
        ];
    }
    
    /**
     * 获取风险分析
     */
    public function getRiskAnalysis($dateRange = null)
    {
        // 按任务分类的理赔率
        $claimRateByCategory = Db::name('insurance_claims')
            ->alias('ic')
            ->join('task_insurance_records tir', 'ic.policy_record_id = tir.id')
            ->join('tasks t', 'tir.task_id = t.id')
            ->join('task_categories tc', 't.category_id = tc.id')
            ->field('
                tc.name as category_name,
                COUNT(ic.id) as claim_count,
                COUNT(tir.id) as policy_count,
                ROUND(COUNT(ic.id) / COUNT(tir.id) * 100, 2) as claim_rate,
                SUM(ic.claim_amount) as total_claim_amount
            ')
            ->group('tc.id')
            ->select()->toArray();
        
        // 按理赔类型分析
        $claimByType = Db::name('insurance_claims')
            ->field('
                claim_type,
                COUNT(*) as count,
                SUM(claim_amount) as total_amount,
                AVG(claim_amount) as avg_amount,
                COUNT(CASE WHEN status = "approved" OR status = "settled" THEN 1 END) as approved_count
            ')
            ->group('claim_type')
            ->select()->toArray();
        
        // 高风险识别
        $highRiskTasks = Db::name('tasks')
            ->alias('t')
            ->join('task_insurance_records tir', 't.id = tir.task_id')
            ->join('insurance_claims ic', 'tir.id = ic.policy_record_id', 'LEFT')
            ->field('
                t.category_id,
                COUNT(tir.id) as policy_count,
                COUNT(ic.id) as claim_count,
                ROUND(COUNT(ic.id) / COUNT(tir.id) * 100, 2) as claim_rate
            ')
            ->group('t.category_id')
            ->having('claim_rate > 10')
            ->order('claim_rate DESC')
            ->select()->toArray();
        
        return [
            'claim_rate_by_category' => $claimRateByCategory,
            'claim_by_type' => $claimByType,
            'high_risk_categories' => $highRiskTasks,
            'risk_recommendations' => $this->generateRiskRecommendations($claimRateByCategory, $claimByType)
        ];
    }
    
    /**
     * 生成保险业务报表
     */
    public function generateBusinessReport($dateRange, $reportType = 'monthly')
    {
        $data = [
            'overview' => $this->getBusinessOverview($dateRange),
            'revenue' => $this->getRevenueAnalysis($dateRange),
            'user_behavior' => $this->getUserBehaviorAnalysis($dateRange),
            'risk_analysis' => $this->getRiskAnalysis($dateRange)
        ];
        
        // 生成报表摘要
        $summary = $this->generateReportSummary($data);
        
        // 生成改进建议
        $recommendations = $this->generateBusinessRecommendations($data);
        
        return [
            'report_date' => date('Y-m-d H:i:s'),
            'date_range' => $dateRange,
            'report_type' => $reportType,
            'data' => $data,
            'summary' => $summary,
            'recommendations' => $recommendations
        ];
    }
    
    /**
     * 格式化月度趋势数据
     */
    private function formatMonthlyTrend($monthlyData)
    {
        $trend = [];
        foreach ($monthlyData as $item) {
            $month = $item['month'];
            if (!isset($trend[$month])) {
                $trend[$month] = ['month' => $month, 'total' => 0];
            }
            $trend[$month][$item['revenue_type']] = $item['amount'];
            $trend[$month]['total'] += $item['amount'];
        }
        return array_values($trend);
    }
    
    /**
     * 生成用户洞察
     */
    private function generateUserInsights($employerBehavior, $planPreference)
    {
        $insights = [];
        
        // 分析保障类型偏好
        $totalTasks = array_sum(array_column($employerBehavior, 'count'));
        foreach ($employerBehavior as $behavior) {
            $percentage = round($behavior['count'] / $totalTasks * 100, 1);
            $insights[] = "{$behavior['protection_type']}任务占比{$percentage}%";
        }
        
        // 分析保险方案偏好
        if (!empty($planPreference)) {
            $mostPopular = $planPreference[0];
            $insights[] = "最受欢迎的保险方案是{$mostPopular['plan_name']}";
        }
        
        return $insights;
    }
    
    /**
     * 生成风险建议
     */
    private function generateRiskRecommendations($claimRateByCategory, $claimByType)
    {
        $recommendations = [];
        
        // 基于分类理赔率的建议
        foreach ($claimRateByCategory as $category) {
            if ($category['claim_rate'] > 15) {
                $recommendations[] = [
                    'type' => 'high_risk_category',
                    'category' => $category['category_name'],
                    'message' => "建议对{$category['category_name']}类任务提高保费或加强风险控制"
                ];
            }
        }
        
        // 基于理赔类型的建议
        foreach ($claimByType as $type) {
            if ($type['count'] > 50 && $type['avg_amount'] > 5000) {
                $recommendations[] = [
                    'type' => 'frequent_claim_type',
                    'claim_type' => $type['claim_type'],
                    'message' => "需要关注{$type['claim_type']}类型的理赔，考虑调整保障方案"
                ];
            }
        }
        
        return $recommendations;
    }
    
    /**
     * 生成报表摘要
     */
    private function generateReportSummary($data)
    {
        $overview = $data['overview'];
        $revenue = $data['revenue'];
        
        return [
            'total_policies' => $overview['basic_stats']['total_policies'],
            'total_premium' => $overview['basic_stats']['total_premium'],
            'total_revenue' => $revenue['total_revenue'],
            'claim_rate' => $overview['key_metrics']['claim_rate'],
            'loss_ratio' => $overview['key_metrics']['loss_ratio'],
            'profitability' => $revenue['total_revenue'] - $overview['claim_stats']['total_settlement']
        ];
    }
    
    /**
     * 生成业务建议
     */
    private function generateBusinessRecommendations($data)
    {
        $recommendations = [];
        $metrics = $data['overview']['key_metrics'];
        
        // 基于理赔率的建议
        if ($metrics['claim_rate'] > 20) {
            $recommendations[] = [
                'priority' => 'high',
                'type' => 'claim_rate',
                'message' => '理赔率偏高，建议加强风险评估和预防措施'
            ];
        }
        
        // 基于赔付率的建议
        if ($metrics['loss_ratio'] > 80) {
            $recommendations[] = [
                'priority' => 'high',
                'type' => 'loss_ratio',
                'message' => '赔付率过高，建议调整保费定价策略'
            ];
        }
        
        // 基于收入结构的建议
        $commissionRate = $data['revenue']['commission_rate'];
        if ($commissionRate < 30) {
            $recommendations[] = [
                'priority' => 'medium',
                'type' => 'revenue_structure',
                'message' => '保险佣金占比较低，可以推广更多保障任务'
            ];
        }
        
        return $recommendations;
    }
}
