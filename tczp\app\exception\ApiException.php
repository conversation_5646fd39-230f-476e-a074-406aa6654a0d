<?php
declare(strict_types=1);

namespace app\exception;

use think\exception\Handle;
use think\exception\HttpException;
use think\exception\ValidateException;
use think\facade\Log;
use think\Response;
use Throwable;

/**
 * 统一API异常处理类
 * Class ApiException
 * @package app\exception
 */
class ApiException extends Handle
{
    /**
     * 不需要记录信息（日志）的异常类列表
     * @var array
     */
    protected $ignoreReport = [
        HttpException::class,
        ValidateException::class,
    ];

    /**
     * 记录异常信息（包括日志或者其它方式记录）
     * @access public
     * @param Throwable $exception
     * @return void
     */
    public function report(Throwable $exception): void
    {
        // 使用内置的方式记录异常日志
        parent::report($exception);
    }

    /**
     * Render an exception into an HTTP response.
     * @access public
     * @param \think\Request $request
     * @param Throwable $e
     * @return Response
     */
    public function render($request, Throwable $e): Response
    {
        // 添加自定义异常处理机制
        
        // 参数验证错误
        if ($e instanceof ValidateException) {
            return $this->jsonResponse(400, $e->getError(), []);
        }
        
        // HTTP异常
        if ($e instanceof HttpException && $request->isAjax()) {
            return $this->jsonResponse($e->getStatusCode(), $e->getMessage(), []);
        }
        
        // 自定义业务异常
        if ($e instanceof BusinessException) {
            return $this->jsonResponse($e->getCode() ?: 400, $e->getMessage(), $e->getData());
        }
        
        // 数据库异常
        if ($e instanceof \think\db\exception\DbException) {
            Log::error('Database Error: ' . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return $this->jsonResponse(500, '数据库操作失败', []);
        }
        
        // 开发环境显示详细错误信息
        if (app()->isDebug()) {
            return $this->jsonResponse(500, $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTrace()
            ]);
        }
        
        // 生产环境返回通用错误信息
        Log::error('System Error: ' . $e->getMessage(), [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]);
        
        return $this->jsonResponse(500, '系统错误，请稍后重试', []);
    }

    /**
     * 返回JSON响应
     * @param int $code
     * @param string $message
     * @param mixed $data
     * @return Response
     */
    private function jsonResponse(int $code, string $message, $data = []): Response
    {
        $result = [
            'code' => $code,
            'status' => $code,
            'msg' => $message,
            'message' => $message,
            'data' => $data,
            'time' => time(),
            'timestamp' => time()
        ];

        return Response::create($result, 'json')->code($code >= 200 && $code < 300 ? 200 : $code);
    }
}

/**
 * 业务异常类
 * Class BusinessException
 * @package app\exception
 */
class BusinessException extends \Exception
{
    /**
     * 异常数据
     * @var mixed
     */
    protected $data;

    /**
     * 构造函数
     * @param string $message
     * @param int $code
     * @param mixed $data
     */
    public function __construct(string $message = '', int $code = 400, $data = [])
    {
        parent::__construct($message, $code);
        $this->data = $data;
    }

    /**
     * 获取异常数据
     * @return mixed
     */
    public function getData()
    {
        return $this->data;
    }
}
