<?php
namespace app\controller\admin\system\log;

use app\controller\admin\AuthController;
use app\services\system\log\ClearServices;
use think\exception\ValidateException;

/**
 * 清除数据控制器
 * Class Clear
 * @package app\controller\admin\v1\system
 */
class Clear extends AuthController
{
    /**
     * @var ClearServices
     */
    protected ClearServices $services;

    /**
     * Clear constructor.
     * @param ClearServices $services
     */
    public function __construct(ClearServices $services)
    {
        parent::__construct();
        $this->services = $services;
        //生产模式下不允许清除数据
        if (!env('APP_DEBUG', false)) {
            throw new ValidateException('生产模式下，禁止操作');
        }
    }

    /**
     * 刷新数据缓存
     */
    public function refresh_cache()
    {
        $this->services->refresCache();
        return $this->success('数据缓存刷新成功!');
    }


    /**
     * 删除日志
     */
    public function delete_log()
    {
        $this->services->deleteLog();
        return $this->success('数据缓存刷新成功!');
    }
}


