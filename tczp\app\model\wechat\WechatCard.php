<?php

namespace app\model\wechat;


use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;

/**
 * 关键词model
 * Class WechatKey
 * @package app\model\wechat
 */
class WechatCard extends BaseModel
{
    use ModelTrait;

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'wechat_card';

    /**
     * 特别信息修改器
     * @param $value
     * @return false|string
     */
    protected function setEspecialAttr($value)
    {
        if ($value) {
            return is_array($value) ? json_encode($value) : $value;
        }
        return '';
    }

    /**
     * 特别信息获取器
     * @param $value
     * @param $data
     * @return mixed
     */
    protected function getEspecialAttr($value)
    {
        return $value ? json_decode($value, true) : [];
    }
}
