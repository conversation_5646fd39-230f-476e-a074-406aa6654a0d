<?php

namespace app\jobs\system;

use app\services\system\log\SystemLogServices;
use crmeb\basic\BaseJobs;
use crmeb\traits\QueueTrait;

/**
 * 后台日志
 * Class AdminLogJob
 * @package app\jobs\system
 */
class AdminLogJob extends BaseJobs
{
    use QueueTrait;

    /**
     * @return mixed
     */
    public static function queueName()
    {
        return 'CRMEB_PRO_LOG';
    }

    /**
     * @param $adminId
     * @param $adminName
     * @param $module
     * @param $rule
     * @param $ip
     * @param $type
     * @return bool
     */
    public function doJob($adminId, $adminName, $module, $rule, $ip, $type)
    {
        try {
            /** @var SystemLogServices $services */
            $services = app()->make(SystemLogServices::class);
            $services->recordAdminLog((int)$adminId, $adminName, $module, $rule, $ip, $type);
        } catch (\Exception) {

        }
        return true;
    }
}
