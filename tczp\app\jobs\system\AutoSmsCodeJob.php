<?php
namespace app\jobs\system;

use crmeb\basic\BaseJobs;
use app\services\message\sms\SmsRecordServices;
use crmeb\traits\QueueTrait;

/**
 * 自动更新短信状态
 * Class AutoSmsCodeJob
 * @package app\jobs\user
 */
class AutoSmsCodeJob extends BaseJobs
{
    use QueueTrait;

    /**
     * @return string
     */
    protected static function queueName()
    {
        return 'CRMEB_PRO_TASK';
    }

    /**
     * @param $event
     */
    public function doJob()
    {
        //清除昨日海报
        try {
            //修改短信发送记录短信状态
            $smsRecord = app()->make(SmsRecordServices::class);
            $smsRecord->modifyResultCode();
        } catch (\Throwable $e) {

            response_log_write([
                'message' => '自动更新短信状态:[' . class_basename($this) . ']' . $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
		return true;
    }
}
