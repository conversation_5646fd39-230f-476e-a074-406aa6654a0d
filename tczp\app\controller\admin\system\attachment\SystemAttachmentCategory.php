<?php
namespace app\controller\admin\system\attachment;

use app\controller\admin\AuthController;
use app\services\system\attachment\SystemAttachmentCategoryServices;
use think\annotation\Inject;

/**
 * 图片分类管理类
 * Class SystemAttachmentCategory
 * @package app\controller\admin\v1\file
 */
class SystemAttachmentCategory extends AuthController
{

    /**
     * @var SystemAttachmentCategoryServices
     */
    #[Inject]
    protected SystemAttachmentCategoryServices $service;

    /**
     * 显示资源列表
     *
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index()
    {
        $where = $this->request->getMore([
            ['name', ''],
            ['pid', 0],
            ['file_type', 1]
        ]);
        $where['type'] = 1;
        if ($this->supplierId) {
            $where['type'] = 4;
            $where['relation_id'] = $this->supplierId;
        }

        if ($where['name'] != '') $where['pid'] = '';
        return $this->success($this->service->getAll($where));
    }

    /**
     * 新增表单
     * @return mixed
     * @throws \FormBuilder\Exception\FormBuilderException
     */
    public function create($id)
    {
        [$file_type] = $this->request->postMore([
            ['file_type', 1]
        ], true);
        $type = 1;
        $relation_id = 0;
        if ($this->supplierId) {
            $type = 4;
            $relation_id = $this->supplierId;
        }
        return $this->success($this->service->createForm($id, $type, $relation_id, $file_type));
    }

    /**
     * 保存新增
     * @return mixed
     */
    public function save()
    {
        $data = $this->request->postMore([
            ['pid', 0],
            ['name', ''],
            ['file_type', 1]
        ]);
        if (!$data['name']) {
            return $this->fail('请输入分类名称');
        }
        $data['type'] = 1;
        if ($this->supplierId) {
            $data['type'] = 4;
            $data['relation_id'] = $this->supplierId;
        }

        $this->service->save($data);
        return $this->success('添加成功');
    }

    /**
     * 编辑表单
     * @param $id
     * @return mixed
     * @throws \FormBuilder\Exception\FormBuilderException
     */
    public function edit($id)
    {
        [$file_type] = $this->request->postMore([
            ['file_type', 1]
        ], true);
        $type = 1;
        $relation_id = 0;
        if ($this->supplierId) {
            $type = 4;
            $relation_id = $this->supplierId;
        }
        return $this->success($this->service->editForm($id, $type, $relation_id, $file_type));
    }

    /**
     * 保存更新的资源
     *
     * @param \think\Request $request
     * @param int $id
     * @return \think\Response
     */
    public function update($id)
    {
        $data = $this->request->postMore([
            ['pid', 0],
            ['name', ''],
            ['file_type', 1]
        ]);
        if (!$data['name']) {
            return $this->fail('请输入分类名称');
        }
        $info = $this->service->get($id);
        $count = $this->service->count(['pid' => $id]);
        if ($count && $info['pid'] != $data['pid']) return $this->fail('该分类有下级分类，无法修改上级');
        $this->service->update($id, $data);
        return $this->success('分类编辑成功!');
    }

    /**
     * 删除指定资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function delete($id)
    {
        $this->service->del($id);
        return $this->success('删除成功!');
    }
}
