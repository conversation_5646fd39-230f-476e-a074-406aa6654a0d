<?php
declare (strict_types=1);

namespace app\dao\wechat;

use think\model;
use app\dao\BaseDao;
use app\model\wechat\WechatCard;

/**
 * 微信卡券
 * Class WechatCardDao
 * @package app\dao\wechat
 */
class WechatCardDao extends BaseDao
{
    protected function setModel(): string
    {
        return WechatCard::class;
    }

    /**
     * 获取卡券列表
     * @param array $where
     * @param int $page
     * @param int $limit
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getList(array $where, int $page = 0, int $limit = 0)
    {
        return $this->search($where)->when($page && $limit, function ($query) use ($page, $limit) {
            $query->page($page, $limit);
        })->select()->toArray();
    }

}
