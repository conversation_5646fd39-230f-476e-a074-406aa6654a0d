<?php
// +----------------------------------------------------------------------
// | 同城用工系统 - 师傅端评价管理
// +----------------------------------------------------------------------

namespace app\controller\staff;

use app\controller\BaseAuth;
use think\facade\Db;

/**
 * 评价管理控制器
 */
class Review extends BaseAuth
{
    /**
     * 获取评价列表
     */
    public function getReviewList()
    {
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 20);
        $rating = $this->request->get('rating', 0);

        try {
            $userId = $this->getUserId();
            $where = [['tr.worker_id', '=', $userId]];

            if ($rating > 0) {
                $where[] = ['tr.rating', '=', $rating];
            }

            $list = Db::name('task_reviews')
                ->alias('tr')
                ->leftJoin('tasks t', 'tr.task_id = t.id')
                ->leftJoin('users u', 'tr.user_id = u.id')
                ->where($where)
                ->field('tr.*, t.task_title, u.nickname as reviewer_name, u.avatar as reviewer_avatar')
                ->order('tr.create_time desc')
                ->page($page, $limit)
                ->select()
                ->toArray();

            $total = Db::name('task_reviews')
                ->alias('tr')
                ->where($where)
                ->count();

            return $this->success([
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ]);
        } catch (\Exception $e) {
            return $this->fail('获取评价列表失败：' . $e->getMessage());
        }
    }

    /**
     * 回复评价
     */
    public function replyReview()
    {
        $reviewId = $this->request->param('id', 0);
        $replyContent = $this->request->post('reply_content', '');

        if (!$reviewId) {
            return $this->fail('评价ID不能为空');
        }

        if (!$replyContent) {
            return $this->fail('请输入回复内容');
        }

        try {
            $userId = $this->getUserId();

            // 检查评价是否存在且属于当前用户
            $review = Db::name('task_reviews')
                ->where('id', $reviewId)
                ->where('worker_id', $userId)
                ->find();

            if (!$review) {
                return $this->fail('评价不存在或无权限');
            }

            if ($review['reply_content']) {
                return $this->fail('已回复过此评价');
            }

            // 更新回复内容
            Db::name('task_reviews')
                ->where('id', $reviewId)
                ->update([
                    'reply_content' => $replyContent,
                    'reply_time' => time()
                ]);

            return $this->success([], '回复成功');
        } catch (\Exception $e) {
            return $this->fail('回复失败：' . $e->getMessage());
        }
    }

    /**
     * 获取评价统计
     */
    public function getReviewStats()
    {
        try {
            $userId = $this->getUserId();

            // 总评价数
            $totalReviews = Db::name('task_reviews')
                ->where('worker_id', $userId)
                ->count();

            // 平均评分
            $avgRating = Db::name('task_reviews')
                ->where('worker_id', $userId)
                ->avg('rating');

            // 各星级评价数量
            $ratingStats = Db::name('task_reviews')
                ->where('worker_id', $userId)
                ->field('rating, COUNT(*) as count')
                ->group('rating')
                ->select()
                ->toArray();

            $ratingDistribution = [
                1 => 0, 2 => 0, 3 => 0, 4 => 0, 5 => 0
            ];

            foreach ($ratingStats as $stat) {
                $ratingDistribution[$stat['rating']] = $stat['count'];
            }

            // 好评率（4星及以上）
            $goodReviews = $ratingDistribution[4] + $ratingDistribution[5];
            $goodRate = $totalReviews > 0 ? round(($goodReviews / $totalReviews) * 100, 1) : 0;

            $stats = [
                'total_reviews' => $totalReviews,
                'avg_rating' => round($avgRating, 1),
                'good_rate' => $goodRate,
                'rating_distribution' => $ratingDistribution
            ];

            return $this->success($stats);
        } catch (\Exception $e) {
            return $this->fail('获取评价统计失败：' . $e->getMessage());
        }
    }
} 