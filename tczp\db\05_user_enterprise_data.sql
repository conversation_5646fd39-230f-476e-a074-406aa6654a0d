-- ==================== 用户端（企业端）基础数据初始化 ====================
-- 文件: 05_user_enterprise_data.sql
-- 描述: 用户端（企业端）系统的基础数据初始化
-- 执行顺序: 第五步执行（在04之后）
-- 作者: TCZP Team
-- 日期: 2024-12-19

-- 设置时间变量
SET @current_time = UNIX_TIMESTAMP();

-- ==================== 1. 更新现有用户角色权限 ====================

-- 更新普通用户权限
UPDATE `eb_user_roles` SET 
    `permissions` = '["task.create", "task.manage", "payment.pay", "review.create", "worker.view", "template.use"]'
WHERE `role_code` = 'customer';

-- 更新企业用户权限
UPDATE `eb_user_roles` SET 
    `permissions` = '["task.batch_create", "task.manage", "worker.invite", "worker.favorite", "report.view", "enterprise.manage", "template.create", "contract.manage", "payment.batch", "settlement.view"]'
WHERE `role_code` = 'enterprise';

-- ==================== 2. 创建任务分类数据 ====================

-- 插入任务分类（如果不存在）
INSERT IGNORE INTO `eb_task_categories` (`parent_id`, `category_name`, `category_code`, `icon`, `description`, `sort`, `status`, `create_time`) VALUES
(0, '企业服务', 'enterprise_service', 'icon-enterprise', '企业级专业服务', 1, 1, @current_time),
(0, '批量任务', 'batch_task', 'icon-batch', '批量发布的任务', 2, 1, @current_time),
(0, '紧急任务', 'urgent_task', 'icon-urgent', '紧急处理的任务', 3, 1, @current_time),
(0, '长期合作', 'long_term', 'icon-longterm', '长期合作项目', 4, 1, @current_time);

-- 获取企业服务分类ID
SET @enterprise_service_id = (SELECT id FROM eb_task_categories WHERE category_code = 'enterprise_service');

-- 插入企业服务子分类
INSERT IGNORE INTO `eb_task_categories` (`parent_id`, `category_name`, `category_code`, `icon`, `description`, `sort`, `status`, `create_time`) VALUES
(@enterprise_service_id, '办公清洁', 'office_cleaning', 'icon-office-clean', '办公室清洁服务', 1, 1, @current_time),
(@enterprise_service_id, '设备维护', 'equipment_maintenance', 'icon-equipment', '设备维护保养', 2, 1, @current_time),
(@enterprise_service_id, '会议服务', 'meeting_service', 'icon-meeting', '会议相关服务', 3, 1, @current_time),
(@enterprise_service_id, '活动支持', 'event_support', 'icon-event', '活动策划执行', 4, 1, @current_time);

-- ==================== 3. 创建任务模板数据 ====================

-- 插入常用任务模板
INSERT INTO `eb_task_templates` (`user_id`, `template_name`, `template_data`, `category_id`, `is_public`, `status`, `create_time`, `update_time`) VALUES
(0, '办公室日常清洁', JSON_OBJECT(
    'task_title', '办公室日常清洁服务',
    'task_description', '需要专业清洁人员进行办公室日常清洁，包括地面清洁、桌面整理、垃圾清理等',
    'required_workers', 2,
    'estimated_duration', 4,
    'hourly_rate', 35.00,
    'required_skills', JSON_ARRAY(1, 2),
    'worker_requirements', JSON_OBJECT('experience_years', 1, 'certification_required', false)
), (SELECT id FROM eb_task_categories WHERE category_code = 'office_cleaning'), 1, 1, @current_time, @current_time),

(0, '设备维护保养', JSON_OBJECT(
    'task_title', '办公设备维护保养',
    'task_description', '定期对办公设备进行维护保养，确保设备正常运行',
    'required_workers', 1,
    'estimated_duration', 2,
    'hourly_rate', 80.00,
    'required_skills', JSON_ARRAY(3, 4),
    'worker_requirements', JSON_OBJECT('experience_years', 3, 'certification_required', true)
), (SELECT id FROM eb_task_categories WHERE category_code = 'equipment_maintenance'), 1, 1, @current_time, @current_time),

(0, '会议室布置', JSON_OBJECT(
    'task_title', '会议室布置服务',
    'task_description', '会议前的会议室布置，包括桌椅摆放、设备调试、茶水准备等',
    'required_workers', 2,
    'estimated_duration', 3,
    'hourly_rate', 45.00,
    'required_skills', JSON_ARRAY(5),
    'worker_requirements', JSON_OBJECT('experience_years', 1, 'certification_required', false)
), (SELECT id FROM eb_task_categories WHERE category_code = 'meeting_service'), 1, 1, @current_time, @current_time),

(0, '活动现场支持', JSON_OBJECT(
    'task_title', '企业活动现场支持',
    'task_description', '企业活动现场的各类支持服务，包括签到、引导、物料搬运等',
    'required_workers', 5,
    'estimated_duration', 8,
    'hourly_rate', 40.00,
    'required_skills', JSON_ARRAY(6),
    'worker_requirements', JSON_OBJECT('experience_years', 0, 'certification_required', false)
), (SELECT id FROM eb_task_categories WHERE category_code = 'event_support'), 1, 1, @current_time, @current_time);

-- ==================== 4. 创建系统配置数据 ====================

-- 插入支付配置
INSERT IGNORE INTO `eb_system_config` (`menu_name`, `type`, `input_type`, `config_tab_id`, `parameter`, `upload_type`, `required`, `width`, `high`, `value`, `info`, `desc`, `form_type`, `title`, `sort`, `status`) VALUES
('企业认证费用', 'text', 'number', 1, 'enterprise_auth_fee', '', 0, 0, 0, '0', '企业认证费用（元）', '企业用户认证需要支付的费用', 'input', '企业认证费用', 100, 1),
('平台服务费率', 'text', 'number', 1, 'platform_fee_rate', '', 0, 0, 0, '5', '平台服务费率（%）', '平台收取的服务费率', 'input', '平台服务费率', 101, 1),
('最低定金比例', 'text', 'number', 1, 'min_deposit_rate', '', 0, 0, 0, '20', '最低定金比例（%）', '任务发布时的最低定金比例', 'input', '最低定金比例', 102, 1),
('企业用户等级', 'text', 'textarea', 1, 'enterprise_levels', '', 0, 0, 0, '{"1":"普通企业","2":"认证企业","3":"优质企业","4":"战略合作","5":"VIP企业"}', '企业用户等级配置', '企业用户等级定义', 'textarea', '企业用户等级', 103, 1);

-- ==================== 5. 创建消息模板数据 ====================

-- 插入消息模板
INSERT IGNORE INTO `eb_system_notification_template` (`type`, `title`, `content`, `status`, `create_time`) VALUES
('enterprise_auth_submit', '企业认证申请提交', '您的企业认证申请已提交，我们将在3个工作日内完成审核。', 1, @current_time),
('enterprise_auth_approved', '企业认证审核通过', '恭喜！您的企业认证已审核通过，现在可以享受企业用户专属服务。', 1, @current_time),
('enterprise_auth_rejected', '企业认证审核未通过', '很抱歉，您的企业认证审核未通过。原因：{reason}。请重新提交申请。', 1, @current_time),
('task_published', '任务发布成功', '您的任务"{task_title}"已成功发布，师傅们可以开始申请了。', 1, @current_time),
('task_application_received', '收到任务申请', '您的任务"{task_title}"收到新的申请，请及时查看并处理。', 1, @current_time),
('task_completed', '任务已完成', '您的任务"{task_title}"已完成，请确认并进行评价。', 1, @current_time),
('payment_success', '支付成功', '您的订单支付成功，金额：￥{amount}。', 1, @current_time),
('settlement_completed', '结算完成', '任务"{task_title}"的结算已完成，金额：￥{amount}。', 1, @current_time);

-- ==================== 6. 迁移现有企业用户数据 ====================

-- 为现有企业用户创建企业档案
INSERT IGNORE INTO `eb_enterprise_profiles` (
    `user_id`, `enterprise_no`, `enterprise_name`, `enterprise_type`, 
    `legal_person`, `contact_person`, `contact_phone`, `business_license`,
    `enterprise_address`, `auth_status`, `create_time`, `update_time`
)
SELECT 
    u.uid,
    CONCAT('E', DATE_FORMAT(FROM_UNIXTIME(u.add_time), '%y%m%d'), LPAD(u.uid, 4, '0')) as enterprise_no,
    COALESCE(NULLIF(u.enterprise_name, ''), CONCAT('企业', u.uid)) as enterprise_name,
    'company' as enterprise_type,
    COALESCE(NULLIF(u.enterprise_contact, ''), u.real_name, u.nickname) as legal_person,
    COALESCE(NULLIF(u.enterprise_contact, ''), u.real_name, u.nickname) as contact_person,
    u.phone as contact_phone,
    COALESCE(u.enterprise_license, '') as business_license,
    COALESCE(u.enterprise_address, '') as enterprise_address,
    CASE 
        WHEN u.verification_status = 2 THEN 'approved'
        WHEN u.verification_status = 1 THEN 'reviewing'
        WHEN u.verification_status = -1 THEN 'rejected'
        ELSE 'pending'
    END as auth_status,
    u.add_time as create_time,
    @current_time as update_time
FROM `eb_user` u
INNER JOIN `eb_user_role_relations` urr ON u.uid = urr.user_id
INNER JOIN `eb_user_roles` ur ON urr.role_id = ur.id AND ur.role_code = 'enterprise'
WHERE u.uid > 0;

-- ==================== 7. 更新现有任务数据 ====================

-- 为现有任务添加企业信息
UPDATE `eb_tasks` t 
INNER JOIN `eb_enterprise_profiles` ep ON t.user_id = ep.user_id
SET t.enterprise_id = ep.id,
    t.contact_person = ep.contact_person,
    t.contact_phone = ep.contact_phone,
    t.task_type = 'simple',
    t.payment_type = 'hourly',
    t.deposit_rate = 20.00
WHERE t.enterprise_id IS NULL;

-- ==================== 8. 创建统计数据 ====================

-- 更新企业统计数据
UPDATE `eb_enterprise_profiles` ep SET 
    task_count = (
        SELECT COUNT(*) FROM eb_tasks t WHERE t.enterprise_id = ep.id
    ),
    completed_count = (
        SELECT COUNT(*) FROM eb_tasks t WHERE t.enterprise_id = ep.id AND t.status = 'completed'
    ),
    total_amount = (
        SELECT COALESCE(SUM(t.hourly_rate * COALESCE(ta.work_hours, t.estimated_duration)), 0)
        FROM eb_tasks t 
        LEFT JOIN eb_task_applications ta ON t.id = ta.task_id AND ta.status = 'completed'
        WHERE t.enterprise_id = ep.id AND t.status = 'completed'
    );

-- 更新用户统计数据
UPDATE `eb_user` u SET 
    task_published = (
        SELECT COUNT(*) FROM eb_tasks t WHERE t.user_id = u.uid
    ),
    task_completed = (
        SELECT COUNT(*) FROM eb_tasks t WHERE t.user_id = u.uid AND t.status = 'completed'
    ),
    total_spent = (
        SELECT COALESCE(SUM(po.actual_amount), 0)
        FROM eb_payment_orders po WHERE po.user_id = u.uid AND po.status = 'paid'
    );

-- ==================== 9. 创建索引优化 ====================

-- 为新表创建必要的索引
ALTER TABLE `eb_enterprise_profiles` ADD INDEX `idx_auth_status_type` (`auth_status`, `enterprise_type`);
ALTER TABLE `eb_enterprise_profiles` ADD INDEX `idx_credit_rating_score` (`credit_rating`, `credit_score`);

ALTER TABLE `eb_task_templates` ADD INDEX `idx_public_category` (`is_public`, `category_id`);
ALTER TABLE `eb_task_templates` ADD INDEX `idx_use_count` (`use_count`);

ALTER TABLE `eb_worker_favorites` ADD INDEX `idx_create_time` (`create_time`);

ALTER TABLE `eb_task_reviews` ADD INDEX `idx_rating_time` (`rating`, `create_time`);

ALTER TABLE `eb_payment_orders` ADD INDEX `idx_status_time` (`status`, `create_time`);
ALTER TABLE `eb_payment_orders` ADD INDEX `idx_payment_type` (`payment_type`);

-- ==================== 10. 数据完整性检查 ====================

SELECT 
    '企业档案' as table_name,
    COUNT(*) as record_count
FROM eb_enterprise_profiles
UNION ALL
SELECT 
    '任务模板' as table_name,
    COUNT(*) as record_count
FROM eb_task_templates
UNION ALL
SELECT 
    '企业用户角色' as table_name,
    COUNT(*) as record_count
FROM eb_user_role_relations urr
JOIN eb_user_roles ur ON urr.role_id = ur.id
WHERE ur.role_code = 'enterprise';

-- ==================== 完成提示 ====================
SELECT 'User enterprise data initialization completed successfully!' as message;
