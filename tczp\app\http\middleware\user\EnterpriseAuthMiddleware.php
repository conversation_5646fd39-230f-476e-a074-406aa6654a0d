<?php
declare(strict_types=1);

namespace app\http\middleware\user;

use app\Request;
use app\services\user\EnterpriseAuthServices;
use crmeb\interfaces\MiddlewareInterface;
use think\Response;

/**
 * 企业认证状态中间件
 * Class EnterpriseAuthMiddleware
 * @package app\http\middleware\user
 */
class EnterpriseAuthMiddleware implements MiddlewareInterface
{
    /**
     * 处理请求
     * @param Request $request
     * @param \Closure $next
     * @param string $requiredStatus 需要的认证状态
     * @return Response
     */
    public function handle(Request $request, \Closure $next, string $requiredStatus = 'approved')
    {
        try {
            // 获取用户ID
            $uid = $request->uid ?? 0;
            
            if (!$uid) {
                return $this->forbiddenResponse('用户未登录');
            }
            
            // 检查是否为企业用户
            /** @var EnterpriseAuthServices $enterpriseAuthService */
            $enterpriseAuthService = app()->make(EnterpriseAuthServices::class);
            
            if (!$enterpriseAuthService->isEnterprise($uid)) {
                return $this->forbiddenResponse('需要企业用户权限');
            }
            
            // 获取企业信息
            $enterpriseInfo = $enterpriseAuthService->getEnterpriseInfo($uid);
            
            if (!$enterpriseInfo) {
                return $this->forbiddenResponse('企业信息不存在，请先申请企业认证');
            }
            
            // 检查认证状态
            if (!$this->checkAuthStatus($enterpriseInfo['auth_status'], $requiredStatus)) {
                return $this->getAuthStatusResponse($enterpriseInfo['auth_status']);
            }
            
            return $next($request);
            
        } catch (\Exception $e) {
            return $this->forbiddenResponse($e->getMessage());
        }
    }
    
    /**
     * 检查认证状态
     * @param string $currentStatus
     * @param string $requiredStatus
     * @return bool
     */
    private function checkAuthStatus(string $currentStatus, string $requiredStatus): bool
    {
        // 定义状态优先级
        $statusPriority = [
            'pending' => 1,
            'reviewing' => 2,
            'approved' => 3,
            'rejected' => 0
        ];
        
        $currentPriority = $statusPriority[$currentStatus] ?? 0;
        $requiredPriority = $statusPriority[$requiredStatus] ?? 0;
        
        return $currentPriority >= $requiredPriority;
    }
    
    /**
     * 根据认证状态返回相应响应
     * @param string $authStatus
     * @return Response
     */
    private function getAuthStatusResponse(string $authStatus): Response
    {
        switch ($authStatus) {
            case 'pending':
                return $this->forbiddenResponse('企业认证申请尚未提交，请先完成企业认证申请');
            case 'reviewing':
                return $this->forbiddenResponse('企业认证正在审核中，请耐心等待');
            case 'rejected':
                return $this->forbiddenResponse('企业认证已被拒绝，请重新提交认证申请');
            default:
                return $this->forbiddenResponse('企业认证状态异常');
        }
    }
    
    /**
     * 返回权限不足响应
     * @param string $message
     * @return Response
     */
    private function forbiddenResponse(string $message): Response
    {
        return app('json')->make(403, $message);
    }
}
