<?php
use crmeb\utils\ApiErrorCode;

return [
    'SUCCESS' => '操作成功',
    'Successful operation' => '操作成功',
    'ERROR' => '操作失败',
    'Landing overdue' => '登录过期',
    'Modified success' => '修改成功',
    'Modification failed' => '修改失败',
    'You do not have permission to access for the time being' => '您暂无权限访问',
    'Interface is not authorized, you cannot access' => '接口未授权，您无法访问',
    'Failed to get administrator ID' => '管理员id获取失败',
    'Token is invalid, please login' => 'token失效,请登录',
    'Please login' => '请登录',
    'The login status is incorrect. Please login again.' => '登录状态有误,请重新登录',

    //订单错误提示语言
    'Data does not exist!' => '数据不存在',
    'Write off successfully' => '核销成功',
    'Write off failure' => '核销失败',
    'Order written off' => '订单已核销',
    'Write off order does not exist' => '核销订单不存在',
    'Lack of write-off code' => '缺少核销码',
    'Missing order ID' => '缺少订单id',
    'Please enter the total price' => '请输入商品总价',
    'Please enter the actual payment amount' => '请输入实际支付金额',
    'Failed to write off the group order' => '拼团订单暂未成功无法核销',
    'Parent classification error' => '父级分类错误',
    'There are attachments under the category. Please delete them first' => '分类下面有附件,请先删除附件',

    //上传配置错误提示语言
    'Please configure accessKey and secretKey' => '请设置上传配置的accessKey和secretKey',
    'Upload failure' => '上传失败',
    'Upload file does not exist' => '上传文件不存在',
    'COS bucket cannot be null' => '腾讯云bucket不能为空',
    'COS allowPrefix cannot be null' => '腾讯云允许前缀不能为空',
    'durationSeconds must be a int type' => 'durationSeconds必须是int类型',
    'get cam failed' => '获取cam失败',
    'Failed to generate upload directory, please check the permission!' => '生成上传目录失败，请检查权限！',

    //易联云
    'request was aborted' => '请求已中止',
    'Accesstoken has expired' => '访问窗口已过期',

    //云信短信
    'Mobile number cannot be empty' => '手机号码不能为空',
    'Account does not exist' => '短信帐户不存在',
    'Access token does not exist' => '访问令牌不存在',
    'Missing template number' => '缺少模板号',

    //订阅消息&模板消息
    'Template number does not exist' => '模板号不存在',
    'Template ID does not exist' => '模板ID不存在',
    'Openid does not exist' => 'Openid不存在',

    'Upload filesize error' => '上传文件大小超出系统设置,请重新选择',
    'Upload fileExt error' => '上传文件后缀不允许,请重新选择',
    'Upload fileMine error' => '上传文件类型不允许,请重新选择',

    ApiErrorCode::ERR_SAVE_TOKEN[1] => '保存token失败',

    ApiErrorCode::ERROR_WECHAT_MESSAGE[45008] => '图文消息超过限制',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[45007] => '语音播放时间超过限制',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[45006] => '图片链接字段超过限制',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[45005] => '链接字段超过限制',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[45004] => '描述字段超过限制',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[45003] => '标题字段超过限制',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[45002] => '消息内容超过限制',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[45001] => '多媒体文件大小超过限制',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[44004] => '文本消息内容为空',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[44003] => '图文消息内容为空',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[44002] => 'POST 的数据包为空',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[44001] => '多媒体文件为空',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[43019] => '需要将接收者从黑名单中移除',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[43005] => '需要好友关系',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[43004] => '需要接收者关注',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[43003] => '需要 HTTPS 请求',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[43002] => '需要 POST 请求',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[43001] => '需要 GET 请求',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[42007] => '用户修改微信密码， accesstoken 和 refreshtoken 失效，需要重新授权',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[42003] => 'oauth_code 超时',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[42002] => 'refresh_token 超时',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[42001] => 'access_token 超时，请检查 access_token 的有效期，请参考基础支持 - 获取 access_token 中，对 access_token 的详细机制说明',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[41009] => '缺少 openid',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[41008] => '缺少 oauth code',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[41007] => '缺少子菜单数据',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[41006] => '缺少 media_id 参数',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[41005] => '缺少多媒体文件数据',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[41004] => '缺少 secret 参数',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[41003] => '缺少 refresh_token 参数',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[41002] => '缺少 appid 参数',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[41001] => '缺少 access_token 参数',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40163] => 'oauth_code已使用',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40155] => '请勿添加其他公众号的主页链接',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40137] => '不支持的图片格式',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40132] => '微信号不合法',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40125] => '无效的appsecret',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40121] => '不合法的 media_id 类型',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40120] => '子 button 类型错误',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40119] => 'button 类型错误',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40118] => 'media_id 大小不合法',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40117] => '分组名字不合法',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40117] => '分组名字不合法',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40060] => '删除单篇图文时，指定的 article_idx 不合法',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40051] => '分组名字不合法',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40050] => '不合法的分组 id',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40040] => '无效的url',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40039] => '不合法的 URL 长度',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40035] => '不合法的参数',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40038] => '不合法的请求格式',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40033] => '不合法的请求字符，不能包含 \uxxxx 格式的字符',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40032] => '不合法的 openid 列表长度',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40031] => '不合法的 openid 列表',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40030] => '不合法的 refresh_token',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40029] => '无效的 oauth_code',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40028] => '不合法的自定义菜单使用用户',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40027] => '不合法的子菜单按钮 URL 长度',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40026] => '不合法的子菜单按钮 KEY 长度',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40025] => '不合法的子菜单按钮名字长度',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40024] => '不合法的子菜单按钮类型',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40023] => '不合法的子菜单按钮个数',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40022] => '不合法的子菜单级数',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40021] => '不合法的菜单版本号',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40020] => '不合法的按钮 URL 长度',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40019] => '不合法的按钮 KEY 长度',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40018] => '不合法的按钮名字长度',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40017] => '不合法的按钮类型',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40016] => '不合法的按钮个数',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40015] => '不合法的菜单类型',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40014] => '不合法的 access_token ，请开发者认真比对 access_token 的有效性（如是否过期），或查看是否正在为恰当的公众号调用接口',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40013] => '不合法的 AppID ，请开发者检查 AppID 的正确性，避免异常字符，注意大小写',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40012] => '不合法的缩略图文件大小',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40011] => '不合法的视频文件大小',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40010] => '不合法的语音文件大小',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40009] => '不合法的图片文件大小',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40008] => '不合法的消息类型',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40007] => '不合法的媒体文件 id',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40006] => '不合法的文件大小',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40005] => '不合法的文件类型',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40004] => '不合法的媒体文件类型',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40003] => '不合法的 OpenID ，请开发者确认 OpenID （该用户）是否已关注公众号，或是否是其他公众号的 OpenID',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40002] => '不合法的凭证类型',
    ApiErrorCode::ERROR_WECHAT_MESSAGE[40001] => '获取 access_token 时 AppSecret 错误，或者 access_token 无效。请开发者认真比对 AppSecret 的正确性，或查看是否正在为恰当的公众号调用接口',

];
