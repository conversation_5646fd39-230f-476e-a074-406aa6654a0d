<?php

namespace app\jobs\notice;


use crmeb\basic\BaseJobs;
use crmeb\services\printer\Printer;
use crmeb\traits\QueueTrait;


/**
 * 小票打印
 * Class PrintJob
 * @package app\jobs\notice
 */
class PrintJob extends BaseJobs
{
    use QueueTrait;

    /**
     * 小票打印
     * @param $type
     * @param $configdata
     * @param $order
     * @param $product
     * @return bool
     */
    public function doJob($type, $configdata, $order, $product)
    {
        try {
            $printer = new Printer($type, $configdata);
            $printer->setPrinterContent([
                'name' => sys_config('site_name'),
                'orderInfo' => is_object($order) ? $order->toArray() : $order,
                'product' => $product
            ])->startPrinter();
        } catch (\Throwable $e) {
            response_log_write([
                'message' => '小票打印失败失败,失败原因:' . $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
		return true;
    }

}
