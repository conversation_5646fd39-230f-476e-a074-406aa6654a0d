<?php
namespace app\listener\notice;

use app\services\message\NoticeService;
use crmeb\interfaces\ListenerInterface;

/**
 * 发送消息
 * Class Create
 * @package app\listener\order
 */
class Notice implements ListenerInterface
{

    /**
     * @param $event
     * <AUTHOR>
     * @email <EMAIL>
     * @date 2023/9/20
     */
    public function handle($event): void
    {
        try {
            [$data, $mark] = $event;

            app()->make(NoticeService::class)->setEvent($mark)->handle($data);

        } catch (\Throwable $e) {
            response_log_write([
                'message' => '发送消息错误' . $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
    }
}
