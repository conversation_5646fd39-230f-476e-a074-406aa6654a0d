-- ==================== 数据迁移脚本 ====================
-- 文件: 03_data_migration.sql
-- 描述: 将现有数据迁移到新的数据库结构中
-- 执行顺序: 第三步执行（在01和02之后）
-- 作者: TCZP Team
-- 日期: 2024-12-19
-- 注意: 执行前请备份数据库！

-- 设置时间变量
SET @current_time = UNIX_TIMESTAMP();

-- ==================== 1. 迁移用户角色数据 ====================

-- 1.1 为现有用户分配角色（基于现有的identity字段）
INSERT IGNORE INTO `eb_user_role_relations` (`user_id`, `role_id`, `status`, `create_time`)
SELECT 
    u.uid,
    CASE 
        WHEN u.identity = 2 THEN (SELECT id FROM eb_user_roles WHERE role_code = 'admin')
        WHEN u.is_channel = 1 THEN (SELECT id FROM eb_user_roles WHERE role_code = 'enterprise')
        WHEN EXISTS (SELECT 1 FROM eb_worker_skills WHERE worker_id = u.uid) THEN (SELECT id FROM eb_user_roles WHERE role_code = 'worker')
        ELSE (SELECT id FROM eb_user_roles WHERE role_code = 'customer')
    END as role_id,
    1,
    @current_time
FROM `eb_user` u
WHERE u.uid > 0;

-- 1.2 检查角色分配结果
SELECT 
    ur.role_name,
    COUNT(*) as user_count
FROM eb_user_role_relations urr
JOIN eb_user_roles ur ON urr.role_id = ur.id
WHERE urr.status = 1
GROUP BY ur.role_name;

-- ==================== 2. 迁移师傅信息数据 ====================

-- 2.1 从现有数据创建师傅档案
INSERT IGNORE INTO `eb_worker_profiles` (
    `user_id`, `worker_no`, `real_name`, `id_card`, `phone`, 
    `work_experience`, `service_areas`, `auth_status`, 
    `rating`, `order_count`, `completion_rate`,
    `create_time`, `update_time`
)
SELECT 
    u.uid,
    CONCAT('W', DATE_FORMAT(FROM_UNIXTIME(u.add_time), '%y%m%d'), LPAD(u.uid, 4, '0')) as worker_no,
    COALESCE(NULLIF(u.real_name, ''), u.nickname, CONCAT('师傅', u.uid)) as real_name,
    COALESCE(u.card_id, '') as id_card,
    u.phone,
    COALESCE(ws.experience_years, 0) as work_experience,
    COALESCE(ws.service_areas, '[]') as service_areas,
    CASE 
        WHEN ua.status = 'approved' THEN 'approved'
        WHEN ua.status = 'rejected' THEN 'rejected'
        WHEN ua.status = 'pending' THEN 'reviewing'
        ELSE 'pending'
    END as auth_status,
    5.00 as rating, -- 默认评分
    0 as order_count, -- 默认接单数
    0.00 as completion_rate, -- 默认完成率
    u.add_time as create_time,
    @current_time as update_time
FROM `eb_user` u
INNER JOIN `eb_user_role_relations` urr ON u.uid = urr.user_id
INNER JOIN `eb_user_roles` ur ON urr.role_id = ur.id AND ur.role_code = 'worker'
LEFT JOIN `eb_worker_skills` ws ON u.uid = ws.worker_id
LEFT JOIN `eb_user_auth` ua ON u.uid = ua.user_id AND ua.auth_type = 'real_name'
WHERE u.uid > 0
GROUP BY u.uid;

-- 2.2 检查师傅档案迁移结果
SELECT 
    auth_status,
    COUNT(*) as worker_count
FROM eb_worker_profiles
GROUP BY auth_status;

-- ==================== 3. 迁移师傅技能数据 ====================

-- 3.1 迁移师傅技能关联数据（基于现有的worker_skills表）
INSERT IGNORE INTO `eb_worker_skills_new` (
    `worker_id`, `skill_id`, `skill_level`, `experience_years`, 
    `hourly_rate`, `auth_status`, `create_time`, `update_time`
)
SELECT 
    wp.id as worker_id,
    -- 根据现有技能标签匹配新的技能ID
    CASE 
        WHEN JSON_CONTAINS(ws.skill_tags, '"清洁"') OR JSON_CONTAINS(ws.skill_tags, '"保洁"') 
            THEN (SELECT id FROM eb_skills_new WHERE skill_code = 'daily_cleaning' LIMIT 1)
        WHEN JSON_CONTAINS(ws.skill_tags, '"维修"') OR JSON_CONTAINS(ws.skill_tags, '"修理"')
            THEN (SELECT id FROM eb_skills_new WHERE skill_code = 'ac_repair' LIMIT 1)
        WHEN JSON_CONTAINS(ws.skill_tags, '"搬家"') OR JSON_CONTAINS(ws.skill_tags, '"搬运"')
            THEN (SELECT id FROM eb_skills_new WHERE skill_code = 'small_moving' LIMIT 1)
        WHEN JSON_CONTAINS(ws.skill_tags, '"保姆"') OR JSON_CONTAINS(ws.skill_tags, '"家政"')
            THEN (SELECT id FROM eb_skills_new WHERE skill_code = 'hourly_worker' LIMIT 1)
        WHEN JSON_CONTAINS(ws.skill_tags, '"水电"') OR JSON_CONTAINS(ws.skill_tags, '"安装"')
            THEN (SELECT id FROM eb_skills_new WHERE skill_code = 'plumbing_install' LIMIT 1)
        ELSE (SELECT id FROM eb_skills_new WHERE skill_code = 'daily_cleaning' LIMIT 1)
    END as skill_id,
    CASE 
        WHEN ws.experience_years >= 5 THEN 'expert'
        WHEN ws.experience_years >= 3 THEN 'advanced'
        WHEN ws.experience_years >= 1 THEN 'intermediate'
        ELSE 'beginner'
    END as skill_level,
    COALESCE(ws.experience_years, 0) as experience_years,
    50.00 as hourly_rate, -- 默认时薪
    'approved' as auth_status,
    @current_time as create_time,
    @current_time as update_time
FROM `eb_worker_profiles` wp
INNER JOIN `eb_user` u ON wp.user_id = u.uid
LEFT JOIN `eb_worker_skills` ws ON u.uid = ws.worker_id
WHERE ws.worker_id IS NOT NULL
AND ws.skill_tags IS NOT NULL
AND ws.skill_tags != '[]';

-- 3.2 为没有技能的师傅添加默认技能
INSERT IGNORE INTO `eb_worker_skills_new` (
    `worker_id`, `skill_id`, `skill_level`, `experience_years`, 
    `hourly_rate`, `auth_status`, `create_time`, `update_time`
)
SELECT 
    wp.id as worker_id,
    (SELECT id FROM eb_skills_new WHERE skill_code = 'daily_cleaning' LIMIT 1) as skill_id,
    'beginner' as skill_level,
    0 as experience_years,
    40.00 as hourly_rate,
    'pending' as auth_status,
    @current_time as create_time,
    @current_time as update_time
FROM `eb_worker_profiles` wp
WHERE NOT EXISTS (
    SELECT 1 FROM eb_worker_skills_new wsn WHERE wsn.worker_id = wp.id
);

-- ==================== 4. 更新现有任务表数据 ====================

-- 4.1 为现有任务添加新字段的默认值
UPDATE `eb_tasks` SET 
    `required_skills` = '[]',
    `difficulty_level` = 1,
    `estimated_duration` = 2,
    `worker_requirements` = '{}'
WHERE `required_skills` IS NULL;

-- 4.2 根据任务描述智能设置技能要求
UPDATE `eb_tasks` t SET 
    `required_skills` = CASE 
        WHEN t.task_description LIKE '%清洁%' OR t.task_description LIKE '%保洁%' 
            THEN JSON_ARRAY((SELECT id FROM eb_skills_new WHERE skill_code = 'daily_cleaning'))
        WHEN t.task_description LIKE '%维修%' OR t.task_description LIKE '%修理%'
            THEN JSON_ARRAY((SELECT id FROM eb_skills_new WHERE skill_code = 'ac_repair'))
        WHEN t.task_description LIKE '%搬家%' OR t.task_description LIKE '%搬运%'
            THEN JSON_ARRAY((SELECT id FROM eb_skills_new WHERE skill_code = 'small_moving'))
        WHEN t.task_description LIKE '%保姆%' OR t.task_description LIKE '%家政%'
            THEN JSON_ARRAY((SELECT id FROM eb_skills_new WHERE skill_code = 'hourly_worker'))
        ELSE '[]'
    END,
    `difficulty_level` = CASE 
        WHEN t.hourly_rate >= 100 THEN 4
        WHEN t.hourly_rate >= 60 THEN 3
        WHEN t.hourly_rate >= 40 THEN 2
        ELSE 1
    END
WHERE t.required_skills = '[]';

-- ==================== 5. 创建认证记录 ====================

-- 5.1 为已认证的师傅创建认证记录
INSERT IGNORE INTO `eb_worker_auth_records` (
    `worker_id`, `auth_type`, `auth_data`, `status`, 
    `submit_time`, `review_time`, `create_time`
)
SELECT 
    wp.id as worker_id,
    'identity' as auth_type,
    JSON_OBJECT(
        'real_name', wp.real_name,
        'id_card', wp.id_card,
        'phone', wp.phone
    ) as auth_data,
    CASE 
        WHEN wp.auth_status = 'approved' THEN 'approved'
        WHEN wp.auth_status = 'rejected' THEN 'rejected'
        WHEN wp.auth_status = 'reviewing' THEN 'reviewing'
        ELSE 'pending'
    END as status,
    wp.create_time as submit_time,
    CASE WHEN wp.auth_time > 0 THEN wp.auth_time ELSE 0 END as review_time,
    @current_time as create_time
FROM `eb_worker_profiles` wp
WHERE wp.auth_status IN ('approved', 'rejected', 'reviewing');

-- ==================== 6. 数据完整性检查和统计 ====================

-- 6.1 检查数据迁移结果
SELECT 
    '用户角色关联' as table_name,
    COUNT(*) as record_count
FROM eb_user_role_relations
UNION ALL
SELECT 
    '师傅档案' as table_name,
    COUNT(*) as record_count
FROM eb_worker_profiles
UNION ALL
SELECT 
    '师傅技能关联' as table_name,
    COUNT(*) as record_count
FROM eb_worker_skills_new
UNION ALL
SELECT 
    '认证记录' as table_name,
    COUNT(*) as record_count
FROM eb_worker_auth_records;

-- 6.2 师傅状态统计
SELECT 
    '师傅认证状态统计' as report_type,
    wp.auth_status,
    COUNT(*) as count
FROM eb_worker_profiles wp
GROUP BY wp.auth_status
UNION ALL
SELECT 
    '师傅技能统计' as report_type,
    wsn.auth_status,
    COUNT(*) as count
FROM eb_worker_skills_new wsn
GROUP BY wsn.auth_status;

-- 6.3 技能分布统计
SELECT 
    '技能分布统计' as report_type,
    s.skill_name,
    COUNT(wsn.id) as worker_count
FROM eb_skills_new s
LEFT JOIN eb_worker_skills_new wsn ON s.id = wsn.skill_id
GROUP BY s.id, s.skill_name
ORDER BY worker_count DESC
LIMIT 10;

-- ==================== 7. 创建必要的索引 ====================

-- 7.1 为新表创建性能优化索引
ALTER TABLE `eb_worker_profiles` ADD INDEX `idx_auth_status_rating` (`auth_status`, `rating`);
ALTER TABLE `eb_worker_profiles` ADD INDEX `idx_work_status_create_time` (`work_status`, `create_time`);

ALTER TABLE `eb_worker_skills_new` ADD INDEX `idx_auth_status_skill_level` (`auth_status`, `skill_level`);
ALTER TABLE `eb_worker_skills_new` ADD INDEX `idx_hourly_rate` (`hourly_rate`);

ALTER TABLE `eb_worker_auth_records` ADD INDEX `idx_auth_type_status_time` (`auth_type`, `status`, `submit_time`);

-- ==================== 完成提示 ====================
SELECT 'Data migration completed successfully!' as message,
       'Please verify the data integrity before proceeding to the next step.' as note;
