<?php
declare(strict_types=1);

namespace app\http\middleware\staff;

use app\Request;
use app\services\user\UserAuthServices;
use app\services\staff\StaffAuthServices;
use crmeb\interfaces\MiddlewareInterface;
use think\facade\Config;
use think\Response;

/**
 * 师傅端统一认证中间件
 * Class StaffAuthMiddleware
 * @package app\http\middleware\staff
 */
class StaffAuthMiddleware implements MiddlewareInterface
{
    /**
     * 处理请求
     * @param Request $request
     * @param \Closure $next
     * @param bool $force 是否强制验证
     * @return Response
     */
    public function handle(Request $request, \Closure $next, bool $force = true)
    {
        try {
            // 获取Token
            $token = $this->getToken($request);
            
            if (!$token && $force) {
                return $this->unauthorizedResponse('缺少认证Token');
            }
            
            if ($token) {
                // 解析Token获取用户信息
                $userInfo = $this->parseToken($token);
                
                // 验证用户是否为师傅
                $this->validateWorkerRole($userInfo);
                
                // 验证师傅状态
                $this->validateWorkerStatus($userInfo);
                
                // 设置请求上下文
                $this->setRequestContext($request, $userInfo);
            }
            
            return $next($request);
            
        } catch (\Exception $e) {
            if ($force) {
                return $this->unauthorizedResponse($e->getMessage());
            }
            return $next($request);
        }
    }
    
    /**
     * 获取Token
     * @param Request $request
     * @return string|null
     */
    private function getToken(Request $request): ?string
    {
        $tokenName = Config::get('cookie.token_name', 'Authorization');
        $token = trim(ltrim($request->header($tokenName), 'Bearer'));
        
        // 兼容处理
        if (!$token) {
            $token = trim(ltrim($request->header('Authori-zation'), 'Bearer'));
        }
        
        return $token ?: null;
    }
    
    /**
     * 解析Token
     * @param string $token
     * @return array
     * @throws \Exception
     */
    private function parseToken(string $token): array
    {
        /** @var UserAuthServices $userAuthService */
        $userAuthService = app()->make(UserAuthServices::class);
        
        $userInfo = $userAuthService->parseToken($token);
        
        if (!$userInfo) {
            throw new \Exception('Token无效或已过期');
        }
        
        return $userInfo;
    }
    
    /**
     * 验证师傅角色
     * @param array $userInfo
     * @throws \Exception
     */
    private function validateWorkerRole(array $userInfo): void
    {
        /** @var StaffAuthServices $staffAuthService */
        $staffAuthService = app()->make(StaffAuthServices::class);
        
        if (!$staffAuthService->isWorker($userInfo['uid'])) {
            throw new \Exception('用户不是师傅角色');
        }
    }
    
    /**
     * 验证师傅状态
     * @param array $userInfo
     * @throws \Exception
     */
    private function validateWorkerStatus(array $userInfo): void
    {
        /** @var StaffAuthServices $staffAuthService */
        $staffAuthService = app()->make(StaffAuthServices::class);
        
        $workerInfo = $staffAuthService->getWorkerInfo($userInfo['uid']);
        
        if (!$workerInfo) {
            throw new \Exception('师傅信息不存在');
        }
        
        // 检查师傅状态
        if ($workerInfo['auth_status'] === 'rejected') {
            throw new \Exception('师傅认证已被拒绝');
        }
        
        // 检查账户状态
        if ($userInfo['status'] != 1) {
            throw new \Exception('账户已被禁用');
        }
    }
    
    /**
     * 设置请求上下文
     * @param Request $request
     * @param array $userInfo
     */
    private function setRequestContext(Request $request, array $userInfo): void
    {
        // 设置用户ID
        $request->uid = (int)$userInfo['uid'];
        
        // 设置用户信息
        $request->userInfo = $userInfo;
        
        // 设置师傅信息
        /** @var StaffAuthServices $staffAuthService */
        $staffAuthService = app()->make(StaffAuthServices::class);
        $workerInfo = $staffAuthService->getWorkerInfo($userInfo['uid']);
        $request->workerInfo = $workerInfo;
        
        // 设置权限信息
        $permissions = $staffAuthService->getWorkerPermissions($userInfo['uid']);
        $request->permissions = $permissions;
    }
    
    /**
     * 返回未授权响应
     * @param string $message
     * @return Response
     */
    private function unauthorizedResponse(string $message): Response
    {
        return app('json')->make(401, $message);
    }
}
