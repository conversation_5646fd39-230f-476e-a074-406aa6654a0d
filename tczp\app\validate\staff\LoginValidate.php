<?php
namespace app\validate\staff;


use think\Validate;

class LoginValidate extends Validate
{
    protected $regex = ['account' => '/^[a-zA-Z0-9]{4,30}$/'];
    /**
     * @var string[]
     */
    protected $rule = [
        'appid' => ['require', 'account', 'length:4,64'],
        'appsecret' => ['require', 'length:4,64']
    ];

    /**
     * @var string[]
     */
    protected $message = [
        'appid.require' => '请填写账号',
        'appid.account' => '请填写正确的账号',
		'account.length' => '账号长度4-64位字符',
        'appsecret.require' => '请填写密码',
		'appsecret.length' => '密码长度4-64位字符',
    ];
}
