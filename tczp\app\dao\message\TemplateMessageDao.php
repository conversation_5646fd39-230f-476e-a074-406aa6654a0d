<?php

namespace app\dao\message;


use app\dao\BaseDao;
use app\model\message\TemplateMessage;

/**
 * 模板消息
 * Class TemplateMessageDao
 * @package app\dao\message
 */
class TemplateMessageDao extends BaseDao
{
    /**
     * 设置模型
     * @return string
     */
    protected function setModel(): string
    {
        return TemplateMessage::class;
    }

    /**
     * 获取一条数据
     * @param array $where
     * @param string $filed
     * @param bool $is_search
     * @return array|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getTemplateOne(array $where, string $filed = '*', bool $is_search = true)
    {
        if ($is_search) {
            return $this->search($where)->field($filed)->find();
        } else {
            return $this->getModel()->where($where)->find();
        }
    }

    /**
     * 获取模板消息列表
     * @param array $where
     * @param int $page
     * @param int $limit
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getTemplateList(array $where, int $page, int $limit)
    {

        return $this->getModel()->when(isset($where['name']) && $where['name'] != '', function ($query) use ($where) {
            $query->where('name', 'LIKE', "%$where[name]%");
        })->when(isset($where['status']) && $where['status'] != '', function ($query) use ($where) {
            $query->where('status', $where['status']);
        })->where('type', $where['type'])->page($page, $limit)->select()->toArray();
    }

}
