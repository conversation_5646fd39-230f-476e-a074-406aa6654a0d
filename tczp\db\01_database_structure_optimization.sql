-- ==================== 师傅端数据库结构优化 ====================
-- 文件: 01_database_structure_optimization.sql
-- 描述: 创建师傅端优化所需的新表结构
-- 执行顺序: 第一步执行
-- 作者: TCZP Team
-- 日期: 2024-12-19

-- ==================== 1. 用户角色表 ====================
DROP TABLE IF EXISTS `eb_user_roles`;
CREATE TABLE `eb_user_roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_code` varchar(50) NOT NULL COMMENT '角色代码',
  `role_name` varchar(100) NOT NULL COMMENT '角色名称',
  `description` varchar(255) DEFAULT NULL COMMENT '角色描述',
  `permissions` json DEFAULT NULL COMMENT '角色权限JSON',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1启用，0禁用',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `role_code` (`role_code`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色表';

-- ==================== 2. 用户角色关联表 ====================
DROP TABLE IF EXISTS `eb_user_role_relations`;
CREATE TABLE `eb_user_role_relations` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `role_id` int(11) NOT NULL COMMENT '角色ID',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1启用，0禁用',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_role` (`user_id`, `role_id`),
  KEY `user_id` (`user_id`),
  KEY `role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';

-- ==================== 3. 师傅信息表 ====================
DROP TABLE IF EXISTS `eb_worker_profiles`;
CREATE TABLE `eb_worker_profiles` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `worker_no` varchar(32) NOT NULL COMMENT '师傅编号',
  `real_name` varchar(50) NOT NULL COMMENT '真实姓名',
  `id_card` varchar(18) NOT NULL COMMENT '身份证号',
  `phone` varchar(15) NOT NULL COMMENT '手机号',
  `emergency_contact` varchar(15) DEFAULT NULL COMMENT '紧急联系人电话',
  `work_experience` int(11) NOT NULL DEFAULT '0' COMMENT '工作经验（年）',
  `service_areas` json DEFAULT NULL COMMENT '服务区域JSON',
  `work_status` enum('available','busy','offline') NOT NULL DEFAULT 'available' COMMENT '工作状态',
  `auth_status` enum('pending','reviewing','approved','rejected') NOT NULL DEFAULT 'pending' COMMENT '认证状态',
  `auth_time` int(11) NOT NULL DEFAULT '0' COMMENT '认证时间',
  `rating` decimal(3,2) NOT NULL DEFAULT '5.00' COMMENT '评分',
  `order_count` int(11) NOT NULL DEFAULT '0' COMMENT '接单数量',
  `completion_rate` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '完成率',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  UNIQUE KEY `worker_no` (`worker_no`),
  UNIQUE KEY `id_card` (`id_card`),
  KEY `auth_status` (`auth_status`),
  KEY `work_status` (`work_status`),
  KEY `rating` (`rating`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='师傅信息表';

-- ==================== 4. 技能分类表 ====================
DROP TABLE IF EXISTS `eb_skill_categories`;
CREATE TABLE `eb_skill_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `parent_id` int(11) NOT NULL DEFAULT '0' COMMENT '父级ID',
  `category_name` varchar(100) NOT NULL COMMENT '分类名称',
  `category_code` varchar(50) NOT NULL COMMENT '分类代码',
  `icon` varchar(255) DEFAULT NULL COMMENT '分类图标',
  `description` varchar(255) DEFAULT NULL COMMENT '分类描述',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1启用，0禁用',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `category_code` (`category_code`),
  KEY `parent_id` (`parent_id`),
  KEY `status` (`status`),
  KEY `sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='技能分类表';

-- ==================== 5. 技能表 ====================
DROP TABLE IF EXISTS `eb_skills_new`;
CREATE TABLE `eb_skills_new` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '技能ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `skill_name` varchar(100) NOT NULL COMMENT '技能名称',
  `skill_code` varchar(50) NOT NULL COMMENT '技能代码',
  `description` varchar(255) DEFAULT NULL COMMENT '技能描述',
  `icon` varchar(255) DEFAULT NULL COMMENT '技能图标',
  `price_range` varchar(50) DEFAULT NULL COMMENT '价格区间',
  `unit` varchar(20) DEFAULT NULL COMMENT '计价单位',
  `certification_required` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否需要认证',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1启用，0禁用',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `skill_code` (`skill_code`),
  KEY `category_id` (`category_id`),
  KEY `status` (`status`),
  KEY `sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='技能表';

-- ==================== 6. 师傅技能关联表 ====================
DROP TABLE IF EXISTS `eb_worker_skills_new`;
CREATE TABLE `eb_worker_skills_new` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `worker_id` int(11) NOT NULL COMMENT '师傅ID',
  `skill_id` int(11) NOT NULL COMMENT '技能ID',
  `skill_level` enum('beginner','intermediate','advanced','expert') NOT NULL DEFAULT 'beginner' COMMENT '技能等级',
  `experience_years` int(11) NOT NULL DEFAULT '0' COMMENT '经验年数',
  `hourly_rate` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '时薪',
  `certificate_images` json DEFAULT NULL COMMENT '证书图片JSON',
  `work_samples` json DEFAULT NULL COMMENT '作品样例JSON',
  `auth_status` enum('pending','approved','rejected') NOT NULL DEFAULT 'pending' COMMENT '认证状态',
  `auth_time` int(11) NOT NULL DEFAULT '0' COMMENT '认证时间',
  `reviewer_id` int(11) NOT NULL DEFAULT '0' COMMENT '审核员ID',
  `reject_reason` varchar(255) DEFAULT NULL COMMENT '拒绝原因',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `worker_skill` (`worker_id`, `skill_id`),
  KEY `worker_id` (`worker_id`),
  KEY `skill_id` (`skill_id`),
  KEY `auth_status` (`auth_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='师傅技能关联表';

-- ==================== 7. 认证记录表 ====================
DROP TABLE IF EXISTS `eb_worker_auth_records`;
CREATE TABLE `eb_worker_auth_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `worker_id` int(11) NOT NULL COMMENT '师傅ID',
  `auth_type` enum('identity','skill','qualification') NOT NULL COMMENT '认证类型',
  `auth_item_id` int(11) NOT NULL DEFAULT '0' COMMENT '认证项目ID（技能ID等）',
  `auth_data` json NOT NULL COMMENT '认证数据JSON',
  `status` enum('pending','reviewing','approved','rejected','expired') NOT NULL DEFAULT 'pending' COMMENT '认证状态',
  `submit_time` int(11) NOT NULL DEFAULT '0' COMMENT '提交时间',
  `review_time` int(11) NOT NULL DEFAULT '0' COMMENT '审核时间',
  `reviewer_id` int(11) NOT NULL DEFAULT '0' COMMENT '审核员ID',
  `reject_reason` varchar(255) DEFAULT NULL COMMENT '拒绝原因',
  `expire_time` int(11) NOT NULL DEFAULT '0' COMMENT '过期时间',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `worker_id` (`worker_id`),
  KEY `auth_type` (`auth_type`),
  KEY `status` (`status`),
  KEY `submit_time` (`submit_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='师傅认证记录表';

-- ==================== 8. 优化现有任务表 ====================
-- 为任务表添加新字段
ALTER TABLE `eb_tasks` 
ADD COLUMN `required_skills` json DEFAULT NULL COMMENT '需要的技能JSON' AFTER `required_workers`,
ADD COLUMN `difficulty_level` tinyint(1) NOT NULL DEFAULT '1' COMMENT '难度等级1-5' AFTER `required_skills`,
ADD COLUMN `estimated_duration` int(11) NOT NULL DEFAULT '0' COMMENT '预估工期（小时）' AFTER `difficulty_level`,
ADD COLUMN `worker_requirements` json DEFAULT NULL COMMENT '师傅要求JSON' AFTER `estimated_duration`;

-- 添加索引
ALTER TABLE `eb_tasks` ADD INDEX `idx_required_skills` (`required_skills`(255));
ALTER TABLE `eb_tasks` ADD INDEX `idx_difficulty_level` (`difficulty_level`);

-- ==================== 9. 优化任务申请表 ====================
-- 为任务申请表添加新字段
ALTER TABLE `eb_task_applications`
ADD COLUMN `skill_match_score` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '技能匹配度' AFTER `quoted_price`,
ADD COLUMN `distance` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '距离（公里）' AFTER `skill_match_score`,
ADD COLUMN `estimated_start_time` int(11) NOT NULL DEFAULT '0' COMMENT '预计开始时间' AFTER `distance`,
ADD COLUMN `estimated_completion_time` int(11) NOT NULL DEFAULT '0' COMMENT '预计完成时间' AFTER `estimated_start_time`;

-- 添加索引
ALTER TABLE `eb_task_applications` ADD INDEX `idx_skill_match_score` (`skill_match_score`);
ALTER TABLE `eb_task_applications` ADD INDEX `idx_distance` (`distance`);

-- ==================== 完成提示 ====================
SELECT 'Database structure optimization completed successfully!' as message;
