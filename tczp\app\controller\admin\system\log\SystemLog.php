<?php
namespace app\controller\admin\system\log;

use app\controller\admin\AuthController;
use app\services\system\admin\SystemAdminServices;
use app\services\system\log\SystemLogServices;

/**
 * 管理员操作记录表控制器
 * Class SystemLog
 * @package app\controller\admin\v1\system
 */
class SystemLog extends AuthController
{

    /**
     * @var SystemLogServices
     */
    protected SystemLogServices $services;

    /**
     * 构造方法
     * SystemLog constructor.
     * @param SystemLogServices $services
     * @throws \Exception
     */
    public function __construct(SystemLogServices $services)
    {
        parent::__construct();
        $this->services = $services;
        $this->services->deleteLog();
    }

    /**
     * 显示操作记录
     */
    public function index()
    {

        $where = $this->request->getMore([
            ['pages', ''],
            ['path', ''],
            ['ip', ''],
            ['admin_id', ''],
            ['data', '', '', 'time'],
        ]);
        return $this->success($this->services->getLogList($where, (int)$this->adminInfo['level']));
    }

    public function search_admin(SystemAdminServices $services)
    {
        $info = $services->getOrdAdmin('id,real_name', $this->adminInfo['level']);
        return $this->success(compact('info'));
    }

}

