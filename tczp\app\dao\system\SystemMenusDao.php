<?php

namespace app\dao\system;

use app\dao\BaseDao;
use app\model\system\SystemMenus;

/**
 * 菜单dao层
 * Class SystemMenusDao
 * @package app\dao\system
 */
class SystemMenusDao extends BaseDao
{

    /**
     * 设置模型
     * @return string
     */
    protected function setModel(): string
    {
        return SystemMenus::class;
    }

    /**
     * 获取权限菜单列表
     * @param array $where
     * @param array|null $field
     * @return array|\crmeb\basic\BaseModel[]|\think\Collection
     * @throws \ReflectionException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getMenusRoule(array $where, ?array $field = [])
    {
        if (!$field) {
            $field = ['id', 'menu_name', 'icon', 'pid', 'sort', 'menu_path', 'is_show', 'header', 'is_header', 'is_show_path'];
        }
        return $this->search($where)->field($field)->order('sort DESC,id DESC')->failException(false)->select();
    }

    /**
     * 获取菜单中的唯一权限
     * @param array $where
     * @return array
     */
    public function getMenusUnique(array $where)
    {
        return $this->search($where)->where('unique_auth', '<>', '')->column('unique_auth', '');
    }

    /**
     * 根据访问地址获得菜单名
     * @param string $rule
     * @return mixed
     */
    public function getVisitName(string $rule)
    {
        return $this->search(['url' => $rule])->value('menu_name');
    }

    /**
     * 获取后台菜单列表并分页
     * @param array $where
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getMenusList(array $where)
    {
        $where = array_merge($where, ['is_del' => 0]);
        return $this->search($where)->order('sort DESC,id ASC')->select();
    }

    /**
     * 指定条件获取某些菜单的名称以数组形式返回
     * @param array $where
     * @param string $field
     * @param string $key
     * @return array
     */
    public function column(array $where, string $field, string $key)
    {
        return $this->search($where)->column($field, $key);
    }

    /**
     * 搜索列表
     * @param int $type
     * @return array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getSearchList($type = 1, $keyword = '')
    {
        return $this->search(['is_show' => 1, 'auth_type' => 1, 'is_del' => 0, 'is_show_path' => 0, 'keywords' => $keyword])->where('type', $type)
            ->field('id,pid,menu_name,menu_path,unique_auth,sort,path')->order('sort DESC')->select();
    }
}
