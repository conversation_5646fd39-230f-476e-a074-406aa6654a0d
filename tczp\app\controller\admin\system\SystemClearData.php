<?php
namespace app\controller\admin\system;

use app\controller\admin\AuthController;
use app\services\other\HotDataServices;
use app\services\product\product\StoreDescriptionServices;
use app\services\product\product\StoreProductCouponServices;
use app\services\product\product\StoreProductRelationServices;
use app\services\product\product\StoreProductReplyServices;
use app\services\product\product\StoreProductServices;
use app\services\product\sku\StoreProductAttrResultServices;
use app\services\product\sku\StoreProductAttrServices;
use app\services\product\sku\StoreProductAttrValueServices;
use app\services\system\attachment\SystemAttachmentServices;
use app\services\system\SystemClearServices;
use crmeb\exceptions\AdminException;
use crmeb\services\CacheService;
use think\annotation\Inject;
use think\exception\ValidateException;
use function app\controller\admin\v1\system\verify_domain;


/**
 * 清除默认数据理控制器
 * Class SystemClearData
 * @package app\controller\admin\v1\system
 */
class SystemClearData extends AuthController
{

    /**
     * @var SystemClearServices
     */
    #[Inject]
    protected SystemClearServices $services;

    /**
     * 构造方法
     * SystemClearData constructor.
     * @param SystemClearServices $services
     */
    public function __construct(SystemClearServices $services)
    {
        parent::__construct();
        $this->services = $services;
        //生产模式下不允许清除数据
        if (!env('APP_DEBUG', false)) {
            throw new ValidateException('生产模式下，禁止操作');
        }
    }

    /**
     * 清除方法入口
     * @param $type
     * @return mixed
     */
    public function index($type)
    {
        switch ($type) {
            case 'temp':
                return $this->userTemp();
                break;
            case 'recycle':
                return $this->recycleProduct();
                break;
            case 'store':
                return $this->storeData();
                break;
            case 'order':
                return $this->orderData();
                break;
            case 'kefu':
                return $this->kefuData();
                break;
            case 'wechat':
                return $this->wechatData();
                break;
            case 'attachment':
                return $this->attachmentData();
                break;
            case 'article':
                return $this->articledata();
                break;
            case 'system':
                return $this->systemdata();
                break;
            case 'user':
                return $this->userRelevantData();
                break;
            default:
                return $this->fail('参数有误');
        }
    }

    /**
     * 清除用户生成的临时文件
     * @return mixed
     */
    public function userTemp()
    {
        /** @var SystemAttachmentServices $services */
        $services = app()->make(SystemAttachmentServices::class);
        $ids = implode(',', $services->getColumn(['module_type' => 2], 'att_id'));
        $services->del($ids);
        $services->delete(2, 'module_type');
        return $this->success('清除数据成功!');
    }

    /**
     * 清除回收站商品
     * @return mixed
     */
    public function recycleProduct(array $ids = [])
    {
        /** @var StoreProductServices $product */
        $product = app()->make(StoreProductServices::class);
        if (!$ids) {
            $ids = $product->getColumn(['is_del' => 1], 'id');
        } else {
            if ($product->search(['id' => $ids, 'is_del' => 0])->find()) {
                throw new AdminException('只能删除回收站商品');
            }
        }
        $this->services->recycleProduct($ids);
        return $this->success('清除数据成功!');
    }

    /**
     * 清除用户数据
     * @return mixed
     */
    public function userRelevantData()
    {
        $this->services->clearData([
            'wechat_user', 'wechat_message', 'user_search', 'user_visit', 'user_sign', 'user_recharge', 'user_notice_see', 'user_notice',
            'user_level', 'user_label_relation', 'user_label', 'user_invoice', 'user_group', 'user_friends', 'user_extract', 'user_enter',
            'user_brokerage_frozen', 'user_bill', 'user_address', 'user', 'system_store_staff', 'store_visit', 'store_service_record',
            'store_service_speechcraft', 'store_service_record', 'store_service_log', 'store_service_feedback', 'store_service',
            'store_product_reply', 'user_relation', 'store_product_log', 'store_pink', 'store_order_status', 'store_order_invoice',
            'store_order_economize', 'store_order_cart_info', 'store_order', 'store_coupon_user', 'store_coupon_issue_user', 'store_cart',
            'store_bargain_user_help', 'store_bargain_user', 'sms_record', 'qrcode', 'other_order_status', 'other_order', 'member_card',
            'member_card_batch', 'delivery_service', 'queue_auxiliary', 'queue_list', 'luck_lottery_record', 'agent_level_task_record',
            'user_brokerage', 'user_money', 'system_message'
        ], true);
        $this->services->delDirAndFile('./public/uploads/store/comment');
        return $this->success('清除数据成功!');
    }

    /**
     * 清除商城数据
     * @return mixed
     */
    public function storeData()
    {
        $this->services->clearData([
            'store_product_category', 'live_anchor', 'live_goods', 'live_room', 'live_room_goods', 'store_bargain', 'store_bargain_user',
            'store_bargain_user_help', 'store_cart', 'store_combination', 'store_coupon_issue', 'store_coupon_issue_user',
            'store_coupon_product', 'store_coupon_user', 'store_product', 'store_product_attr', 'store_product_attr_result',
            'store_product_attr_value', 'store_product_cate', 'store_product_coupon', 'store_product_description', 'store_product_log',
            'user_relation', 'store_product_reply', 'store_product_rule', 'store_seckill', 'store_visit', 'store_integral'
        ], true);
        return $this->success('清除数据成功!');
    }

    /**
     * 清除订单数据
     * @return mixed
     */
    public function orderData()
    {
        $this->services->clearData([
            'queue_list', 'queue_auxiliary', 'other_order_status', 'store_order', 'store_order_cart_info', 'store_order_economize', 'store_order_invoice', 'store_order_refund',
            'store_order_status', 'store_pink', 'other_order', 'store_integral_order', 'store_integral_order_status'
        ], true);
        return $this->success('清除数据成功!');
    }

    /**
     * 清除客服数据
     * @return mixed
     */
    public function kefuData()
    {
        $this->services->clearData([
            'queue_auxiliary', 'store_service', 'store_service_feedback', 'store_service_log', 'store_service_record', 'store_service_speechcraft'
        ], true);
        $this->services->delDirAndFile('./public/uploads/store/service');
        return $this->success('清除数据成功!');
    }

    /**
     * 清除微信管理数据
     * @return mixed
     */
    public function wechatData()
    {
        $this->services->clearData([
            'wechat_key', 'wechat_media', 'wechat_message', 'wechat_news_category', 'wechat_reply'
        ], true);
        $this->services->delDirAndFile('./public/uploads/wechat');
        return $this->success('清除数据成功!');
    }

    /**
     * 清除所有附件
     * @return mixed
     */
    public function attachmentData()
    {
        $this->services->clearData([
            'system_attachment', 'system_attachment_category'
        ], true);
        $this->services->delDirAndFile('./public/uploads/');
        return $this->success('清除上传文件成功!');
    }

    /**
     * 清楚内容数据
     * @return mixed
     */
    public function articledata()
    {
        $this->services->clearData([
            'article_category', 'article', 'article_content'
        ], true);
        return $this->success('清除数据成功!');
    }

    /**
     * 清楚系统记录
     * @return mixed
     */
    public function systemdata()
    {
        $this->services->clearData([
            'system_notice_admin', 'system_log'
        ], true);
        return $this->success('清除数据成功!');
    }

    /**
     * 替换域名方法
     * @return mixed
     */
    public function replaceSiteUrl()
    {
        [$url] = $this->request->postMore([
            ['url', '']
        ], true);
        if (!$url)
            return $this->fail('请输入需要更换的域名');
        if (!verify_domain($url))
            return $this->fail('域名不合法');
        $this->services->replaceSiteUrl($url);
        return $this->success('替换成功！');
    }

    /**
     * 预热营销商品库存
     * @param HotDataServices $hotDataServices
     * @return mixed
     */
    public function hotProductStock(HotDataServices $hotDataServices)
    {
        $key = 'hot_product_stock';
        $re = CacheService::get($key);
        if (!$re) {
            $f = [1, 2, 3, 4, 5];
            try {
                foreach ($f as $type) {
                    $hotDataServices->hot($type);
                }
            } catch (\Throwable $e) {

            }
            CacheService::set($key, time(), 86400);
        }
        return $this->success('预热成功！');
    }
}
