<?php

/**
 * TCZP服务端统一配置文件
 * 版本: v3.0
 * 描述: 三端统一配置管理
 */

return [
    // ==================== 基础配置 ====================
    'app' => [
        'name' => '同城找师傅',
        'version' => '3.0.0',
        'description' => '专业的同城服务平台',
        'keywords' => '同城,师傅,服务,维修,安装',
        'author' => 'TCZP Team',
        'copyright' => 'Copyright © 2024 TCZP. All rights reserved.',
    ],

    // ==================== API配置 ====================
    'api' => [
        'version' => 'v3',
        'prefix' => 'api/v3',
        'timeout' => 30,
        'rate_limit' => [
            'enable' => true,
            'requests' => 100,
            'window' => 60, // 秒
        ],
        'response' => [
            'format' => 'json',
            'charset' => 'utf-8',
            'headers' => [
                'Access-Control-Allow-Origin' => '*',
                'Access-Control-Allow-Methods' => 'GET, POST, PUT, DELETE, OPTIONS',
                'Access-Control-Allow-Headers' => 'Authorization, Content-Type, X-Requested-With',
            ],
        ],
    ],

    // ==================== 认证配置 ====================
    'auth' => [
        'token' => [
            'expire' => 7200, // Token过期时间（秒）
            'refresh_expire' => 86400, // 刷新Token过期时间（秒）
            'algorithm' => 'HS256',
            'secret' => env('JWT_SECRET', 'tczp_jwt_secret_key_2024'),
        ],
        'password' => [
            'min_length' => 6,
            'max_length' => 20,
            'require_special' => false,
            'hash_algo' => PASSWORD_DEFAULT,
        ],
        'login' => [
            'max_attempts' => 5,
            'lockout_time' => 900, // 15分钟
            'remember_expire' => 2592000, // 30天
        ],
    ],

    // ==================== 三端配置 ====================
    'endpoints' => [
        'worker' => [
            'name' => '师傅端',
            'prefix' => 'worker',
            'auth_required' => true,
            'features' => [
                'profile_management' => true,
                'skill_certification' => true,
                'task_taking' => true,
                'income_management' => true,
                'review_system' => true,
            ],
        ],
        'user' => [
            'name' => '用户端',
            'prefix' => 'user',
            'auth_required' => true,
            'features' => [
                'task_publishing' => true,
                'enterprise_auth' => true,
                'payment_system' => true,
                'worker_management' => true,
                'batch_operations' => true,
            ],
        ],
        'admin' => [
            'name' => '管理后台',
            'prefix' => 'admin',
            'auth_required' => true,
            'features' => [
                'user_management' => true,
                'audit_system' => true,
                'statistics_analysis' => true,
                'finance_management' => true,
                'system_management' => true,
            ],
        ],
    ],

    // ==================== 业务配置 ====================
    'business' => [
        'task' => [
            'auto_assign' => false,
            'timeout_hours' => 24,
            'max_workers' => 10,
            'min_price' => 10,
            'max_price' => 10000,
            'categories' => [
                'home_repair' => '家庭维修',
                'appliance_repair' => '家电维修',
                'installation' => '安装服务',
                'cleaning' => '清洁服务',
                'moving' => '搬家服务',
            ],
        ],
        'worker' => [
            'auth_required' => true,
            'skill_levels' => [1 => '初级', 2 => '中级', 3 => '高级', 4 => '专家', 5 => '大师'],
            'work_status' => ['available' => '可接单', 'busy' => '忙碌中', 'offline' => '离线'],
            'rating_system' => [
                'min_rating' => 1,
                'max_rating' => 5,
                'default_rating' => 5,
            ],
        ],
        'enterprise' => [
            'auth_required' => true,
            'types' => [
                'individual' => '个体工商户',
                'company' => '有限公司',
                'partnership' => '合伙企业',
                'other' => '其他',
            ],
            'credit_levels' => ['A', 'B', 'C', 'D'],
        ],
    ],

    // ==================== 支付配置 ====================
    'payment' => [
        'methods' => [
            'wechat' => [
                'name' => '微信支付',
                'enabled' => true,
                'config' => [
                    'app_id' => env('WECHAT_APP_ID', ''),
                    'mch_id' => env('WECHAT_MCH_ID', ''),
                    'key' => env('WECHAT_KEY', ''),
                ],
            ],
            'alipay' => [
                'name' => '支付宝',
                'enabled' => true,
                'config' => [
                    'app_id' => env('ALIPAY_APP_ID', ''),
                    'private_key' => env('ALIPAY_PRIVATE_KEY', ''),
                    'public_key' => env('ALIPAY_PUBLIC_KEY', ''),
                ],
            ],
        ],
        'platform_fee_rate' => 5, // 平台手续费率（%）
        'min_withdrawal' => 100, // 最低提现金额
        'withdrawal_fee_rate' => 0.5, // 提现手续费率（%）
    ],

    // ==================== 文件上传配置 ====================
    'upload' => [
        'disk' => 'public',
        'max_size' => 10 * 1024 * 1024, // 10MB
        'allowed_types' => [
            'image' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
            'video' => ['mp4', 'avi', 'mov', 'wmv'],
            'document' => ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],
        ],
        'paths' => [
            'avatar' => 'uploads/avatar',
            'id_card' => 'uploads/id_card',
            'license' => 'uploads/license',
            'skill_cert' => 'uploads/skill_cert',
            'task_image' => 'uploads/task_image',
            'work_image' => 'uploads/work_image',
        ],
    ],

    // ==================== 短信配置 ====================
    'sms' => [
        'provider' => env('SMS_PROVIDER', 'aliyun'),
        'providers' => [
            'aliyun' => [
                'access_key' => env('ALIYUN_SMS_ACCESS_KEY', ''),
                'access_secret' => env('ALIYUN_SMS_ACCESS_SECRET', ''),
                'sign_name' => env('ALIYUN_SMS_SIGN_NAME', '同城找师傅'),
                'templates' => [
                    'register' => env('ALIYUN_SMS_TEMPLATE_REGISTER', ''),
                    'login' => env('ALIYUN_SMS_TEMPLATE_LOGIN', ''),
                    'reset_password' => env('ALIYUN_SMS_TEMPLATE_RESET', ''),
                    'bind_phone' => env('ALIYUN_SMS_TEMPLATE_BIND', ''),
                ],
            ],
        ],
        'code_length' => 6,
        'code_expire' => 300, // 5分钟
        'send_limit' => [
            'per_minute' => 1,
            'per_hour' => 5,
            'per_day' => 20,
        ],
    ],

    // ==================== 地图配置 ====================
    'map' => [
        'provider' => env('MAP_PROVIDER', 'amap'),
        'providers' => [
            'amap' => [
                'key' => env('AMAP_KEY', ''),
                'secret' => env('AMAP_SECRET', ''),
            ],
            'baidu' => [
                'ak' => env('BAIDU_MAP_AK', ''),
            ],
        ],
    ],

    // ==================== 缓存配置 ====================
    'cache' => [
        'prefix' => 'tczp:',
        'expire' => [
            'default' => 3600,
            'user_info' => 1800,
            'config' => 86400,
            'statistics' => 300,
        ],
        'tags' => [
            'user' => 'user_cache',
            'worker' => 'worker_cache',
            'task' => 'task_cache',
            'config' => 'config_cache',
        ],
    ],

    // ==================== 队列配置 ====================
    'queue' => [
        'default' => 'redis',
        'jobs' => [
            'sms' => [
                'queue' => 'sms',
                'delay' => 0,
                'max_attempts' => 3,
            ],
            'notification' => [
                'queue' => 'notification',
                'delay' => 0,
                'max_attempts' => 3,
            ],
            'statistics' => [
                'queue' => 'statistics',
                'delay' => 0,
                'max_attempts' => 1,
            ],
        ],
    ],

    // ==================== 日志配置 ====================
    'log' => [
        'channels' => [
            'api' => [
                'type' => 'File',
                'path' => runtime_path() . 'log/api/',
                'level' => ['info', 'notice', 'warning', 'error'],
            ],
            'auth' => [
                'type' => 'File',
                'path' => runtime_path() . 'log/auth/',
                'level' => ['info', 'warning', 'error'],
            ],
            'business' => [
                'type' => 'File',
                'path' => runtime_path() . 'log/business/',
                'level' => ['info', 'notice', 'warning', 'error'],
            ],
        ],
    ],

    // ==================== 安全配置 ====================
    'security' => [
        'encryption' => [
            'key' => env('APP_KEY', ''),
            'cipher' => 'AES-256-CBC',
        ],
        'csrf' => [
            'enable' => false, // API接口通常不需要CSRF
        ],
        'xss' => [
            'enable' => true,
            'filter_keys' => ['content', 'description', 'remark'],
        ],
        'sql_injection' => [
            'enable' => true,
            'keywords' => ['select', 'insert', 'update', 'delete', 'union', 'script'],
        ],
    ],

    // ==================== 通知配置 ====================
    'notification' => [
        'channels' => [
            'wechat' => [
                'enabled' => true,
                'app_id' => env('WECHAT_MINI_APP_ID', ''),
                'secret' => env('WECHAT_MINI_SECRET', ''),
            ],
            'sms' => [
                'enabled' => true,
            ],
            'push' => [
                'enabled' => true,
                'provider' => 'jpush',
                'config' => [
                    'app_key' => env('JPUSH_APP_KEY', ''),
                    'master_secret' => env('JPUSH_MASTER_SECRET', ''),
                ],
            ],
        ],
        'templates' => [
            'task_assigned' => '任务已分配给您',
            'task_completed' => '任务已完成',
            'payment_received' => '收到付款',
            'review_received' => '收到新评价',
        ],
    ],

    // ==================== 系统监控配置 ====================
    'monitor' => [
        'enable' => true,
        'metrics' => [
            'api_response_time' => true,
            'database_query_time' => true,
            'cache_hit_rate' => true,
            'error_rate' => true,
        ],
        'alerts' => [
            'email' => env('MONITOR_EMAIL', ''),
            'webhook' => env('MONITOR_WEBHOOK', ''),
        ],
    ],
];
