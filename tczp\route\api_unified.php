<?php

use app\http\middleware\AllowOriginMiddleware;
use app\http\middleware\StationOpenMiddleware;
use think\facade\Route;

/**
 * 统一API路由配置 - TCZP服务端优化版
 * 版本: v3.0
 * 特性: 三端统一、RESTful规范、完整接口覆盖
 */

// ==================== 公共接口（无需登录） ====================
Route::group('api/v3/public', function () {
    
    // 系统配置
    Route::get('config', 'common.Common/getConfig')->name('api.public.config');
    Route::get('cities', 'common.Common/getCityList')->name('api.public.cities');
    Route::get('task-categories', 'common.Common/getTaskCategories')->name('api.public.task_categories');
    Route::get('skill-categories', 'common.Common/getSkillCategories')->name('api.public.skill_categories');
    
    // 短信验证
    Route::post('sms/send', 'common.Common/sendSmsCode')->name('api.public.sms_send');
    Route::post('sms/verify', 'common.Common/verifySmsCode')->name('api.public.sms_verify');
    
    // 文件上传
    Route::post('upload/image', 'common.Common/uploadImage')->name('api.public.upload_image');
    Route::post('upload/video', 'common.Common/uploadVideo')->name('api.public.upload_video');
    Route::post('upload/file', 'common.Common/uploadFile')->name('api.public.upload_file');
    
    // 地理位置
    Route::post('location/info', 'common.Common/getLocationInfo')->name('api.public.location_info');
    Route::post('location/geocode', 'common.Common/geocode')->name('api.public.geocode');
    Route::post('location/reverse-geocode', 'common.Common/reverseGeocode')->name('api.public.reverse_geocode');
    
    // 工具接口
    Route::post('qrcode/generate', 'common.Common/generateQrcode')->name('api.public.qrcode');
    Route::get('version/info', 'common.Common/getVersionInfo')->name('api.public.version_info');
    Route::post('version/check', 'common.Common/checkVersion')->name('api.public.version_check');
    
    // 帮助支持
    Route::get('help/docs', 'common.Common/getHelpDocs')->name('api.public.help_docs');
    Route::get('help/faq', 'common.Common/getFaq')->name('api.public.faq');
    Route::post('feedback', 'common.Common/submitFeedback')->name('api.public.feedback');
    
    // 协议政策
    Route::get('agreement', 'common.Common/getAgreement')->name('api.public.agreement');
    Route::get('privacy-policy', 'common.Common/getPrivacyPolicy')->name('api.public.privacy_policy');
    
    // 任务大厅（公开浏览）
    Route::get('tasks', 'user.Task/getTaskList')->name('api.public.tasks');
    Route::get('tasks/:id', 'user.Task/getTaskDetail')->name('api.public.task_detail');
    
    // 师傅展示（公开）
    Route::get('workers', 'worker.Worker/getWorkerList')->name('api.public.workers');
    Route::get('workers/:id', 'worker.Worker/getWorkerDetail')->name('api.public.worker_detail');
    
    // 企业展示（公开）
    Route::get('enterprises', 'user.Enterprise/getEnterpriseList')->name('api.public.enterprises');
    Route::get('enterprises/:id', 'user.Enterprise/getEnterpriseDetail')->name('api.public.enterprise_detail');

})->middleware(StationOpenMiddleware::class);

// ==================== 师傅端接口 ====================
Route::group('api/v3/worker', function () {
    
    // 认证相关
    Route::group('auth', function () {
        Route::post('register', 'worker.Auth/register')->name('api.worker.auth.register');
        Route::post('login', 'worker.Auth/login')->name('api.worker.auth.login');
        Route::post('logout', 'worker.Auth/logout')->name('api.worker.auth.logout');
        Route::post('refresh-token', 'worker.Auth/refreshToken')->name('api.worker.auth.refresh_token');
        Route::post('reset-password', 'worker.Auth/resetPassword')->name('api.worker.auth.reset_password');
    });
    
    // 个人中心
    Route::group('profile', function () {
        Route::get('', 'worker.Profile/getProfile')->name('api.worker.profile.get');
        Route::put('', 'worker.Profile/updateProfile')->name('api.worker.profile.update');
        Route::post('avatar', 'worker.Profile/uploadAvatar')->name('api.worker.profile.avatar');
        Route::get('stats', 'worker.Profile/getWorkStats')->name('api.worker.profile.stats');
        Route::get('income-stats', 'worker.Profile/getIncomeStats')->name('api.worker.profile.income_stats');
        Route::get('review-stats', 'worker.Profile/getReviewStats')->name('api.worker.profile.review_stats');
        Route::put('work-status', 'worker.Profile/updateWorkStatus')->name('api.worker.profile.work_status');
        Route::get('auth-status', 'worker.Profile/getAuthStatus')->name('api.worker.profile.auth_status');
        Route::post('submit-auth', 'worker.Profile/submitAuth')->name('api.worker.profile.submit_auth');
        Route::get('settings', 'worker.Profile/getSettings')->name('api.worker.profile.settings');
        Route::put('settings', 'worker.Profile/updateSettings')->name('api.worker.profile.update_settings');
    });
    
    // 技能管理
    Route::group('skills', function () {
        Route::get('', 'worker.Profile/getSkills')->name('api.worker.skills.list');
        Route::post('', 'worker.Profile/addSkill')->name('api.worker.skills.add');
        Route::put(':id', 'worker.Profile/updateSkill')->name('api.worker.skills.update');
        Route::delete(':id', 'worker.Profile/deleteSkill')->name('api.worker.skills.delete');
    });
    
    // 任务管理
    Route::group('tasks', function () {
        Route::get('available', 'worker.Task/getAvailableTasks')->name('api.worker.tasks.available');
        Route::get('my', 'worker.Task/getMyTasks')->name('api.worker.tasks.my');
        Route::get(':id', 'worker.Task/getTaskDetail')->name('api.worker.tasks.detail');
        Route::post(':id/apply', 'worker.Task/applyTask')->name('api.worker.tasks.apply');
        Route::put(':id/accept', 'worker.Task/acceptTask')->name('api.worker.tasks.accept');
        Route::put(':id/start', 'worker.Task/startTask')->name('api.worker.tasks.start');
        Route::put(':id/complete', 'worker.Task/completeTask')->name('api.worker.tasks.complete');
        Route::put(':id/cancel', 'worker.Task/cancelTask')->name('api.worker.tasks.cancel');
    });
    
    // 收入管理
    Route::group('income', function () {
        Route::get('', 'worker.Income/getIncomeList')->name('api.worker.income.list');
        Route::get('stats', 'worker.Income/getIncomeStats')->name('api.worker.income.stats');
        Route::get('withdrawals', 'worker.Income/getWithdrawals')->name('api.worker.income.withdrawals');
        Route::post('withdraw', 'worker.Income/withdraw')->name('api.worker.income.withdraw');
    });
    
    // 评价管理
    Route::group('reviews', function () {
        Route::get('', 'worker.Review/getReviews')->name('api.worker.reviews.list');
        Route::get(':id', 'worker.Review/getReviewDetail')->name('api.worker.reviews.detail');
        Route::post(':id/reply', 'worker.Review/replyReview')->name('api.worker.reviews.reply');
    });
    
    // 消息通知
    Route::group('messages', function () {
        Route::get('', 'worker.Message/getMessages')->name('api.worker.messages.list');
        Route::get(':id', 'worker.Message/getMessageDetail')->name('api.worker.messages.detail');
        Route::put(':id/read', 'worker.Message/markAsRead')->name('api.worker.messages.read');
        Route::put('read-all', 'worker.Message/markAllAsRead')->name('api.worker.messages.read_all');
        Route::delete(':id', 'worker.Message/deleteMessage')->name('api.worker.messages.delete');
    });

})->middleware(StationOpenMiddleware::class)
    ->middleware(\app\http\middleware\worker\WorkerAuthMiddleware::class, true);

// ==================== 用户端（企业端）接口 ====================
Route::group('api/v3/user', function () {
    
    // 认证相关
    Route::group('auth', function () {
        Route::post('register', 'user.Auth/registerUser')->name('api.user.auth.register');
        Route::post('register/enterprise', 'user.Auth/registerEnterprise')->name('api.user.auth.register_enterprise');
        Route::post('login', 'user.Auth/login')->name('api.user.auth.login');
        Route::post('logout', 'user.Auth/logout')->name('api.user.auth.logout');
        Route::post('refresh-token', 'user.Auth/refreshToken')->name('api.user.auth.refresh_token');
        Route::post('reset-password', 'user.Auth/resetPassword')->name('api.user.auth.reset_password');
        Route::post('verify-phone', 'user.Auth/verifyPhone')->name('api.user.auth.verify_phone');
        
        // 认证申请
        Route::post('personal/submit', 'user.Auth/submitPersonalAuth')->name('api.user.auth.personal');
        Route::get('personal/status', 'user.Auth/getPersonalStatus')->name('api.user.auth.personal_status');
        Route::post('enterprise/submit', 'user.Auth/submitEnterpriseAuth')->name('api.user.auth.enterprise');
        Route::get('enterprise/status', 'user.Auth/getEnterpriseStatus')->name('api.user.auth.enterprise_status');
        Route::put('enterprise/update', 'user.Auth/updateEnterpriseAuth')->name('api.user.auth.enterprise_update');
        
        // 文件上传
        Route::post('upload/personal', 'user.Auth/uploadPersonalFiles')->name('api.user.auth.upload_personal');
        Route::post('upload/enterprise', 'user.Auth/uploadEnterpriseFiles')->name('api.user.auth.upload_enterprise');
        Route::post('upload/license', 'user.Auth/uploadLicense')->name('api.user.auth.upload_license');
    });
    
    // 个人中心
    Route::group('profile', function () {
        Route::get('', 'user.Profile/getProfile')->name('api.user.profile.get');
        Route::put('', 'user.Profile/updateProfile')->name('api.user.profile.update');
        Route::post('avatar', 'user.Profile/uploadAvatar')->name('api.user.profile.avatar');
        Route::get('stats', 'user.Profile/getStats')->name('api.user.profile.stats');
        Route::get('stats/chart', 'user.Profile/getStatsChart')->name('api.user.profile.stats_chart');
        Route::get('settings', 'user.Profile/getSettings')->name('api.user.profile.settings');
        Route::put('settings', 'user.Profile/updateSettings')->name('api.user.profile.update_settings');
        Route::get('notifications', 'user.Profile/getNotificationSettings')->name('api.user.profile.notifications');
        Route::put('notifications', 'user.Profile/updateNotificationSettings')->name('api.user.profile.update_notifications');
        Route::post('change-password', 'user.Profile/changePassword')->name('api.user.profile.change_password');
        Route::post('bind-phone', 'user.Profile/bindPhone')->name('api.user.profile.bind_phone');
        Route::post('bind-email', 'user.Profile/bindEmail')->name('api.user.profile.bind_email');
    });
    
    // 收藏管理
    Route::group('favorites', function () {
        Route::get('', 'user.Profile/getFavorites')->name('api.user.favorites.list');
        Route::post('', 'user.Profile/addFavorite')->name('api.user.favorites.add');
        Route::delete(':id', 'user.Profile/removeFavorite')->name('api.user.favorites.remove');
    });
    
    // 地址管理
    Route::group('addresses', function () {
        Route::get('', 'user.Profile/getAddresses')->name('api.user.addresses.list');
        Route::post('', 'user.Profile/addAddress')->name('api.user.addresses.add');
        Route::put(':id', 'user.Profile/updateAddress')->name('api.user.addresses.update');
        Route::delete(':id', 'user.Profile/deleteAddress')->name('api.user.addresses.delete');
        Route::put(':id/default', 'user.Profile/setDefaultAddress')->name('api.user.addresses.default');
    });
    
    // 任务管理
    Route::group('tasks', function () {
        Route::post('', 'user.Task/publishTask')->name('api.user.tasks.publish');
        Route::get('my', 'user.Task/getMyTasks')->name('api.user.tasks.my');
        Route::get(':id', 'user.Task/getTaskDetail')->name('api.user.tasks.detail');
        Route::put(':id', 'user.Task/updateTask')->name('api.user.tasks.update');
        Route::delete(':id', 'user.Task/cancelTask')->name('api.user.tasks.cancel');
        Route::get(':id/applications', 'user.Task/getTaskApplications')->name('api.user.tasks.applications');
        Route::put('applications/:id/approve', 'user.Task/approveApplication')->name('api.user.tasks.approve');
        Route::put('applications/:id/reject', 'user.Task/rejectApplication')->name('api.user.tasks.reject');
        Route::put(':id/confirm', 'user.Task/confirmTask')->name('api.user.tasks.confirm');
        Route::put(':id/complete', 'user.Task/completeTask')->name('api.user.tasks.complete');
        
        // 任务模板
        Route::get('templates', 'user.Task/getTaskTemplates')->name('api.user.tasks.templates');
        Route::post('templates', 'user.Task/saveTaskTemplate')->name('api.user.tasks.save_template');
        Route::post('templates/:id/use', 'user.Task/useTaskTemplate')->name('api.user.tasks.use_template');
    });

})->middleware(StationOpenMiddleware::class)
    ->middleware(\app\http\middleware\user\UserAuthMiddleware::class, true);

// ==================== 管理后台接口 ====================
Route::group('api/v3/admin', function () {
    
    // 认证相关
    Route::group('auth', function () {
        Route::post('login', 'admin.Auth/login')->name('api.admin.auth.login');
        Route::post('logout', 'admin.Auth/logout')->name('api.admin.auth.logout');
        Route::post('refresh-token', 'admin.Auth/refreshToken')->name('api.admin.auth.refresh_token');
        Route::post('change-password', 'admin.Auth/changePassword')->name('api.admin.auth.change_password');
    });
    
    // 首页控制台
    Route::group('dashboard', function () {
        Route::get('overview', 'admin.Dashboard/getOverview')->name('api.admin.dashboard.overview');
        Route::get('realtime', 'admin.Dashboard/getRealTimeData')->name('api.admin.dashboard.realtime');
        Route::get('user-growth', 'admin.Dashboard/getUserGrowthTrend')->name('api.admin.dashboard.user_growth');
        Route::get('task-analysis', 'admin.Dashboard/getTaskAnalysis')->name('api.admin.dashboard.task_analysis');
        Route::get('finance-analysis', 'admin.Dashboard/getFinanceAnalysis')->name('api.admin.dashboard.finance_analysis');
        Route::get('region-distribution', 'admin.Dashboard/getRegionDistribution')->name('api.admin.dashboard.region');
        Route::get('operation-analysis', 'admin.Dashboard/getOperationAnalysis')->name('api.admin.dashboard.operation');
        Route::get('quick-actions', 'admin.Dashboard/getQuickActions')->name('api.admin.dashboard.quick_actions');
        Route::get('system-status', 'admin.Dashboard/getSystemStatus')->name('api.admin.dashboard.system_status');
    });
    
    // 审核管理
    Route::group('audit', function () {
        Route::get('pending', 'admin.Audit/getPendingList')->name('api.admin.audit.pending');
        Route::post('handle', 'admin.Audit/handleAudit')->name('api.admin.audit.handle');
        Route::post('batch', 'admin.Audit/batchAudit')->name('api.admin.audit.batch');
        Route::get('statistics', 'admin.Audit/getAuditStatistics')->name('api.admin.audit.statistics');
        Route::get('enterprise', 'admin.Audit/getEnterpriseAuditList')->name('api.admin.audit.enterprise');
        Route::post('enterprise', 'admin.Audit/auditEnterprise')->name('api.admin.audit.enterprise_handle');
        Route::get('worker', 'admin.Audit/getWorkerAuditList')->name('api.admin.audit.worker');
        Route::post('worker', 'admin.Audit/auditWorker')->name('api.admin.audit.worker_handle');
        Route::post('skill', 'admin.Audit/auditSkill')->name('api.admin.audit.skill');
        Route::post('task', 'admin.Audit/auditTask')->name('api.admin.audit.task');
    });
    
    // 任务管理
    Route::group('tasks', function () {
        Route::get('', 'admin.TaskManagement/getTaskList')->name('api.admin.tasks.list');
        Route::get(':id', 'admin.TaskManagement/getTaskDetail')->name('api.admin.tasks.detail');
        Route::post(':id/audit', 'admin.TaskManagement/auditTask')->name('api.admin.tasks.audit');
        Route::post(':id/force-cancel', 'admin.TaskManagement/forceCancel')->name('api.admin.tasks.force_cancel');
        Route::post(':id/assign-worker', 'admin.TaskManagement/assignWorker')->name('api.admin.tasks.assign_worker');
        Route::get('statistics', 'admin.TaskManagement/getTaskStatistics')->name('api.admin.tasks.statistics');
        Route::get('categories', 'admin.TaskManagement/getTaskCategories')->name('api.admin.tasks.categories');
    });

})->middleware(StationOpenMiddleware::class)
    ->middleware(\app\http\middleware\admin\AdminAuthMiddleware::class, true)
    ->middleware(\app\http\middleware\admin\AdminOperationLogMiddleware::class);

// ==================== 跨域处理 ====================
Route::options('[:path]', function () {
    return response('', 200);
})->pattern(['path' => '.*'])->middleware(AllowOriginMiddleware::class);
