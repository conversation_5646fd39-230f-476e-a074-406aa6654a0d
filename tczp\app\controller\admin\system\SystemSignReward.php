<?php
namespace app\controller\admin\system;

use app\controller\admin\AuthController;
use app\services\system\SystemSignRewardServices;
use think\annotation\Inject;

/**
 * 签到奖励
 * Class SystemSignReward
 * @package app\controller\admin\v1\system
 */
class SystemSignReward extends AuthController
{

    /**
     * @var SystemSignRewardServices
     */
    #[Inject]
    protected SystemSignRewardServices $services;

    /**
     * 签到奖励列表
     * @return \think\Response
     */
    public function index()
    {
        [$type] = $this->request->getMore([
            ['type', 0]
        ], true);
        $data = $this->services->getList($type);
        return app('json')->success($data);
    }

    /**
     * 新增签到奖励
     * @return \think\Response
     * @throws \FormBuilder\Exception\FormBuilderException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function addRewards()
    {
        [$type] = $this->request->getMore([
            ['type', 0]
        ], true);
        $data = $this->services->rewardsForm(0, $type);
        return app('json')->success($data);
    }

    /**
     * 修改签到奖励
     * @param $id
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function editRewards($id)
    {
        $data = $this->services->rewardsForm($id);
        return app('json')->success($data);
    }

    /**
     * 保存签到奖励
     * @param $id
     * @return \think\Response
     * @throws \think\db\exception\DbException
     */
    public function saveRewards($id)
    {
        $data = $this->request->postMore([
            ['type', 0],
            ['days', 0],
            ['point', 0],
            ['exp', 0]
        ]);
        $this->services->saveRewards($id, $data);
        return app('json')->success('保存成功');
    }

    /**
     * 删除签到奖励
     * @param $id
     * @return \think\Response
     */
    public function delRewards($id)
    {
        $this->services->delete($id);
        return app('json')->success('删除成功');
    }
}

