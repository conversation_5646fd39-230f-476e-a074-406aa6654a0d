<?php
declare(strict_types=1);

namespace app\services\user;

use app\services\BaseServices;
use think\facade\Db;
use think\facade\Cache;

/**
 * 企业认证服务类
 * Class EnterpriseAuthServices
 * @package app\services\user
 */
class EnterpriseAuthServices extends BaseServices
{
    /**
     * 表名
     * @var string
     */
    protected $tableName = 'enterprise_profiles';
    
    /**
     * 缓存前缀
     * @var string
     */
    protected $cachePrefix = 'enterprise_';
    
    /**
     * 检查用户是否为企业用户
     * @param int $uid
     * @return bool
     */
    public function isEnterprise(int $uid): bool
    {
        // 检查用户角色
        $hasEnterpriseRole = Db::name('user_role_relations')
            ->alias('urr')
            ->join('user_roles ur', 'urr.role_id = ur.id')
            ->where('urr.user_id', $uid)
            ->where('ur.role_code', 'enterprise')
            ->where('urr.status', 1)
            ->count() > 0;
            
        return $hasEnterpriseRole;
    }
    
    /**
     * 获取企业信息
     * @param int $uid
     * @return array|null
     */
    public function getEnterpriseInfo(int $uid): ?array
    {
        $cacheKey = "enterprise_info_{$uid}";
        
        return Cache::remember($cacheKey, function () use ($uid) {
            return Db::name('enterprise_profiles')
                ->where('user_id', $uid)
                ->find();
        }, 300);
    }
    
    /**
     * 获取企业权限
     * @param int $uid
     * @return array
     */
    public function getEnterprisePermissions(int $uid): array
    {
        $cacheKey = "enterprise_permissions_{$uid}";
        
        return Cache::remember($cacheKey, function () use ($uid) {
            // 获取角色权限
            $rolePermissions = Db::name('user_role_relations')
                ->alias('urr')
                ->join('user_roles ur', 'urr.role_id = ur.id')
                ->where('urr.user_id', $uid)
                ->where('urr.status', 1)
                ->column('ur.permissions');
                
            $permissions = [];
            foreach ($rolePermissions as $rolePermission) {
                $perms = json_decode($rolePermission, true) ?: [];
                $permissions = array_merge($permissions, $perms);
            }
            
            return array_unique($permissions);
        }, 300);
    }
    
    /**
     * 创建企业档案
     * @param int $uid
     * @param array $data
     * @return bool
     */
    public function createEnterpriseProfile(int $uid, array $data): bool
    {
        try {
            Db::startTrans();
            
            // 生成企业编号
            $enterpriseNo = $this->generateEnterpriseNo();
            
            // 创建企业档案
            $profileData = [
                'user_id' => $uid,
                'enterprise_no' => $enterpriseNo,
                'enterprise_name' => $data['enterprise_name'] ?? '',
                'enterprise_type' => $data['enterprise_type'] ?? 'company',
                'legal_person' => $data['legal_person'] ?? '',
                'contact_person' => $data['contact_person'] ?? '',
                'contact_phone' => $data['contact_phone'] ?? '',
                'contact_email' => $data['contact_email'] ?? '',
                'business_license' => $data['business_license'] ?? '',
                'license_images' => json_encode($data['license_images'] ?? []),
                'enterprise_address' => $data['enterprise_address'] ?? '',
                'business_scope' => $data['business_scope'] ?? '',
                'registered_capital' => $data['registered_capital'] ?? 0,
                'establishment_date' => $data['establishment_date'] ?? 0,
                'credit_code' => $data['credit_code'] ?? '',
                'auth_status' => 'pending',
                'create_time' => time(),
                'update_time' => time(),
            ];
            
            $profileId = Db::name('enterprise_profiles')->insertGetId($profileData);
            
            // 添加企业角色
            $this->addEnterpriseRole($uid);
            
            Db::commit();
            
            // 清除缓存
            $this->clearEnterpriseCache($uid);
            
            return true;
            
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 更新企业认证状态
     * @param int $uid
     * @param string $status
     * @param string $reason
     * @param int $reviewerId
     * @return bool
     */
    public function updateAuthStatus(int $uid, string $status, string $reason = '', int $reviewerId = 0): bool
    {
        $updateData = [
            'auth_status' => $status,
            'auth_time' => time(),
            'reviewer_id' => $reviewerId,
            'update_time' => time(),
        ];
        
        if ($status === 'rejected' && $reason) {
            $updateData['reject_reason'] = $reason;
        }
        
        $result = Db::name('enterprise_profiles')
            ->where('user_id', $uid)
            ->update($updateData);
            
        if ($result) {
            // 记录认证日志
            $this->logAuthRecord($uid, 'basic', $status, $reason);
            
            // 清除缓存
            $this->clearEnterpriseCache($uid);
        }
        
        return $result > 0;
    }
    
    /**
     * 获取企业统计信息
     * @param int $uid
     * @return array
     */
    public function getEnterpriseStats(int $uid): array
    {
        $enterpriseInfo = $this->getEnterpriseInfo($uid);
        if (!$enterpriseInfo) {
            return [];
        }
        
        // 获取任务统计
        $taskStats = Db::name('tasks')
            ->where('enterprise_id', $enterpriseInfo['id'])
            ->field([
                'COUNT(*) as total_tasks',
                'SUM(CASE WHEN status = "published" THEN 1 ELSE 0 END) as published_count',
                'SUM(CASE WHEN status = "in_progress" THEN 1 ELSE 0 END) as progress_count',
                'SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as completed_count',
                'AVG(hourly_rate) as avg_hourly_rate'
            ])
            ->find();
            
        // 获取支付统计
        $paymentStats = Db::name('payment_orders')
            ->where('user_id', $uid)
            ->where('status', 'paid')
            ->field([
                'SUM(actual_amount) as total_paid',
                'COUNT(*) as payment_count',
                'AVG(actual_amount) as avg_payment'
            ])
            ->find();
            
        // 获取评价统计
        $reviewStats = Db::name('task_reviews')
            ->where('user_id', $uid)
            ->field([
                'AVG(rating) as avg_rating',
                'COUNT(*) as review_count'
            ])
            ->find();
            
        return [
            'enterprise_info' => $enterpriseInfo,
            'task_stats' => $taskStats ?: [],
            'payment_stats' => $paymentStats ?: [],
            'review_stats' => $reviewStats ?: []
        ];
    }
    
    /**
     * 获取企业列表
     * @param array $where
     * @param int $page
     * @param int $limit
     * @return array
     */
    public function getEnterpriseList(array $where = [], int $page = 1, int $limit = 20): array
    {
        $query = Db::name('enterprise_profiles')
            ->alias('ep')
            ->join('user u', 'ep.user_id = u.uid')
            ->where('ep.auth_status', 'approved');
            
        // 企业类型筛选
        if (!empty($where['enterprise_type'])) {
            $query->where('ep.enterprise_type', $where['enterprise_type']);
        }
        
        // 信用等级筛选
        if (!empty($where['credit_rating'])) {
            $query->where('ep.credit_rating', $where['credit_rating']);
        }
        
        // 关键词搜索
        if (!empty($where['keyword'])) {
            $query->where(function ($q) use ($where) {
                $q->where('ep.enterprise_name', 'like', "%{$where['keyword']}%")
                  ->whereOr('ep.business_scope', 'like', "%{$where['keyword']}%");
            });
        }
        
        $total = $query->count();
        $list = $query->field([
                'ep.id', 'ep.enterprise_no', 'ep.enterprise_name', 'ep.enterprise_type',
                'ep.contact_person', 'ep.contact_phone', 'ep.credit_rating', 'ep.credit_score',
                'ep.task_count', 'ep.completed_count', 'ep.total_amount', 'ep.create_time',
                'u.nickname', 'u.avatar'
            ])
            ->order('ep.credit_score desc, ep.create_time desc')
            ->page($page, $limit)
            ->select()
            ->toArray();
            
        // 计算完成率
        foreach ($list as &$item) {
            $item['completion_rate'] = $item['task_count'] > 0 
                ? round(($item['completed_count'] / $item['task_count']) * 100, 2) 
                : 0;
        }
            
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    }
    
    /**
     * 更新企业信用分数
     * @param int $uid
     * @param int $score
     * @param string $reason
     * @return bool
     */
    public function updateCreditScore(int $uid, int $score, string $reason = ''): bool
    {
        $enterpriseInfo = $this->getEnterpriseInfo($uid);
        if (!$enterpriseInfo) {
            return false;
        }
        
        // 计算新的信用等级
        $creditRating = $this->calculateCreditRating($score);
        
        $result = Db::name('enterprise_profiles')
            ->where('user_id', $uid)
            ->update([
                'credit_score' => $score,
                'credit_rating' => $creditRating,
                'update_time' => time()
            ]);
            
        if ($result) {
            // 记录信用变更日志
            $this->logCreditChange($uid, $score, $reason);
            
            // 清除缓存
            $this->clearEnterpriseCache($uid);
        }
        
        return $result > 0;
    }
    
    /**
     * 生成企业编号
     * @return string
     */
    private function generateEnterpriseNo(): string
    {
        $prefix = 'E';
        $timestamp = date('ymd');
        $random = str_pad((string)mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
        
        return $prefix . $timestamp . $random;
    }
    
    /**
     * 添加企业角色
     * @param int $uid
     * @return bool
     */
    private function addEnterpriseRole(int $uid): bool
    {
        // 获取企业角色ID
        $roleId = Db::name('user_roles')
            ->where('role_code', 'enterprise')
            ->value('id');
            
        if (!$roleId) {
            throw new \Exception('企业角色不存在');
        }
        
        // 检查是否已有该角色
        $exists = Db::name('user_role_relations')
            ->where('user_id', $uid)
            ->where('role_id', $roleId)
            ->count() > 0;
            
        if ($exists) {
            return true;
        }
        
        // 添加角色关联
        return Db::name('user_role_relations')->insert([
            'user_id' => $uid,
            'role_id' => $roleId,
            'status' => 1,
            'create_time' => time(),
        ]);
    }
    
    /**
     * 计算信用等级
     * @param int $score
     * @return string
     */
    private function calculateCreditRating(int $score): string
    {
        if ($score >= 90) return 'A';
        if ($score >= 80) return 'B';
        if ($score >= 70) return 'C';
        return 'D';
    }
    
    /**
     * 记录认证日志
     * @param int $uid
     * @param string $authType
     * @param string $status
     * @param string $reason
     */
    private function logAuthRecord(int $uid, string $authType, string $status, string $reason = ''): void
    {
        $enterpriseInfo = $this->getEnterpriseInfo($uid);
        if (!$enterpriseInfo) {
            return;
        }
        
        Db::name('enterprise_auth_records')->insert([
            'enterprise_id' => $enterpriseInfo['id'],
            'auth_type' => $authType,
            'auth_data' => json_encode(['reason' => $reason]),
            'status' => $status,
            'submit_time' => time(),
            'create_time' => time(),
        ]);
    }
    
    /**
     * 记录信用变更日志
     * @param int $uid
     * @param int $score
     * @param string $reason
     */
    private function logCreditChange(int $uid, int $score, string $reason): void
    {
        // 这里可以记录到专门的信用变更日志表
        // 暂时记录到认证记录表
        $this->logAuthRecord($uid, 'credit', 'approved', "信用分数变更为{$score}分，原因：{$reason}");
    }
    
    /**
     * 清除企业缓存
     * @param int $uid
     */
    private function clearEnterpriseCache(int $uid): void
    {
        Cache::delete("enterprise_info_{$uid}");
        Cache::delete("enterprise_permissions_{$uid}");
    }
}
