<?php
namespace app\controller\admin;

use app\Request;
use crmeb\basic\BaseController;
use think\Response;

/**
 * 基类 所有控制器继承的类
 * Class AuthController
 * @package app\controller\admin
 * @property Request $request
 * @method Response success($message = '', array $data = [])
 * @method Response fail($message = '', array $data = [])
 */
class AuthController extends BaseController
{
    /**
     * 当前登陆管理员信息
     * @var array
     */
    protected $adminInfo = [];


    /**
     * 当前登陆管理员ID
     * @var int
     */
    protected $adminId = 0;


    protected $supplierId = 0;



    /**
     * 当前登陆管理员类型 4 是供应商
     * @var int
     */
    protected $adminType = 1;


    /**
     * 当前管理员权限
     * @var array
     */
    protected $auth = [];

    /**
     * 初始化
     */
    protected function initialize()
    {
        $this->loadAdminInfo();
    }

    /**
     * 加载管理员信息
     */
    protected function loadAdminInfo()
    {
        $this->adminId = $this->request->hasMacro('adminId') ? (int)$this->request->adminId() : 0;
        $this->adminInfo = $this->request->hasMacro('adminInfo') ? (array)$this->request->adminInfo() : [];
        $this->adminType = $this->adminInfo['admin_type'] ?? 1;
        if ($this->adminType == 4) {
            $this->supplierId = $this->adminId;
        }
        $this->auth = $this->adminInfo['rule'] ?? [];
    }
}
