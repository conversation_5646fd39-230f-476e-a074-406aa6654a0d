<?php
// +----------------------------------------------------------------------
// | 同城用工系统 - 师傅端收入管理
// +----------------------------------------------------------------------

namespace app\controller\staff;

use app\controller\BaseAuth;
use think\facade\Db;

/**
 * 收入管理控制器
 */
class Income extends BaseAuth
{
    /**
     * 获取收入概览
     */
    public function getOverview()
    {
        try {
            $userId = $this->getUserId();

            // 总收入
            $totalIncome = Db::name('task_applications')
                ->where('user_id', $userId)
                ->where('status', 'completed')
                ->sum('total_amount');

            // 本月收入
            $monthStart = strtotime(date('Y-m-01'));
            $monthEnd = strtotime(date('Y-m-t 23:59:59'));
            $monthIncome = Db::name('task_applications')
                ->where('user_id', $userId)
                ->where('status', 'completed')
                ->where('end_time', 'between', [$monthStart, $monthEnd])
                ->sum('total_amount');

            // 今日收入
            $todayStart = strtotime(date('Y-m-d 00:00:00'));
            $todayEnd = strtotime(date('Y-m-d 23:59:59'));
            $todayIncome = Db::name('task_applications')
                ->where('user_id', $userId)
                ->where('status', 'completed')
                ->where('end_time', 'between', [$todayStart, $todayEnd])
                ->sum('total_amount');

            // 可提现金额
            $availableAmount = Db::name('user_wallet')
                ->where('user_id', $userId)
                ->value('available_amount') ?? 0;

            // 待结算金额
            $pendingAmount = Db::name('task_applications')
                ->where('user_id', $userId)
                ->where('status', 'completed')
                ->where('settlement_status', 'pending')
                ->sum('total_amount');

            $overview = [
                'total_income' => $totalIncome ?? 0,
                'month_income' => $monthIncome ?? 0,
                'today_income' => $todayIncome ?? 0,
                'available_amount' => $availableAmount,
                'pending_amount' => $pendingAmount ?? 0
            ];

            return $this->success($overview);
        } catch (\Exception $e) {
            return $this->fail('获取收入概览失败：' . $e->getMessage());
        }
    }

    /**
     * 获取收入记录
     */
    public function getIncomeList()
    {
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 20);
        $type = $this->request->get('type', ''); // income, withdraw

        try {
            $userId = $this->getUserId();
            $where = [['user_id', '=', $userId]];

            if ($type === 'income') {
                // 收入记录
                $list = Db::name('task_applications')
                    ->alias('ta')
                    ->leftJoin('tasks t', 'ta.task_id = t.id')
                    ->where('ta.user_id', $userId)
                    ->where('ta.status', 'completed')
                    ->field('ta.id, ta.total_amount, ta.work_hours, ta.end_time,
                            t.task_title, t.hourly_rate')
                    ->order('ta.end_time desc')
                    ->page($page, $limit)
                    ->select()
                    ->toArray();

                $total = Db::name('task_applications')
                    ->where('user_id', $userId)
                    ->where('status', 'completed')
                    ->count();
            } else {
                // 提现记录
                $list = Db::name('user_withdraw')
                    ->where('user_id', $userId)
                    ->order('create_time desc')
                    ->page($page, $limit)
                    ->select()
                    ->toArray();

                $total = Db::name('user_withdraw')
                    ->where('user_id', $userId)
                    ->count();
            }

            return $this->success([
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ]);
        } catch (\Exception $e) {
            return $this->fail('获取收入记录失败：' . $e->getMessage());
        }
    }

    /**
     * 获取收入统计
     */
    public function getIncomeStats()
    {
        $type = $this->request->get('type', 'month'); // month, year

        try {
            $userId = $this->getUserId();
            $stats = [];

            if ($type === 'month') {
                // 近12个月收入统计
                for ($i = 11; $i >= 0; $i--) {
                    $month = date('Y-m', strtotime("-{$i} month"));
                    $monthStart = strtotime($month . '-01');
                    $monthEnd = strtotime($month . '-' . date('t', $monthStart) . ' 23:59:59');

                    $income = Db::name('task_applications')
                        ->where('user_id', $userId)
                        ->where('status', 'completed')
                        ->where('end_time', 'between', [$monthStart, $monthEnd])
                        ->sum('total_amount');

                    $stats[] = [
                        'period' => $month,
                        'income' => $income ?? 0
                    ];
                }
            } else {
                // 近5年收入统计
                for ($i = 4; $i >= 0; $i--) {
                    $year = date('Y', strtotime("-{$i} year"));
                    $yearStart = strtotime($year . '-01-01');
                    $yearEnd = strtotime($year . '-12-31 23:59:59');

                    $income = Db::name('task_applications')
                        ->where('user_id', $userId)
                        ->where('status', 'completed')
                        ->where('end_time', 'between', [$yearStart, $yearEnd])
                        ->sum('total_amount');

                    $stats[] = [
                        'period' => $year,
                        'income' => $income ?? 0
                    ];
                }
            }

            return $this->success($stats);
        } catch (\Exception $e) {
            return $this->fail('获取收入统计失败：' . $e->getMessage());
        }
    }

    /**
     * 申请提现
     */
    public function withdraw()
    {
        $amount = $this->request->post('amount', 0);
        $bankCard = $this->request->post('bank_card', '');
        $bankName = $this->request->post('bank_name', '');
        $accountName = $this->request->post('account_name', '');

        if ($amount <= 0) {
            return $this->fail('提现金额必须大于0');
        }

        if (!$bankCard) {
            return $this->fail('请输入银行卡号');
        }

        if (!$bankName) {
            return $this->fail('请输入银行名称');
        }

        if (!$accountName) {
            return $this->fail('请输入账户姓名');
        }

        try {
            $userId = $this->getUserId();

            // 检查可提现金额
            $wallet = Db::name('user_wallet')->where('user_id', $userId)->find();
            if (!$wallet || $wallet['available_amount'] < $amount) {
                return $this->fail('可提现金额不足');
            }

            // 检查最小提现金额
            $minWithdraw = 100; // 最小提现金额100元
            if ($amount < $minWithdraw) {
                return $this->fail('最小提现金额为' . $minWithdraw . '元');
            }

            Db::startTrans();
            try {
                // 创建提现申请
                $withdrawId = Db::name('user_withdraw')->insertGetId([
                    'user_id' => $userId,
                    'amount' => $amount,
                    'bank_card' => $bankCard,
                    'bank_name' => $bankName,
                    'account_name' => $accountName,
                    'status' => 'pending',
                    'create_time' => time()
                ]);

                // 冻结金额
                Db::name('user_wallet')
                    ->where('user_id', $userId)
                    ->dec('available_amount', $amount)
                    ->inc('frozen_amount', $amount)
                    ->update();

                Db::commit();

                return $this->success(['withdraw_id' => $withdrawId], '提现申请提交成功');
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            return $this->fail('提现申请失败：' . $e->getMessage());
        }
    }

    /**
     * 获取提现记录
     */
    public function getWithdrawList()
    {
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 20);

        try {
            $userId = $this->getUserId();

            $list = Db::name('user_withdraw')
                ->where('user_id', $userId)
                ->order('create_time desc')
                ->page($page, $limit)
                ->select()
                ->toArray();

            $total = Db::name('user_withdraw')
                ->where('user_id', $userId)
                ->count();

            return $this->success([
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ]);
        } catch (\Exception $e) {
            return $this->fail('获取提现记录失败：' . $e->getMessage());
        }
    }
} 