<?php
namespace app\controller\admin\user;

use app\controller\admin\AuthController;
use app\controller\admin\v1\user\用户id;
use app\jobs\BatchHandleJob;
use app\services\order\StoreOrderServices;
use app\services\other\queue\QueueServices;
use app\services\product\product\StoreProductLogServices;
use app\services\user\channel\ChannelMerchantServices;
use app\services\user\group\UserGroupServices;
use app\services\user\label\UserLabelServices;
use app\services\user\UserBatchProcessServices;
use app\services\user\UserServices;
use app\services\user\UserSpreadServices;
use app\services\user\UserWechatuserServices;
use FormBuilder\Exception\FormBuilderException;
use think\annotation\Inject;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\exception\ValidateException;
use think\Response;
use function app\controller\admin\v1\user\check_card;

class User extends AuthController
{

    /**
     * @var UserServices
     */
    #[Inject]
    protected UserServices $services;

    /**
     * 显示资源列表头部
     *
     * @return Response
     */
    public function type_header(): Response
    {
        $list = $this->services->typeHead();
        return $this->success(compact('list'));
    }

    /**
     * 显示资源列表
     *
     * @return Response
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function index(): Response
    {
        $where = $this->request->getMore([
            ['page', 1],
            ['limit', 20],
            ['nickname', ''],
            ['status', ''],
            ['pay_count', ''],
            ['is_promoter', ''],
            ['order', ''],
            ['data', ''],
            ['user_type', ''],
            ['country', ''],
            ['province', ''],
            ['city', ''],
            ['user_time_type', ''],
            ['user_time', ''],
            ['sex', ''],
            [['level', 0], 0],
            [['group_id', 'd'], 0],
            [['label_id', 'd'], 0],
            ['now_money', 'normal'],
            ['field_key', ''],
            ['isMember', ''],
            ['label_ids', ''],
            ['is_channel', ''],
        ]);
        if ($where['label_ids']) {
            $where['label_id'] = stringToIntArray($where['label_ids']);
            unset($where['label_ids']);
        }
        $where['user_time_type'] = $where['user_time_type'] == 'all' ? '' : $where['user_time_type'];
        return $this->success($this->services->index($where));
    }

    /**
     * 补充信息表单
     * @param $id
     * @return Response
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function extendInfoForm($id): Response
    {
        return $this->success($this->services->extendInfoForm((int)$id));
    }

    /**
     * 保存用户补充信息
     * @param $id
     * @return Response
     */
    public function saveExtendForm($id): Response
    {
        $data = $this->request->post();
        if (!$data) {
            return $this->fail('请提交要保存信息');
        }
        $this->services->saveExtendForm((int)$id, $data);
        return $this->success('保存成功');
    }

    /**
     * 显示创建资源表单页.
     *
     * @return Response
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function create(): Response
    {
        return $this->success($this->services->saveForm());
    }

    /**
     * 保存新建的资源
     *
     * @return Response
     */
    public function save(): Response
    {
        $data = $this->request->postMore([
            ['is_promoter', 0],
            ['real_name', ''],
            ['card_id', ''],
            ['birthday', ''],
            ['mark', ''],
            ['status', 0],
            ['level', 0],
            ['phone', 0],
            ['addres', ''],
            ['label_id', []],
            ['group_id', 0],
            ['pwd', ''],
            ['true_pwd', ''],
            ['spread_open', 1],
            ['sex', 0],
            ['provincials', ''],
            ['province', 0],
            ['city', 0],
            ['area', 0],
            ['street', 0],
            ['extend_info', []],
        ]);
        $this->validate(['pwd' => $data['pwd']], \app\validate\admin\user\UserValidate::class);
        if ($data['phone']) {
            if (!check_phone($data['phone'])) {
                return $this->fail('手机号码格式不正确');
            }
            if ($this->services->count(['phone' => $data['phone']])) {
                return $this->fail('手机号已经存在不能添加相同的手机号用户');
            }
            $data['nickname'] = substr_replace($data['phone'], '****', 3, 4);
        }
        if ($data['card_id']) {
            try {
                if (!check_card($data['card_id'])) return $this->fail('请输入正确的身份证');
            } catch (\Throwable $e) {
//				return $this->fail('请输入正确的身份证');
            }
        }
        if ($data['birthday']) {
            if (strtotime($data['birthday']) > time()) return $this->fail('生日请选择今天之前日期');
        }
        if ($data['pwd']) {
            if (!$data['true_pwd']) {
                return $this->fail('请输入确认密码');
            }
            if ($data['pwd'] != $data['true_pwd']) {
                return $this->fail('两次输入的密码不一致');
            }
            if (strlen($data['pwd']) < 6) {
                return $this->fail('密码长度最小6位');
            }
            if ($data['pwd'] == '123456') {
                return $this->fail('您设置的密码太过简单');
            }
            $data['pwd'] = md5($data['pwd']);
        } else {
            unset($data['pwd']);
        }
        unset($data['true_pwd']);
        $data['avatar'] = sys_config('h5_avatar');
        $data['adminId'] = $this->adminId;
        $data['user_type'] = 'h5';
        $lables = $data['label_id'];
        unset($data['label_id']);
        foreach ($lables as $k => $v) {
            if (!$v) {
                unset($lables[$k]);
            }
        }
        $data['birthday'] = empty($data['birthday']) ? 0 : strtotime($data['birthday']);
        $data['add_time'] = time();
        $data['extend_info'] = $this->services->handelExtendInfo($data['extend_info']);
        $this->services->transaction(function () use ($data, $lables) {
            $res = true;
            $userInfo = $this->services->save($data);
            if ($lables) {
                $res = $this->services->saveSetLabel([$userInfo->uid], $lables);
            }
            if ($data['level']) {
                $res = $this->services->saveGiveLevel((int)$userInfo->uid, (int)$data['level']);
            }
            if (!$res) {
                throw new ValidateException('保存添加用户失败');
            }
            event('user.register', [$this->services->get((int)$userInfo->uid), true, 0]);
        });
        event('user.create', $data);
        return $this->success('添加成功');
    }

    /**
     * 显示指定的资源
     *
     * @param int $id
     * @return Response
     */
    public function read($id): Response
    {
        return $this->success($this->services->read((int)$id));
    }

    /**
     * 赠送会员等级
     * @param $id
     * @return Response
     */
    public function give_level($id): Response
    {
        if (!$id) return $this->fail('缺少参数');
        return $this->success($this->services->giveLevel((int)$id));
    }

    /**
     * 执行赠送会员等级
     * @param $id
     * @return Response
     */
    public function save_give_level($id): Response
    {
        if (!$id) return $this->fail('缺少参数');
        [$level_id] = $this->request->postMore([
            ['level_id', 0],
        ], true);
        return $this->success($this->services->saveGiveLevel((int)$id, (int)$level_id) ? '赠送成功' : '赠送失败');
    }

    /**
     * 赠送付费会员时长
     * @param $id
     * @return Response
     */
    public function give_level_time($id): Response
    {
        if (!$id) return $this->fail('缺少参数');
        return $this->success($this->services->giveLevelTime((int)$id));
    }

    /**
     * 执行赠送付费会员时长
     * @param $id
     * @return Response
     * @throws \Exception
     */
    public function save_give_level_time($id): Response
    {
        if (!$id) return $this->fail('缺少参数');
        [$days_status, $days] = $this->request->postMore([
            ['days_status', 1],
            ['days', 0],
        ], true);
        return $this->success($this->services->saveGiveLevelTime((int)$id, (int)$days, (int)$days_status) ? '赠送成功' : '赠送失败');
    }

    /**
     * 清除会员等级
     * @param $id
     * @return Response
     */
    public function del_level($id): Response
    {
        if (!$id) return $this->fail('缺少参数');
        return $this->success($this->services->cleanUpLevel((int)$id) ? '清除成功' : '清除失败');
    }

    /**
     * 设置会员分组
     * @return Response
     */
    public function set_group(): Response
    {
        [$uids, $all, $where] = $this->request->postMore([
            ['uids', []],
            ['all', 0],
            ['where', []],
        ], true);
        return $this->success($this->services->setGroup($uids, $all, $where));
    }

    /**
     * 保存会员分组
     * @return Response
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function save_set_group(): Response
    {
        [$group_id, $uids, $all, $where] = $this->request->postMore([
            ['group_id', 0],
            ['uids', ''],
            ['all', 0],
            ['where', ""],
        ], true);
        if (!$uids && $all == 0) return $this->fail('缺少参数');
        if (!$group_id) return $this->fail('请选择分组');
        $type = 2;//代表设置用户标签
        if ($all == 0) {
            $uids = stringToIntArray($uids);
            $where = [];
        }
        if ($all == 1) {
            $where = $where ? json_decode($where, true) : [];
            /** @var UserWechatuserServices $userWechatUser */
            $userWechatUser = app()->make(UserWechatuserServices::class);
            $fields = 'u.uid';
            [$list, $count] = $userWechatUser->getWhereUserList($where, $fields);
            $uids = array_unique(array_column($list, 'uid'));
            $where = [];
        }
        /** @var UserGroupServices $userGroup */
        $userGroup = app()->make(UserGroupServices::class);
        if (!$userGroup->getGroup($group_id)) {
            return $this->fail('该分组不存在');
        }
        /** @var QueueServices $queueService */
        $queueService = app()->make(QueueServices::class);
        $queueService->setQueueData($where, 'uid', $uids, $type, $group_id);
        //加入队列
        BatchHandleJob::dispatch([$group_id, $type]);
        return $this->success('后台程序已执行用户分组任务!');
    }

    /**
     * 设置用户标签
     * @return Response
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function set_label(): Response
    {
        [$uids, $all, $where] = $this->request->postMore([
            ['uids', []],
            ['all', 0],
            ['where', ""],
        ], true);
        return $this->success($this->services->setLabel($uids, $all, $where));
    }

    /**
     * 保存用户标签
     * @return mixed
     */
    public function save_set_label()
    {
        [$lables, $uids, $all, $where] = $this->request->postMore([
            ['label_id', []],
            ['uids', ''],
            ['all', 0],
            ['where', ""],
        ], true);
        if (!$uids && $all == 0) return $this->fail('缺少参数');
        if (!$lables) return $this->fail('请选择标签');
        if ($all == 0) {
            $uids = is_array($uids) ? $uids : stringToIntArray($uids);
            $where = [];
        }
        if ($all == 1) {
            $where = $where ? (is_string($where) ? json_decode($where, true) : $where) : [];
            /** @var UserWechatuserServices $userWechatUser */
            $userWechatUser = app()->make(UserWechatuserServices::class);
            $fields = 'u.uid';
            [$list, $count] = $userWechatUser->getWhereUserList($where, $fields);
            $uids = array_unique(array_column($list, 'uid'));
            $where = [];
        }
        /** @var UserLabelServices $userLabelServices */
        $userLabelServices = app()->make(UserLabelServices::class);
        $count = $userLabelServices->getCount([['id', 'IN', $lables]]);
        if ($count != count($lables)) {
            return app('json')->fail('用户标签不存在或被删除');
        }
        $type = 3;//批量设置用户标签
        /** @var QueueServices $queueService */
        $queueService = app()->make(QueueServices::class);
        $queueService->setQueueData($where, 'uid', $uids, $type, $lables);
        //加入队列
        BatchHandleJob::dispatch([$lables, $type]);
        return $this->success('后台程序已执行批量设置用户标签任务!');
    }

    /**
     * 编辑其他
     * @param $id
     * @return Response
     * @throws FormBuilderException
     */
    public function edit_other($id, $type = 0): Response
    {
        if (!$id) return $this->fail('数据不存在');
        return $this->success($this->services->editOther((int)$id, $type));
    }

    /**
     * 执行编辑其他
     * @param int $id
     * @return Response
     */
    public function update_other($id): Response
    {
        $data = $this->request->postMore([
            ['money_status', 0],
            ['money', 0],
            ['money_mark', ''],
            ['integration_status', 0],
            ['integration', 0],
            ['integral_mark', ''],
        ]);
        if (!$id) return $this->fail('数据不存在');
        if (!is_numeric($data['money'])) return $this->fail('金额必须是数字');
        $data['adminId'] = $this->adminId;
        $data['money'] = (string)$data['money'];
        $data['integration'] = (string)$data['integration'];
        $data['is_other'] = true;
        return $this->success($this->services->updateInfo($id, $data) ? '修改成功' : '修改失败');
    }

    /**
     * 修改user表状态
     *
     * @return array
     */
    public function set_status($status, $id)
    {
//        if ($status == '' || $id == 0) return $this->fail('参数错误');
//        UserModel::where(['uid' => $id])->update(['status' => $status]);
        return $this->success($status == 0 ? '禁用成功' : '解禁成功');
    }

    /**
     * 编辑会员信息
     * @param $id
     * @return mixed|\think\response\Json|void
     */
    public function edit($id)
    {
        if (!$id) return $this->fail('数据不存在');
        return $this->success($this->services->edit($id));
    }

    public function update($id)
    {
        $data = $this->request->postMore([
            ['money_status', 0],
            ['is_promoter', 0],
            ['real_name', ''],
            ['card_id', ''],
            ['birthday', ''],
            ['mark', ''],
            ['money', 0],
            ['integration_status', 0],
            ['integration', 0],
            ['status', 0],
            ['level', 0],
            ['phone', 0],
            ['addres', ''],
            ['label_id', []],
            ['group_id', 0],
            ['pwd', ''],
            ['true_pwd'],
            ['spread_open', 1],
            ['sex', 0],
            ['provincials', ''],
            ['province', 0],
            ['city', 0],
            ['area', 0],
            ['street', 0],
            ['spread_uid', -1],
            ['extend_info', []]
        ]);
        if ($data['phone']) {
            if (!check_phone($data['phone'])) return $this->fail('手机号码格式不正确');
        }
        if ($data['card_id']) {
            try {
                if (!check_card($data['card_id'])) return $this->fail('请输入正确的身份证');
            } catch (\Throwable $e) {
//				return $this->fail('请输入正确的身份证');
            }
        }
        if ($data['birthday']) {
            if (strtotime($data['birthday']) > time()) return $this->fail('生日请选择今天之前日期');
        }
        if ($data['pwd']) {
            if (!$data['true_pwd']) {
                return $this->fail('请输入确认密码');
            }
            if ($data['pwd'] != $data['true_pwd']) {
                return $this->fail('两次输入的密码不一致');
            }
            if ($data['pwd'] == '123456') {
                return $this->fail('您设置的密码太过简单');
            }
            $this->validate(['pwd' => $data['pwd']], \app\validate\admin\user\UserValidate::class);
            $data['pwd'] = md5($data['pwd']);
        } else {
            unset($data['pwd']);
        }
        $userInfo = $this->services->get($id);
        if (!$userInfo) {
            return $this->fail('用户不存在');
        }
        if (!in_array($data['spread_uid'], [0, -1])) {
            $spreadUid = $data['spread_uid'];
            if ($id == $spreadUid) {
                return $this->fail('上级推广人不能为自己');
            }
            if (!$this->services->count(['uid' => $spreadUid])) {
                return $this->fail('上级用户不存在');
            }
            $spreadInfo = $this->services->get($spreadUid);
            if ($spreadInfo->spread_uid == $id) {
                return $this->fail('上级推广人不能为自己下级');
            }
        }
        unset($data['true_pwd']);
        if (!$id) return $this->fail('数据不存在');
        $data['adminId'] = $this->adminId;
        $data['money'] = (string)$data['money'];
        $data['integration'] = (string)$data['integration'];
        if ($data['extend_info']) {
            $data['extend_info'] = $this->services->handelExtendInfo($data['extend_info']);
        }
        return $this->success($this->services->updateInfo((int)$id, $data) ? '修改成功' : '修改失败');
    }

    /**
     * 获取单个用户信息
     * @param $id 用户id
     * @return mixed
     */
    public function oneUserInfo($id)
    {
        $data = $this->request->getMore([
            ['type', ''],
        ]);
        $id = (int)$id;
        if ($data['type'] == '') return $this->fail('缺少参数');
        return $this->success($this->services->oneUserInfo($id, $data['type']));
    }

    /**
     * 同步微信粉丝用户
     * @return mixed
     */
    public function syncWechatUsers()
    {
        $this->services->syncWechatUsers();
        return $this->success('加入消息队列成功，正在异步执行中');
    }

    /**
     * 商品浏览记录
     * @param $id
     * @param StoreProductLogServices $services
     * @return mixed
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function visitList($id, StoreProductLogServices $services)
    {
        $where['uid'] = (int)$id;
        $where['type'] = 'visit';
        return app('json')->success($services->getList($where, 'product_id'));
    }

    /**
     * 获取推广人记录
     * @param $id
     * @param UserSpreadServices $services
     * @return mixed
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function spreadList($id, UserSpreadServices $services)
    {
        $where['store_id'] = 0;
        $where['staff_id'] = 0;
        $where['uid'] = $id;
        return app('json')->success($services->getSpreadList($where, '*', ['spreadUser', 'admin'], false));
    }

    /**
     * 用户批量操作
     * @return mixed
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function batchProcess(UserBatchProcessServices $batchProcessServices)
    {
        [$type, $uids, $all, $where, $data] = $this->request->postMore([
            ['type', 1],
            ['uids', ''],
            ['all', 0],
            ['where', ""],
            ['data', []]
        ], true);
        if (!$uids && $all == 0) return $this->fail('请选择批处理用户');
        if (!$data) {
            return $this->fail('请选择批处理数据');
        }
        //批量操作
        $batchProcessServices->batchProcess((int)$type, $uids, $data, !!$all, $where);
        return app('json')->success('已加入消息队列,请稍后查看');
    }

    /**
     * 用户消费记录
     * @param StoreOrderServices $services
     * @return Response
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User: liusl
     * DateTime: 2025/4/28 下午5:06
     */
    public function getUserOrderList($id, StoreOrderServices $services)
    {
        [$channel] = $this->request->postMore([
            ['channel', 0],
        ], true);
        if ($channel == 1) {
            $id = app()->make(ChannelMerchantServices::class)->value(['id' => $id, 'is_del' => 0], 'uid');
            if (!$id) {
                return app('json')->fail('采购商不存在');
            }
        }
        return app('json')->success($services->getUserOrderList((int)$id, $channel));
    }
}
