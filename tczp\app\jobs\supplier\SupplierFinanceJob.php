<?php

namespace app\jobs\supplier;


use app\services\supplier\finance\SupplierFlowingWaterServices;
use crmeb\basic\BaseJobs;
use crmeb\traits\QueueTrait;
use think\facade\Log;

/**
 * 供应商资金流水记录
 * Class SupplierFinanceJob
 * @package app\jobs
 */
class SupplierFinanceJob extends BaseJobs
{
    use QueueTrait;

    /**供应商流水
     * @param int $oid
     * @param int $type
     * @return bool
     */
    public function doJob(int $oid, int $type)
    {
        try {
            /** @var SupplierFlowingWaterServices $supplierFlowServices */
            $supplierFlowServices = app()->make(SupplierFlowingWaterServices::class);
            $supplierFlowServices->setSupplierFinance($oid, $type);
        } catch (\Throwable $e) {
            Log::error('记录流水失败:' . $e->getMessage());
        }
        return true;
    }
}
