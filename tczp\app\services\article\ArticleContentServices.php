<?php

namespace app\services\article;

use app\dao\article\ArticleContentDao;
use app\services\BaseServices;
use think\annotation\Inject;

/**
 * 文章内容
 * Class ArticleContentServices
 * @package app\services\article
 * @mixin ArticleContentDao
 */
class ArticleContentServices extends BaseServices
{
    /**
     * @var ArticleContentDao
     */
    #[Inject]
    protected ArticleContentDao $dao;

    /**
     * 删除
     * @param int $id
     * @return bool
     */
    public function del(int $id)
    {
        return $this->dao->del($id);
    }
}
