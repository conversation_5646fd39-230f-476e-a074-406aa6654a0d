<?php

namespace app\jobs\user;


use app\services\user\level\UserLevelServices;
use crmeb\basic\BaseJobs;
use crmeb\traits\QueueTrait;
use think\facade\Log;

/**
 * 检测会员等级
 * Class UserLevelJob
 * @package app\jobs\user
 */
class UserLevelJob extends BaseJobs
{

    use QueueTrait;

    public function doJob($uid)
    {
        try {
            /** @var UserLevelServices $levelServices */
            $levelServices = app()->make(UserLevelServices::class);
            $levelServices->detection((int)$uid);
        } catch (\Throwable $e) {

            response_log_write([
                'message' => '会员等级升级失败,失败原因:' . $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
        return true;
    }
}
