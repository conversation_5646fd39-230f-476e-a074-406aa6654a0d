<?php

namespace app\jobs\work;


use app\services\work\WorkGroupChatServices;
use crmeb\basic\BaseJobs;
use crmeb\traits\QueueTrait;

/**
 * 企业微信群
 * Class WorkGroupChatJob
 * @package app\jobs\work
 */
class WorkGroupChatJob extends BaseJobs
{

    use QueueTrait;

    public function authChat($corpId, $chatId)
    {
        /** @var WorkGroupChatServices $make */
        $make = app()->make(WorkGroupChatServices::class);
        return $make->saveWorkGroupChat($corpId, $chatId);
    }

    /**
     * @param $nextCursor
     * @return bool
     * <AUTHOR>
     * @email <EMAIL>
     * @date 2022/10/10
     */
    public function authGroupChat($nextCursor)
    {
        /** @var WorkGroupChatServices $make */
        $make = app()->make(WorkGroupChatServices::class);
        return $make->authGroupChat($nextCursor);
    }

}
