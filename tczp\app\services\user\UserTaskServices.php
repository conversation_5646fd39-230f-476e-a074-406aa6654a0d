<?php
declare(strict_types=1);

namespace app\services\user;

use app\services\BaseServices;
use think\facade\Db;
use think\facade\Cache;

/**
 * 用户任务管理服务类
 * Class UserTaskServices
 * @package app\services\user
 */
class UserTaskServices extends BaseServices
{
    /**
     * 发布任务
     * @param int $uid
     * @param array $data
     * @return int
     */
    public function publishTask(int $uid, array $data): int
    {
        // 验证必填字段
        $required = ['task_title', 'task_description', 'task_category_id', 'hourly_rate'];
        foreach ($required as $field) {
            if (empty($data[$field])) {
                throw new \Exception("字段 {$field} 不能为空");
            }
        }
        
        try {
            Db::startTrans();
            
            // 获取企业信息
            $enterpriseInfo = null;
            $enterpriseAuthService = app()->make(EnterpriseAuthServices::class);
            if ($enterpriseAuthService->isEnterprise($uid)) {
                $enterpriseInfo = $enterpriseAuthService->getEnterpriseInfo($uid);
            }
            
            // 构建任务数据
            $taskData = [
                'user_id' => $uid,
                'enterprise_id' => $enterpriseInfo['id'] ?? null,
                'task_title' => $data['task_title'],
                'task_description' => $data['task_description'],
                'task_category_id' => $data['task_category_id'],
                'task_type' => $data['task_type'] ?? 'simple',
                'required_workers' => $data['required_workers'] ?? 1,
                'task_location' => $data['task_location'] ?? '',
                'hourly_rate' => $data['hourly_rate'],
                'payment_type' => $data['payment_type'] ?? 'hourly',
                'deposit_rate' => $data['deposit_rate'] ?? 20.00,
                'difficulty_level' => $data['difficulty_level'] ?? 1,
                'estimated_duration' => $data['estimated_duration'] ?? 2,
                'required_skills' => json_encode($data['required_skills'] ?? []),
                'worker_requirements' => json_encode($data['worker_requirements'] ?? []),
                'preferred_workers' => json_encode($data['preferred_workers'] ?? []),
                'contact_person' => $data['contact_person'] ?? ($enterpriseInfo['contact_person'] ?? ''),
                'contact_phone' => $data['contact_phone'] ?? ($enterpriseInfo['contact_phone'] ?? ''),
                'insurance_required' => $data['insurance_required'] ?? 0,
                'contract_required' => $data['contract_required'] ?? 0,
                'deadline' => $data['deadline'] ?? 0,
                'status' => 'published',
                'create_time' => time(),
                'update_time' => time()
            ];
            
            $taskId = Db::name('tasks')->insertGetId($taskData);
            
            // 更新企业统计
            if ($enterpriseInfo) {
                Db::name('enterprise_profiles')
                    ->where('id', $enterpriseInfo['id'])
                    ->inc('task_count', 1)
                    ->update(['update_time' => time()]);
            }
            
            // 更新用户统计
            Db::name('user')
                ->where('uid', $uid)
                ->inc('task_published', 1)
                ->update(['last_active_time' => time()]);
            
            Db::commit();
            
            return $taskId;
            
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 获取我发布的任务列表
     * @param int $uid
     * @param array $where
     * @param int $page
     * @param int $limit
     * @return array
     */
    public function getMyTasks(int $uid, array $where = [], int $page = 1, int $limit = 20): array
    {
        $query = Db::name('tasks')
            ->alias('t')
            ->join('task_categories tc', 't.task_category_id = tc.id', 'left')
            ->where('t.user_id', $uid);
            
        // 状态筛选
        if (!empty($where['status'])) {
            $query->where('t.status', $where['status']);
        }
        
        // 任务类型筛选
        if (!empty($where['task_type'])) {
            $query->where('t.task_type', $where['task_type']);
        }
        
        // 时间筛选
        if (!empty($where['start_time']) && !empty($where['end_time'])) {
            $query->whereBetween('t.create_time', [$where['start_time'], $where['end_time']]);
        }
        
        $total = $query->count();
        $list = $query->field([
                't.id', 't.task_title', 't.task_description', 't.task_location',
                't.hourly_rate', 't.required_workers', 't.task_type', 't.payment_type',
                't.status', 't.create_time', 't.deadline',
                'tc.category_name'
            ])
            ->order('t.create_time desc')
            ->page($page, $limit)
            ->select()
            ->toArray();
            
        // 获取每个任务的申请统计
        foreach ($list as &$item) {
            $applicationStats = Db::name('task_applications')
                ->where('task_id', $item['id'])
                ->field([
                    'COUNT(*) as total_applications',
                    'SUM(CASE WHEN status = "pending" THEN 1 ELSE 0 END) as pending_count',
                    'SUM(CASE WHEN status = "approved" THEN 1 ELSE 0 END) as approved_count'
                ])
                ->find();
                
            $item['application_stats'] = $applicationStats ?: [
                'total_applications' => 0,
                'pending_count' => 0,
                'approved_count' => 0
            ];
        }
            
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    }
    
    /**
     * 获取任务申请列表
     * @param int $taskId
     * @param int $page
     * @param int $limit
     * @return array
     */
    public function getTaskApplications(int $taskId, int $page = 1, int $limit = 20): array
    {
        $query = Db::name('task_applications')
            ->alias('ta')
            ->join('user u', 'ta.user_id = u.uid')
            ->join('worker_profiles wp', 'u.uid = wp.user_id', 'left')
            ->where('ta.task_id', $taskId);
            
        $total = $query->count();
        $list = $query->field([
                'ta.id', 'ta.status', 'ta.quoted_price', 'ta.application_note',
                'ta.skill_match_score', 'ta.distance', 'ta.create_time',
                'u.uid', 'u.nickname', 'u.avatar', 'u.phone',
                'wp.real_name', 'wp.work_experience', 'wp.rating', 'wp.order_count'
            ])
            ->order('ta.skill_match_score desc, ta.create_time asc')
            ->page($page, $limit)
            ->select()
            ->toArray();
            
        // 获取每个申请者的技能信息
        foreach ($list as &$item) {
            if ($item['uid']) {
                $skills = Db::name('worker_skills_new')
                    ->alias('ws')
                    ->join('skills_new s', 'ws.skill_id = s.id')
                    ->where('ws.worker_id', $item['uid'])
                    ->where('ws.auth_status', 'approved')
                    ->column('s.skill_name');
                    
                $item['skills'] = $skills;
            } else {
                $item['skills'] = [];
            }
        }
            
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    }
    
    /**
     * 处理任务申请
     * @param int $applicationId
     * @param string $action
     * @param string $reason
     * @return bool
     */
    public function handleTaskApplication(int $applicationId, string $action, string $reason = ''): bool
    {
        $allowedActions = ['approve', 'reject'];
        if (!in_array($action, $allowedActions)) {
            throw new \Exception('无效的操作');
        }
        
        try {
            Db::startTrans();
            
            // 获取申请信息
            $application = Db::name('task_applications')->where('id', $applicationId)->find();
            if (!$application) {
                throw new \Exception('申请不存在');
            }
            
            if ($application['status'] !== 'pending') {
                throw new \Exception('申请状态不允许操作');
            }
            
            // 更新申请状态
            $updateData = [
                'status' => $action === 'approve' ? 'approved' : 'rejected',
                'update_time' => time()
            ];
            
            if ($action === 'reject' && $reason) {
                $updateData['reject_reason'] = $reason;
            }
            
            Db::name('task_applications')
                ->where('id', $applicationId)
                ->update($updateData);
            
            // 如果是批准申请，更新任务状态
            if ($action === 'approve') {
                Db::name('tasks')
                    ->where('id', $application['task_id'])
                    ->update([
                        'status' => 'assigned',
                        'assigned_worker_id' => $application['user_id'],
                        'update_time' => time()
                    ]);
            }
            
            Db::commit();
            
            return true;
            
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 取消任务
     * @param int $taskId
     * @param int $uid
     * @param string $reason
     * @return bool
     */
    public function cancelTask(int $taskId, int $uid, string $reason = ''): bool
    {
        try {
            Db::startTrans();
            
            // 检查任务是否存在且属于该用户
            $task = Db::name('tasks')
                ->where('id', $taskId)
                ->where('user_id', $uid)
                ->find();
                
            if (!$task) {
                throw new \Exception('任务不存在或无权操作');
            }
            
            if (!in_array($task['status'], ['published', 'assigned'])) {
                throw new \Exception('任务状态不允许取消');
            }
            
            // 更新任务状态
            Db::name('tasks')
                ->where('id', $taskId)
                ->update([
                    'status' => 'cancelled',
                    'cancel_reason' => $reason,
                    'cancel_time' => time(),
                    'update_time' => time()
                ]);
            
            // 如果有已批准的申请，需要处理
            if ($task['status'] === 'assigned') {
                Db::name('task_applications')
                    ->where('task_id', $taskId)
                    ->where('status', 'approved')
                    ->update([
                        'status' => 'cancelled',
                        'cancel_reason' => '任务已取消',
                        'update_time' => time()
                    ]);
            }
            
            Db::commit();
            
            return true;
            
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 确认任务完成
     * @param int $taskId
     * @param int $uid
     * @param array $data
     * @return bool
     */
    public function confirmTaskCompletion(int $taskId, int $uid, array $data = []): bool
    {
        try {
            Db::startTrans();
            
            // 检查任务是否存在且属于该用户
            $task = Db::name('tasks')
                ->where('id', $taskId)
                ->where('user_id', $uid)
                ->find();
                
            if (!$task) {
                throw new \Exception('任务不存在或无权操作');
            }
            
            if ($task['status'] !== 'completed') {
                throw new \Exception('任务状态不允许确认');
            }
            
            // 更新任务状态
            Db::name('tasks')
                ->where('id', $taskId)
                ->update([
                    'status' => 'confirmed',
                    'confirm_time' => time(),
                    'update_time' => time()
                ]);
            
            // 更新企业统计
            if ($task['enterprise_id']) {
                Db::name('enterprise_profiles')
                    ->where('id', $task['enterprise_id'])
                    ->inc('completed_count', 1)
                    ->update(['update_time' => time()]);
            }
            
            // 更新用户统计
            Db::name('user')
                ->where('uid', $uid)
                ->inc('task_completed', 1)
                ->update(['last_active_time' => time()]);
            
            Db::commit();
            
            return true;
            
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 获取任务模板列表
     * @param int $uid
     * @param array $where
     * @param int $page
     * @param int $limit
     * @return array
     */
    public function getTaskTemplates(int $uid, array $where = [], int $page = 1, int $limit = 20): array
    {
        $query = Db::name('task_templates')
            ->alias('tt')
            ->join('task_categories tc', 'tt.category_id = tc.id', 'left')
            ->where(function ($q) use ($uid) {
                $q->where('tt.user_id', $uid)->whereOr('tt.is_public', 1);
            });
            
        // 分类筛选
        if (!empty($where['category_id'])) {
            $query->where('tt.category_id', $where['category_id']);
        }
        
        // 关键词搜索
        if (!empty($where['keyword'])) {
            $query->where('tt.template_name', 'like', "%{$where['keyword']}%");
        }
        
        $total = $query->count();
        $list = $query->field([
                'tt.id', 'tt.template_name', 'tt.template_data', 'tt.use_count',
                'tt.is_public', 'tt.create_time',
                'tc.category_name'
            ])
            ->order('tt.use_count desc, tt.create_time desc')
            ->page($page, $limit)
            ->select()
            ->toArray();
            
        // 解析模板数据
        foreach ($list as &$item) {
            $item['template_data'] = json_decode($item['template_data'], true) ?: [];
        }
            
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    }
    
    /**
     * 保存任务模板
     * @param int $uid
     * @param array $data
     * @return bool
     */
    public function saveTaskTemplate(int $uid, array $data): bool
    {
        $templateData = [
            'user_id' => $uid,
            'template_name' => $data['template_name'],
            'template_data' => json_encode($data['template_data']),
            'category_id' => $data['category_id'],
            'is_public' => $data['is_public'] ?? 0,
            'status' => 1,
            'create_time' => time(),
            'update_time' => time()
        ];
        
        return Db::name('task_templates')->insert($templateData);
    }
}
