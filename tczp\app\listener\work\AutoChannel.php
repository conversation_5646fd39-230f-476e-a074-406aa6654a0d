<?php

namespace app\listener\work;


use app\services\work\WorkChannelCodeServices;
use crmeb\interfaces\ListenerInterface;
use crmeb\utils\Cron;
use think\facade\Log;

/**
 * 渠道码定时任务
 * Class AutoChannel
 * @package app\listener\work
 */
class AutoChannel extends <PERSON>ron implements ListenerInterface
{

    public function handle($event): void
    {
        //1分钟执行一次
        $this->tick(1000, function () {
            /** @var WorkChannelCodeServices $service */
            $service = app()->make(WorkChannelCodeServices::class);

            try {
                $service->cronHandle();
            } catch (\Throwable $e) {
                Log::error(json_encode([
                    'message' => '渠道码定时任务执行错误：' . $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]));
            }
        });
    }
}
