<?php
declare(strict_types=1);

namespace app\http\middleware\user;

use app\Request;
use crmeb\interfaces\MiddlewareInterface;
use think\Response;

/**
 * 用户端权限控制中间件
 * Class UserPermissionMiddleware
 * @package app\http\middleware\user
 */
class UserPermissionMiddleware implements MiddlewareInterface
{
    /**
     * 处理请求
     * @param Request $request
     * @param \Closure $next
     * @param string $permission 需要的权限
     * @return Response
     */
    public function handle(Request $request, \Closure $next, string $permission = '')
    {
        try {
            // 获取用户ID
            $uid = $request->uid ?? 0;
            
            if (!$uid) {
                return $this->forbiddenResponse('用户未登录');
            }
            
            // 如果没有指定权限，直接通过
            if (!$permission) {
                return $next($request);
            }
            
            // 检查权限
            if (!$this->checkPermission($request, $permission)) {
                return $this->forbiddenResponse('权限不足');
            }
            
            return $next($request);
            
        } catch (\Exception $e) {
            return $this->forbiddenResponse($e->getMessage());
        }
    }
    
    /**
     * 检查权限
     * @param Request $request
     * @param string $permission
     * @return bool
     */
    private function checkPermission(Request $request, string $permission): bool
    {
        // 获取用户权限
        $permissions = $request->permissions ?? [];
        
        // 检查是否有该权限
        return in_array($permission, $permissions) || in_array('*', $permissions);
    }
    
    /**
     * 返回权限不足响应
     * @param string $message
     * @return Response
     */
    private function forbiddenResponse(string $message): Response
    {
        return app('json')->make(403, $message);
    }
}
