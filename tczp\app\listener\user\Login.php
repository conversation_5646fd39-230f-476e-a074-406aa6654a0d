<?php

namespace app\listener\user;

use app\jobs\user\LoginJob;
use crmeb\interfaces\ListenerInterface;

/**
 * 登录完成后置事件
 * Class Register
 * @package app\listener\user
 */
class Login implements ListenerInterface
{
    /**
     * 登录完成后置事件
     * @param $event
     */
    public function handle($event): void
    {
        [$uid, $ip] = $event;
        try {
            LoginJob::dispatch([$uid, $ip]);
        } catch (\Throwable $e) {

        }
    }

}
