<?php
declare(strict_types=1);

// +----------------------------------------------------------------------
// | 同城用工系统 - 师傅端认证管理 (优化版)
// +----------------------------------------------------------------------

namespace app\controller\staff;

use app\controller\BaseController;
use app\services\staff\StaffAuthServices;
use app\services\staff\StaffSkillServices;
use think\Response;

/**
 * 师傅认证管理控制器 - 优化版
 * Class Auth
 * @package app\controller\staff
 */
class Auth extends BaseController
{
    /**
     * 师傅认证服务
     * @var StaffAuthServices
     */
    protected $staffAuthService;

    /**
     * 师傅技能服务
     * @var StaffSkillServices
     */
    protected $staffSkillService;

    /**
     * 初始化
     */
    protected function initialize(): void
    {
        parent::initialize();
        $this->staffAuthService = app()->make(StaffAuthServices::class);
        $this->staffSkillService = app()->make(StaffSkillServices::class);
    }

    /**
     * 获取用户ID
     * @return int
     */
    private function getUserId(): int
    {
        return $this->request->uid ?? 0;
    }

    /**
     * 获取师傅信息
     * @return array|null
     */
    private function getWorkerInfo(): ?array
    {
        $uid = $this->getUserId();
        return $this->staffAuthService->getWorkerInfo($uid);
    }

    /**
     * 申请师傅注册
     * @return Response
     */
    public function applyRegister(): Response
    {
        try {
            $data = $this->request->post();

            // 验证必填字段
            $required = ['real_name', 'id_card', 'phone'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    return $this->fail("请填写{$field}");
                }
            }

            $uid = $this->getUserId();

            // 检查是否已经是师傅
            if ($this->staffAuthService->isWorker($uid)) {
                return $this->fail('您已经是师傅用户');
            }

            // 创建师傅档案
            $result = $this->staffAuthService->createWorkerProfile($uid, $data);

            if ($result) {
                return $this->success([], '师傅注册申请提交成功，请等待审核');
            } else {
                return $this->fail('申请提交失败');
            }

        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 提交身份认证
     * @return Response
     */
    public function submitIdentityAuth(): Response
    {
        try {
            $data = $this->request->post();

            // 验证必填字段
            $required = ['real_name', 'id_card', 'phone'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    return $this->fail("请填写{$field}");
                }
            }

            $uid = $this->getUserId();
            $workerInfo = $this->getWorkerInfo();

            if (!$workerInfo) {
                return $this->fail('请先申请成为师傅');
            }

            if ($workerInfo['auth_status'] === 'approved') {
                return $this->fail('身份认证已通过，无需重复提交');
            }

            // 更新师傅信息
            $updateData = [
                'real_name' => $data['real_name'],
                'id_card' => $data['id_card'],
                'phone' => $data['phone'],
                'emergency_contact' => $data['emergency_contact'] ?? '',
                'work_experience' => $data['work_experience'] ?? 0,
                'service_areas' => json_encode($data['service_areas'] ?? []),
                'auth_status' => 'reviewing',
                'update_time' => time()
            ];

            $result = \think\facade\Db::name('worker_profiles')
                ->where('user_id', $uid)
                ->update($updateData);

            if ($result) {
                return $this->success([], '身份认证资料提交成功，请等待审核');
            } else {
                return $this->fail('提交失败');
            }

        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 申请技能认证
     * @return Response
     */
    public function applySkillAuth(): Response
    {
        try {
            $data = $this->request->post();

            // 验证必填字段
            $required = ['skill_id', 'skill_level', 'hourly_rate'];
            foreach ($required as $field) {
                if (!isset($data[$field]) || $data[$field] === '') {
                    return $this->fail("请填写{$field}");
                }
            }

            $uid = $this->getUserId();
            $workerInfo = $this->getWorkerInfo();

            if (!$workerInfo) {
                return $this->fail('请先申请成为师傅');
            }

            if ($workerInfo['auth_status'] !== 'approved') {
                return $this->fail('请先完成身份认证');
            }

            // 申请技能认证
            $result = $this->staffSkillService->applySkillAuth($workerInfo['id'], $data);

            if ($result) {
                return $this->success([], '技能认证申请提交成功，请等待审核');
            } else {
                return $this->fail('申请提交失败');
            }

        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取身份认证状态
     * @return Response
     */
    public function getIdentityStatus(): Response
    {
        try {
            $uid = $this->getUserId();
            $workerInfo = $this->getWorkerInfo();

            if (!$workerInfo) {
                return $this->success([
                    'is_worker' => false,
                    'auth_status' => 'not_applied',
                    'message' => '尚未申请成为师傅'
                ]);
            }

            $data = [
                'is_worker' => true,
                'auth_status' => $workerInfo['auth_status'],
                'worker_info' => [
                    'worker_no' => $workerInfo['worker_no'],
                    'real_name' => $workerInfo['real_name'],
                    'phone' => $workerInfo['phone'],
                    'work_experience' => $workerInfo['work_experience'],
                    'rating' => $workerInfo['rating'],
                    'order_count' => $workerInfo['order_count'],
                    'completion_rate' => $workerInfo['completion_rate'],
                    'create_time' => $workerInfo['create_time'],
                    'auth_time' => $workerInfo['auth_time']
                ]
            ];

            return $this->success($data);

        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取技能认证状态
     * @return Response
     */
    public function getSkillAuthStatus(): Response
    {
        try {
            $uid = $this->getUserId();
            $workerInfo = $this->getWorkerInfo();

            if (!$workerInfo) {
                return $this->fail('请先申请成为师傅');
            }

            // 获取技能列表
            $skills = $this->staffSkillService->getWorkerSkills($workerInfo['id']);

            // 获取技能认证统计
            $stats = $this->staffSkillService->getSkillAuthStats($workerInfo['id']);

            $data = [
                'skills' => $skills,
                'stats' => $stats
            ];

            return $this->success($data);

        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 上传身份认证文件
     * @return Response
     */
    public function uploadIdentityFiles(): Response
    {
        try {
            $files = $this->request->file();

            if (empty($files)) {
                return $this->fail('请选择要上传的文件');
            }

            $uid = $this->getUserId();
            $uploadedFiles = [];

            foreach ($files as $key => $file) {
                if ($file->isValid()) {
                    // 这里应该调用文件上传服务
                    // $uploadResult = $this->uploadService->upload($file);
                    // 暂时返回模拟数据
                    $uploadedFiles[$key] = [
                        'url' => '/uploads/identity/' . $file->getOriginalName(),
                        'name' => $file->getOriginalName(),
                        'size' => $file->getSize()
                    ];
                }
            }

            return $this->success($uploadedFiles, '文件上传成功');

        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 上传技能认证文件
     * @return Response
     */
    public function uploadSkillFiles(): Response
    {
        try {
            $files = $this->request->file();

            if (empty($files)) {
                return $this->fail('请选择要上传的文件');
            }

            $uid = $this->getUserId();
            $uploadedFiles = [];

            foreach ($files as $key => $file) {
                if ($file->isValid()) {
                    // 这里应该调用文件上传服务
                    // $uploadResult = $this->uploadService->upload($file);
                    // 暂时返回模拟数据
                    $uploadedFiles[$key] = [
                        'url' => '/uploads/skills/' . $file->getOriginalName(),
                        'name' => $file->getOriginalName(),
                        'size' => $file->getSize()
                    ];
                }
            }

            return $this->success($uploadedFiles, '文件上传成功');

        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 上传证书文件
     * @return Response
     */
    public function uploadCertificate(): Response
    {
        try {
            $files = $this->request->file();

            if (empty($files)) {
                return $this->fail('请选择要上传的证书');
            }

            $uid = $this->getUserId();
            $uploadedFiles = [];

            foreach ($files as $key => $file) {
                if ($file->isValid()) {
                    // 验证文件类型
                    $allowedTypes = ['jpg', 'jpeg', 'png', 'pdf'];
                    $extension = strtolower($file->getOriginalExtension());

                    if (!in_array($extension, $allowedTypes)) {
                        return $this->fail('只支持JPG、PNG、PDF格式的文件');
                    }

                    // 这里应该调用文件上传服务
                    // $uploadResult = $this->uploadService->upload($file);
                    // 暂时返回模拟数据
                    $uploadedFiles[$key] = [
                        'url' => '/uploads/certificates/' . $file->getOriginalName(),
                        'name' => $file->getOriginalName(),
                        'size' => $file->getSize(),
                        'type' => $extension
                    ];
                }
            }

            return $this->success($uploadedFiles, '证书上传成功');

        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}
                'auth_data' => json_encode($authData),
                'status' => 'pending',
                'create_time' => time()
            ]);

            return $this->success(['auth_id' => $authId], '认证申请提交成功，请等待审核');
        } catch (\Exception $e) {
            return $this->fail('提交认证失败：' . $e->getMessage());
        }
    }

    /**
     * 获取认证状态
     */
    public function getAuthStatus()
    {
        try {
            $userId = $this->getUserId();

            $authList = Db::name('user_auth')
                ->where('user_id', $userId)
                ->order('create_time desc')
                ->select()
                ->toArray();

            $authStatus = [];
            foreach ($authList as $auth) {
                $authStatus[$auth['auth_type']] = [
                    'status' => $auth['status'],
                    'auth_data' => json_decode($auth['auth_data'], true),
                    'reject_reason' => $auth['reject_reason'] ?? '',
                    'create_time' => $auth['create_time'],
                    'review_time' => $auth['review_time'] ?? 0
                ];
            }

            return $this->success($authStatus);
        } catch (\Exception $e) {
            return $this->fail('获取认证状态失败：' . $e->getMessage());
        }
    }

    /**
     * 上传认证文件
     */
    public function uploadFile()
    {
        $file = $this->request->file('file');
        $fileType = $this->request->post('file_type', ''); // id_card, certificate, photo

        if (!$file) {
            return $this->fail('请选择要上传的文件');
        }

        if (!$fileType) {
            return $this->fail('请指定文件类型');
        }

        try {
            // 验证文件类型和大小
            $allowedTypes = ['jpg', 'jpeg', 'png', 'pdf'];
            $maxSize = 10 * 1024 * 1024; // 10MB

            if (!in_array(strtolower($file->getOriginalExtension()), $allowedTypes)) {
                return $this->fail('不支持的文件类型');
            }

            if ($file->getSize() > $maxSize) {
                return $this->fail('文件大小不能超过10MB');
            }

            // 生成文件名
            $fileName = date('Ymd') . '/' . md5(uniqid()) . '.' . $file->getOriginalExtension();
            $uploadPath = 'uploads/auth/' . $fileName;

            // 保存文件
            $file->move(public_path() . 'uploads/auth/', $fileName);

            // 记录文件信息
            $fileId = Db::name('user_auth_files')->insertGetId([
                'user_id' => $this->getUserId(),
                'file_type' => $fileType,
                'file_path' => $uploadPath,
                'file_name' => $file->getOriginalName(),
                'file_size' => $file->getSize(),
                'create_time' => time()
            ]);

            return $this->success([
                'file_id' => $fileId,
                'file_path' => $uploadPath,
                'file_url' => request()->domain() . '/' . $uploadPath
            ], '文件上传成功');
        } catch (\Exception $e) {
            return $this->fail('文件上传失败：' . $e->getMessage());
        }
    }
} 