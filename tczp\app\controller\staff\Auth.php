<?php
// +----------------------------------------------------------------------
// | 同城用工系统 - 师傅端认证管理
// +----------------------------------------------------------------------

namespace app\controller\staff;

use app\controller\BaseAuth;
use think\facade\Db;

/**
 * 认证管理控制器
 */
class Auth extends BaseAuth
{
    /**
     * 提交认证申请
     */
    public function submitAuth()
    {
        $authType = $this->request->post('auth_type', ''); // real_name, skill
        $authData = $this->request->post('auth_data', []);

        if (!$authType) {
            return $this->fail('请选择认证类型');
        }

        if (empty($authData)) {
            return $this->fail('请提交认证资料');
        }

        try {
            $userId = $this->getUserId();

            // 检查是否已有待审核的申请
            $existAuth = Db::name('user_auth')
                ->where([
                    'user_id' => $userId,
                    'auth_type' => $authType,
                    'status' => 'pending'
                ])
                ->find();

            if ($existAuth) {
                return $this->fail('您已提交过认证申请，请等待审核');
            }

            $authId = Db::name('user_auth')->insertGetId([
                'user_id' => $userId,
                'auth_type' => $authType,
                'auth_data' => json_encode($authData),
                'status' => 'pending',
                'create_time' => time()
            ]);

            return $this->success(['auth_id' => $authId], '认证申请提交成功，请等待审核');
        } catch (\Exception $e) {
            return $this->fail('提交认证失败：' . $e->getMessage());
        }
    }

    /**
     * 获取认证状态
     */
    public function getAuthStatus()
    {
        try {
            $userId = $this->getUserId();

            $authList = Db::name('user_auth')
                ->where('user_id', $userId)
                ->order('create_time desc')
                ->select()
                ->toArray();

            $authStatus = [];
            foreach ($authList as $auth) {
                $authStatus[$auth['auth_type']] = [
                    'status' => $auth['status'],
                    'auth_data' => json_decode($auth['auth_data'], true),
                    'reject_reason' => $auth['reject_reason'] ?? '',
                    'create_time' => $auth['create_time'],
                    'review_time' => $auth['review_time'] ?? 0
                ];
            }

            return $this->success($authStatus);
        } catch (\Exception $e) {
            return $this->fail('获取认证状态失败：' . $e->getMessage());
        }
    }

    /**
     * 上传认证文件
     */
    public function uploadFile()
    {
        $file = $this->request->file('file');
        $fileType = $this->request->post('file_type', ''); // id_card, certificate, photo

        if (!$file) {
            return $this->fail('请选择要上传的文件');
        }

        if (!$fileType) {
            return $this->fail('请指定文件类型');
        }

        try {
            // 验证文件类型和大小
            $allowedTypes = ['jpg', 'jpeg', 'png', 'pdf'];
            $maxSize = 10 * 1024 * 1024; // 10MB

            if (!in_array(strtolower($file->getOriginalExtension()), $allowedTypes)) {
                return $this->fail('不支持的文件类型');
            }

            if ($file->getSize() > $maxSize) {
                return $this->fail('文件大小不能超过10MB');
            }

            // 生成文件名
            $fileName = date('Ymd') . '/' . md5(uniqid()) . '.' . $file->getOriginalExtension();
            $uploadPath = 'uploads/auth/' . $fileName;

            // 保存文件
            $file->move(public_path() . 'uploads/auth/', $fileName);

            // 记录文件信息
            $fileId = Db::name('user_auth_files')->insertGetId([
                'user_id' => $this->getUserId(),
                'file_type' => $fileType,
                'file_path' => $uploadPath,
                'file_name' => $file->getOriginalName(),
                'file_size' => $file->getSize(),
                'create_time' => time()
            ]);

            return $this->success([
                'file_id' => $fileId,
                'file_path' => $uploadPath,
                'file_url' => request()->domain() . '/' . $uploadPath
            ], '文件上传成功');
        } catch (\Exception $e) {
            return $this->fail('文件上传失败：' . $e->getMessage());
        }
    }
} 