<?php
declare (strict_types = 1);

namespace app\controller\api;

use app\Request;
use app\services\location\LocationServices;
use think\annotation\Inject;

/**
 * 地理位置控制器
 * Class LocationController
 * @package app\controller\api\v1
 */
class LocationController
{
    /**
     * @var LocationServices
     */
    #[Inject]
    protected LocationServices $services;

    /**
     * 计算两点间距离
     * @param Request $request
     * @return mixed
     */
    public function calculateDistance(Request $request)
    {
        [$lat1, $lng1, $lat2, $lng2] = $request->postMore([
            ['lat1', 0],
            ['lng1', 0],
            ['lat2', 0],
            ['lng2', 0]
        ], true);

        if (!$lat1 || !$lng1 || !$lat2 || !$lng2) {
            return app('json')->fail('坐标参数不能为空');
        }

        $distance = $this->services->calculateDistance($lat1, $lng1, $lat2, $lng2);

        return app('json')->success([
            'distance' => $distance,
            'distance_text' => $this->services->formatDistance($distance)
        ]);
    }

    /**
     * 获取附近的任务
     * @param Request $request
     * @return mixed
     */
    public function getNearbyTasks(Request $request)
    {
        [$lat, $lng, $radius, $category_id, $page, $limit] = $request->getMore([
            ['lat', 0],
            ['lng', 0],
            ['radius', 5], // 默认5公里
            ['category_id', 0],
            ['page', 1],
            ['limit', 20]
        ], true);

        if (!$lat || !$lng) {
            return app('json')->fail('位置信息不能为空');
        }

        $result = $this->services->getNearbyTasks($lat, $lng, $radius, $category_id, $page, $limit);

        return app('json')->success($result);
    }

    /**
     * 地址解析（地址转坐标）
     * @param Request $request
     * @return mixed
     */
    public function geocoding(Request $request)
    {
        $address = $request->post('address', '');
        $city = $request->post('city', '');

        if (!$address) {
            return app('json')->fail('地址不能为空');
        }

        $result = $this->services->geocoding($address, $city);

        if ($result) {
            return app('json')->success($result);
        } else {
            return app('json')->fail('地址解析失败');
        }
    }

    /**
     * 逆地址解析（坐标转地址）
     * @param Request $request
     * @return mixed
     */
    public function reverseGeocoding(Request $request)
    {
        [$lat, $lng] = $request->postMore([
            ['lat', 0],
            ['lng', 0]
        ], true);

        if (!$lat || !$lng) {
            return app('json')->fail('坐标不能为空');
        }

        $result = $this->services->reverseGeocoding($lat, $lng);

        if ($result) {
            return app('json')->success($result);
        } else {
            return app('json')->fail('地址解析失败');
        }
    }

    /**
     * 获取附近的师傅
     * @param Request $request
     * @return mixed
     */
    public function getNearbyWorkers(Request $request)
    {
        [$lat, $lng, $radius, $skill_id, $page, $limit] = $request->getMore([
            ['lat', 0],
            ['lng', 0],
            ['radius', 10], // 默认10公里
            ['skill_id', 0],
            ['page', 1],
            ['limit', 20]
        ], true);

        if (!$lat || !$lng) {
            return app('json')->fail('位置信息不能为空');
        }

        $result = $this->services->getNearbyWorkers($lat, $lng, $radius, $skill_id, $page, $limit);

        return app('json')->success($result);
    }

    /**
     * 更新用户位置
     * @param Request $request
     * @return mixed
     */
    public function updateLocation(Request $request)
    {
        $uid = $request->uid();
        [$lat, $lng, $address] = $request->postMore([
            ['lat', 0],
            ['lng', 0],
            ['address', '']
        ], true);

        if (!$lat || !$lng) {
            return app('json')->fail('位置信息不能为空');
        }

        $result = $this->services->updateUserLocation($uid, $lat, $lng, $address);

        if ($result) {
            return app('json')->success([], '位置更新成功');
        } else {
            return app('json')->fail('位置更新失败');
        }
    }

    /**
     * 获取城市列表
     * @param Request $request
     * @return mixed
     */
    public function getCityList(Request $request)
    {
        $province = $request->get('province', '');

        $result = $this->services->getCityList($province);

        return app('json')->success($result);
    }

    /**
     * 路径规划
     * @param Request $request
     * @return mixed
     */
    public function getRoute(Request $request)
    {
        [$start_lat, $start_lng, $end_lat, $end_lng, $type] = $request->postMore([
            ['start_lat', 0],
            ['start_lng', 0],
            ['end_lat', 0],
            ['end_lng', 0],
            ['type', 'driving'] // driving, walking, transit
        ], true);

        if (!$start_lat || !$start_lng || !$end_lat || !$end_lng) {
            return app('json')->fail('起终点坐标不能为空');
        }

        $result = $this->services->getRoute($start_lat, $start_lng, $end_lat, $end_lng, $type);

        if ($result) {
            return app('json')->success($result);
        } else {
            return app('json')->fail('路径规划失败');
        }
    }

    /**
     * 获取热门区域
     * @param Request $request
     * @return mixed
     */
    public function getHotAreas(Request $request)
    {
        $city = $request->get('city', '');

        $result = $this->services->getHotAreas($city);

        return app('json')->success($result);
    }

    /**
     * 地理围栏检查
     * @param Request $request
     * @return mixed
     */
    public function checkGeofence(Request $request)
    {
        [$lat, $lng, $fence_id] = $request->postMore([
            ['lat', 0],
            ['lng', 0],
            ['fence_id', 0]
        ], true);

        if (!$lat || !$lng || !$fence_id) {
            return app('json')->fail('参数不能为空');
        }

        $result = $this->services->checkGeofence($lat, $lng, $fence_id);

        return app('json')->success([
            'in_fence' => $result,
            'message' => $result ? '在服务区域内' : '不在服务区域内'
        ]);
    }
}