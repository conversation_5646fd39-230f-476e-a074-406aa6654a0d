<?php
namespace app\listener\community;

use app\jobs\activity\StorePromotionsJob;
use app\jobs\community\CommunityJob;
use app\services\community\CommunityServices;
use app\services\order\StoreOrderInvoiceServices;
use crmeb\interfaces\ListenerInterface;

/**
 * 帖子操作事件
 * Class Cancel
 * @package app\listener\order
 */
class CommunityOperate implements ListenerInterface
{
    /**
     * 帖子事件
     * @param $event
     */
    public function handle($event): void
    {
        $id = $event[0] ?? 0;
        $type = $event[1] ?? 0;
        if ($id) {
            //用户发帖数数据矫正
            CommunityJob::dispatchDo('communityUserSync', [$id]);
            //话题帖子数矫正
            CommunityJob::dispatchDo('communityTopicSync', [$id]);
            if ($type == 1) {
                //新增帖子增加积分经验
                CommunityJob::dispatchDo('communityIncome', [$id]);
                //修正帖子话题
                CommunityJob::dispatchDo('communityTopicStatus', [$id]);
            }
        }
    }
}
