<?php
// +----------------------------------------------------------------------
// | 同城用工系统 - 师傅端设置管理
// +----------------------------------------------------------------------

namespace app\controller\staff;

use app\controller\BaseAuth;
use think\facade\Db;

/**
 * 设置管理控制器
 */
class Setting extends BaseAuth
{
    /**
     * 获取用户设置
     */
    public function getSetting()
    {
        try {
            $userId = $this->getUserId();

            $settings = Db::name('user_settings')
                ->where('user_id', $userId)
                ->find();

            if (!$settings) {
                // 创建默认设置
                $defaultSettings = [
                    'user_id' => $userId,
                    'notification_task' => 1,
                    'notification_system' => 1,
                    'notification_review' => 1,
                    'work_status' => 1,
                    'auto_accept_distance' => 5,
                    'preferred_work_time' => '09:00-18:00',
                    'create_time' => time(),
                    'update_time' => time()
                ];

                Db::name('user_settings')->insert($defaultSettings);
                $settings = $defaultSettings;
            }

            return $this->success($settings);
        } catch (\Exception $e) {
            return $this->fail('获取设置失败：' . $e->getMessage());
        }
    }

    /**
     * 更新用户设置
     */
    public function updateSetting()
    {
        $notificationTask = $this->request->post('notification_task');
        $notificationSystem = $this->request->post('notification_system');
        $notificationReview = $this->request->post('notification_review');
        $workStatus = $this->request->post('work_status');
        $autoAcceptDistance = $this->request->post('auto_accept_distance');
        $preferredWorkTime = $this->request->post('preferred_work_time', '');

        try {
            $userId = $this->getUserId();
            $updateData = ['update_time' => time()];

            if ($notificationTask !== null) {
                $updateData['notification_task'] = $notificationTask ? 1 : 0;
            }

            if ($notificationSystem !== null) {
                $updateData['notification_system'] = $notificationSystem ? 1 : 0;
            }

            if ($notificationReview !== null) {
                $updateData['notification_review'] = $notificationReview ? 1 : 0;
            }

            if ($workStatus !== null) {
                $updateData['work_status'] = $workStatus ? 1 : 0;
            }

            if ($autoAcceptDistance !== null) {
                $distance = intval($autoAcceptDistance);
                if ($distance >= 1 && $distance <= 50) {
                    $updateData['auto_accept_distance'] = $distance;
                }
            }

            if ($preferredWorkTime) {
                $updateData['preferred_work_time'] = $preferredWorkTime;
            }

            // 检查设置是否存在
            $existSetting = Db::name('user_settings')
                ->where('user_id', $userId)
                ->find();

            if ($existSetting) {
                // 更新设置
                Db::name('user_settings')
                    ->where('user_id', $userId)
                    ->update($updateData);
            } else {
                // 创建设置
                $updateData['user_id'] = $userId;
                $updateData['create_time'] = time();
                Db::name('user_settings')->insert($updateData);
            }

            return $this->success([], '设置更新成功');
        } catch (\Exception $e) {
            return $this->fail('更新设置失败：' . $e->getMessage());
        }
    }
} 