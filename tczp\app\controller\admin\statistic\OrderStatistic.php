<?php
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\admin\statistic;

use app\controller\admin\AuthController;
use app\services\statistic\OrderStatisticServices;
use think\annotation\Inject;

class OrderStatistic extends AuthController
{

    /**
     * @var OrderStatisticServices
     */
    #[Inject]
    protected OrderStatisticServices $services;

    /**
     * 订单统计基础信息
     * @return mixed
     */
    public function getBasic()
    {
        $where = $this->request->getMore([
            ['time', '']
        ]);
        if($this->supplierId > 0){
            $where['supplier_id'] =  $this->supplierId;
        }
        $data = $this->services->getBasic($where);
        return app('json')->success($data);
    }

    /**
     * 订单统计趋势图
     * @return mixed
     */
    public function getTrend()
    {
        $where = $this->request->getMore([
            ['time', '']
        ]);
        if($this->supplierId > 0){
            $where['supplier_id'] =  $this->supplierId;
        }
        $data = $this->services->getTrend($where);
        return app('json')->success($data);
    }

    /**
     * 订单来源
     * @return mixed
     */
    public function getChannel()
    {
        $where = $this->request->getMore([
            ['time', '']
        ]);
        if($this->supplierId > 0){
            $where['supplier_id'] =  $this->supplierId;
        }
        $data = $this->services->getChannel($where);
        return app('json')->success($data);
    }

    /**
     * 订单类型
     * @return mixed
     */
    public function getType()
    {
        $where = $this->request->getMore([
            ['time', '']
        ]);
        if($this->supplierId > 0){
            $where['supplier_id'] =  $this->supplierId;
        }
        $data = $this->services->getType($where);
        return app('json')->success($data);
    }
}
