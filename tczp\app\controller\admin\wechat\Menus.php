<?php
namespace app\controller\admin\wechat;


use app\controller\admin\AuthController;
use app\services\wechat\WechatMenuServices;
use think\annotation\Inject;

/**
 * 微信菜单  控制器
 * Class Menus
 * @package app\admin\controller\wechat
 */
class Menus extends AuthController
{

    /**
     * @var WechatMenuServices
     */
    #[Inject]
    protected WechatMenuServices $services;

    /**
     * 获取菜单
     * @return mixed
     */
    public function index()
    {
        $menus = $this->services->getWechatMenu();
        return $this->success(compact('menus'));
    }

    /**
     * 保存菜单
     * @return mixed
     */
    public function save()
    {
        $buttons = request()->post('button/a', []);
        if (!count($buttons)) return $this->fail('请添加至少一个按钮');
        $this->services->saveMenu($buttons);
        return $this->success('修改成功!');
    }
}
