<?php

namespace app\model\user\member;


use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;

/**
 * Class MemberRight
 * @package app\model\user\member
 */
class MemberRight extends BaseModel
{
    use ModelTrait;

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'member_right';

    /**
     * 状态搜索器
     * @param $query
     * @param $value
     */
    public function searchStatusAttr($query, $value)
    {
        if ($value) {
            $query->where('status', $value);
        }
    }

    /**
     * 状态搜索器
     * @param $query
     * @param $value
     */
    public function searchRightTypeAttr($query, $value)
    {
        if ($value) {
            $query->where('right_type', $value);
        }
    }
}
