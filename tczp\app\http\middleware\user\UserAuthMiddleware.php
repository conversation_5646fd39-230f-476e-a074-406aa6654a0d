<?php
declare(strict_types=1);

namespace app\http\middleware\user;

use app\Request;
use app\services\user\UserAuthServices;
use app\services\user\EnterpriseAuthServices;
use crmeb\interfaces\MiddlewareInterface;
use think\facade\Config;
use think\Response;

/**
 * 用户端统一认证中间件
 * Class UserAuthMiddleware
 * @package app\http\middleware\user
 */
class UserAuthMiddleware implements MiddlewareInterface
{
    /**
     * 处理请求
     * @param Request $request
     * @param \Closure $next
     * @param bool $force 是否强制验证
     * @return Response
     */
    public function handle(Request $request, \Closure $next, bool $force = true)
    {
        try {
            // 获取Token
            $token = $this->getToken($request);
            
            if (!$token && $force) {
                return $this->unauthorizedResponse('缺少认证Token');
            }
            
            if ($token) {
                // 解析Token获取用户信息
                $userInfo = $this->parseToken($token);
                
                // 验证用户状态
                $this->validateUserStatus($userInfo);
                
                // 设置请求上下文
                $this->setRequestContext($request, $userInfo);
            }
            
            return $next($request);
            
        } catch (\Exception $e) {
            if ($force) {
                return $this->unauthorizedResponse($e->getMessage());
            }
            return $next($request);
        }
    }
    
    /**
     * 获取Token
     * @param Request $request
     * @return string|null
     */
    private function getToken(Request $request): ?string
    {
        $tokenName = Config::get('cookie.token_name', 'Authorization');
        $token = trim(ltrim($request->header($tokenName), 'Bearer'));
        
        // 兼容处理
        if (!$token) {
            $token = trim(ltrim($request->header('Authori-zation'), 'Bearer'));
        }
        
        return $token ?: null;
    }
    
    /**
     * 解析Token
     * @param string $token
     * @return array
     * @throws \Exception
     */
    private function parseToken(string $token): array
    {
        /** @var UserAuthServices $userAuthService */
        $userAuthService = app()->make(UserAuthServices::class);
        
        $userInfo = $userAuthService->parseToken($token);
        
        if (!$userInfo) {
            throw new \Exception('Token无效或已过期');
        }
        
        return $userInfo;
    }
    
    /**
     * 验证用户状态
     * @param array $userInfo
     * @throws \Exception
     */
    private function validateUserStatus(array $userInfo): void
    {
        // 检查账户状态
        if ($userInfo['status'] != 1) {
            throw new \Exception('账户已被禁用');
        }
        
        // 检查用户类型（如果需要）
        // 这里可以根据业务需求添加更多验证
    }
    
    /**
     * 设置请求上下文
     * @param Request $request
     * @param array $userInfo
     */
    private function setRequestContext(Request $request, array $userInfo): void
    {
        // 设置用户ID
        $request->uid = (int)$userInfo['uid'];
        
        // 设置用户信息
        $request->userInfo = $userInfo;
        
        // 设置用户类型
        $request->userType = $userInfo['user_type'] ?? 'customer';
        
        // 如果是企业用户，设置企业信息
        /** @var EnterpriseAuthServices $enterpriseAuthService */
        $enterpriseAuthService = app()->make(EnterpriseAuthServices::class);
        
        if ($enterpriseAuthService->isEnterprise($userInfo['uid'])) {
            $enterpriseInfo = $enterpriseAuthService->getEnterpriseInfo($userInfo['uid']);
            $request->enterpriseInfo = $enterpriseInfo;
            $request->isEnterprise = true;
            
            // 设置企业权限
            $permissions = $enterpriseAuthService->getEnterprisePermissions($userInfo['uid']);
            $request->permissions = $permissions;
        } else {
            $request->isEnterprise = false;
            // 设置普通用户权限
            $request->permissions = $this->getUserPermissions($userInfo['uid']);
        }
    }
    
    /**
     * 获取用户权限
     * @param int $uid
     * @return array
     */
    private function getUserPermissions(int $uid): array
    {
        // 获取用户角色权限
        $rolePermissions = \think\facade\Db::name('user_role_relations')
            ->alias('urr')
            ->join('user_roles ur', 'urr.role_id = ur.id')
            ->where('urr.user_id', $uid)
            ->where('urr.status', 1)
            ->column('ur.permissions');
            
        $permissions = [];
        foreach ($rolePermissions as $rolePermission) {
            $perms = json_decode($rolePermission, true) ?: [];
            $permissions = array_merge($permissions, $perms);
        }
        
        return array_unique($permissions);
    }
    
    /**
     * 返回未授权响应
     * @param string $message
     * @return Response
     */
    private function unauthorizedResponse(string $message): Response
    {
        return app('json')->make(401, $message);
    }
}
