<?php

namespace app\controller\kefu;


use app\Request;
use crmeb\basic\BaseController;
use crmeb\services\CacheService;
use app\services\kefu\LoginServices;
use app\validate\kefu\LoginValidate;
use Psr\SimpleCache\InvalidArgumentException;
use think\annotation\Inject;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

/**
 * Class Login
 * @package app\kefu\controller
 */
class Login extends BaseController
{

    /**
     * @var LoginServices
     */
    #[Inject]
    protected LoginServices $services;

    /**
     * 客服登录
     * @param Request $request
     * @return mixed
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function login(Request $request)
    {
        [$account, $password] = $request->postMore([
            ['account', ''],
            ['password', ''],
        ], true);

        $this->validate(['account' => $account, 'password' => $password], LoginValidate::class);

        $token = $this->services->authLogin($account, $password);

        return $this->success('登录成功', $token);
    }

    /**
     * 开放平台扫码登录
     * @return mixed
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function wechatAuth()
    {
        return $this->success($this->services->wechatAuth());
    }

    /**
     * 获取公众平台id
     * @return mixed
     */
    public function getAppid()
    {
        return $this->success([
            'appid' => sys_config('wechat_open_app_id', 'wxc736972a4ca1e2a1'),
            'version' => get_crmeb_version(),
            'site_name' => sys_config('site_name')
        ]);
    }

    /**
     * 获取登录唯一code
     * @return mixed
     */
    public function getLoginKey()
    {
        $key = md5(time() . uniqid());
        $time = time() + 600;
        CacheService::set($key, 1, 600);
        return $this->success(['key' => $key, 'time' => $time]);
    }

    /**
     * 验证登录
     * @param string $key
     * @return mixed
     * @throws InvalidArgumentException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function scanLogin(string $key)
    {
        return $this->success($this->services->scanLogin($key));
    }
}
