<?php
declare (strict_types=1);

namespace app\dao\message;

use app\dao\BaseDao;
use app\model\message\SystemMessage;

/**
 * 站内信
 * Class SystemMessageDao
 * @package app\dao\message
 */
class SystemMessageDao extends BaseDao
{

    /**
     * 设置模型
     * @return string
     */
    protected function setModel(): string
    {
        return SystemMessage::class;
    }

    /**
     * @param array $where
     * @param string $field
     * @param int $page
     * @param int $limit
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getMessageList(array $where, string $field = '*', int $page = 0, int $limit = 0)
    {
        return $this->getModel()->where($where)->field($field)
			->when($page && $limit, function ($query) use ($page, $limit) {
				$query->page($page, $limit);
			})->when(!$page && $limit, function ($query) use ($limit) {
				$query->limit($limit);
			})->order('add_time desc')->select()->toArray();
    }
}
