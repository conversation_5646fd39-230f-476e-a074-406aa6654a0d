<?php
namespace app\services\message;


use app\dao\message\TemplateMessageDao;
use app\services\BaseServices;
use think\annotation\Inject;

/**
 * 模板消息
 * Class TemplateMessageServices
 * @package app\services\other
 * @mixin TemplateMessageDao
 */
class TemplateMessageServices extends BaseServices
{
    /**
     * @var TemplateMessageDao
     */
    #[Inject]
    protected TemplateMessageDao $dao;

    /**
     * 获取模板消息列表
     * @param array $where
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getTemplateList(array $where)
    {
        [$page, $limit] = $this->getPageValue();
        $list = $this->dao->getTemplateList($where, $page, $limit);
        foreach ($list as &$item) {
            if ($item['content']) $item['content'] = explode("\n", $item['content']);
        }
        $count = $this->dao->count($where);
        return compact('list', 'count');
    }

    /**
     * 获取模板消息id
     * @param string $templateId
     * @param int $type
     * @return mixed
     */
    public function getTempId(string $templateId, int $type = 0)
    {
        return $this->dao->value(['type' => $type, 'tempkey' => $templateId, 'status' => 1], 'tempid');
    }
}
