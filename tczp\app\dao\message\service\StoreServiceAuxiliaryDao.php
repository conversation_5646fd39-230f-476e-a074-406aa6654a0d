<?php

namespace app\dao\message\service;


use app\dao\other\queue\QueueAuxiliaryDao;
use crmeb\basic\BaseModel;

/**
 * 客服辅助表
 * Class StoreServiceAuxiliaryDao
 * @package app\dao\message\service
 */
class StoreServiceAuxiliaryDao extends QueueAuxiliaryDao
{

    /**
     * 搜索
     * @param array $where
     * @param bool $search
     * @return BaseModel
     * @throws \ReflectionException
     */
    public function search(array $where = [], bool $search = true)
    {
        return parent::search($where, $search)->where('type', 0);
    }

}

