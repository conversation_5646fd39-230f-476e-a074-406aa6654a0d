<?php
declare(strict_types=1);

namespace app\services\staff;

use app\services\BaseServices;
use think\facade\Db;
use think\facade\Cache;

/**
 * 师傅任务管理服务类
 * Class StaffTaskServices
 * @package app\services\staff
 */
class StaffTaskServices extends BaseServices
{
    /**
     * 获取任务列表
     * @param array $where
     * @param int $page
     * @param int $limit
     * @return array
     */
    public function getTaskList(array $where = [], int $page = 1, int $limit = 20): array
    {
        $query = Db::name('tasks')
            ->alias('t')
            ->join('user u', 't.user_id = u.uid')
            ->join('task_categories tc', 't.task_category_id = tc.id', 'left')
            ->where('t.status', 'published');
            
        // 分类筛选
        if (!empty($where['category_id'])) {
            $query->where('t.task_category_id', $where['category_id']);
        }
        
        // 地区筛选
        if (!empty($where['city'])) {
            $query->where('t.task_location', 'like', "%{$where['city']}%");
        }
        
        // 价格筛选
        if (!empty($where['min_price'])) {
            $query->where('t.hourly_rate', '>=', $where['min_price']);
        }
        if (!empty($where['max_price'])) {
            $query->where('t.hourly_rate', '<=', $where['max_price']);
        }
        
        // 难度筛选
        if (!empty($where['difficulty_level'])) {
            $query->where('t.difficulty_level', $where['difficulty_level']);
        }
        
        // 关键词搜索
        if (!empty($where['keyword'])) {
            $query->where(function ($q) use ($where) {
                $q->where('t.task_title', 'like', "%{$where['keyword']}%")
                  ->whereOr('t.task_description', 'like', "%{$where['keyword']}%");
            });
        }
        
        $total = $query->count();
        $list = $query->field([
                't.id', 't.task_title', 't.task_description', 't.task_location',
                't.hourly_rate', 't.required_workers', 't.difficulty_level',
                't.estimated_duration', 't.required_skills', 't.worker_requirements',
                't.create_time', 't.deadline',
                'u.nickname as publisher_name', 'u.avatar as publisher_avatar',
                'tc.category_name'
            ])
            ->order('t.create_time desc')
            ->page($page, $limit)
            ->select()
            ->toArray();
            
        // 处理技能要求
        foreach ($list as &$item) {
            $item['required_skills'] = json_decode($item['required_skills'] ?: '[]', true);
            $item['worker_requirements'] = json_decode($item['worker_requirements'] ?: '{}', true);
            $item['create_time_text'] = date('Y-m-d H:i', $item['create_time']);
            $item['deadline_text'] = $item['deadline'] ? date('Y-m-d H:i', $item['deadline']) : '';
        }
            
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    }
    
    /**
     * 获取任务详情
     * @param int $taskId
     * @return array|null
     */
    public function getTaskDetail(int $taskId): ?array
    {
        $task = Db::name('tasks')
            ->alias('t')
            ->join('user u', 't.user_id = u.uid')
            ->join('task_categories tc', 't.task_category_id = tc.id', 'left')
            ->where('t.id', $taskId)
            ->field([
                't.*',
                'u.nickname as publisher_name', 'u.avatar as publisher_avatar',
                'u.phone as publisher_phone',
                'tc.category_name'
            ])
            ->find();
            
        if (!$task) {
            return null;
        }
        
        // 处理JSON字段
        $task['required_skills'] = json_decode($task['required_skills'] ?: '[]', true);
        $task['worker_requirements'] = json_decode($task['worker_requirements'] ?: '{}', true);
        
        // 获取技能名称
        if (!empty($task['required_skills'])) {
            $skillNames = Db::name('skills_new')
                ->where('id', 'in', $task['required_skills'])
                ->column('skill_name');
            $task['required_skill_names'] = $skillNames;
        } else {
            $task['required_skill_names'] = [];
        }
        
        // 获取申请统计
        $applicationStats = Db::name('task_applications')
            ->where('task_id', $taskId)
            ->field([
                'COUNT(*) as total_applications',
                'SUM(CASE WHEN status = "pending" THEN 1 ELSE 0 END) as pending_count',
                'SUM(CASE WHEN status = "approved" THEN 1 ELSE 0 END) as approved_count'
            ])
            ->find();
            
        $task['application_stats'] = $applicationStats ?: [
            'total_applications' => 0,
            'pending_count' => 0,
            'approved_count' => 0
        ];
        
        return $task;
    }
    
    /**
     * 申请任务
     * @param int $taskId
     * @param int $userId
     * @param array $data
     * @return bool
     */
    public function applyTask(int $taskId, int $userId, array $data): bool
    {
        // 验证必填字段
        $required = ['quoted_price'];
        foreach ($required as $field) {
            if (!isset($data[$field]) || $data[$field] === '') {
                throw new \Exception("字段 {$field} 不能为空");
            }
        }
        
        // 检查任务是否存在且可申请
        $task = Db::name('tasks')->where('id', $taskId)->find();
        if (!$task) {
            throw new \Exception('任务不存在');
        }
        
        if ($task['status'] !== 'published') {
            throw new \Exception('任务状态不允许申请');
        }
        
        if ($task['user_id'] == $userId) {
            throw new \Exception('不能申请自己发布的任务');
        }
        
        // 检查是否已申请过
        $hasApplied = Db::name('task_applications')
            ->where('task_id', $taskId)
            ->where('user_id', $userId)
            ->count() > 0;
            
        if ($hasApplied) {
            throw new \Exception('已申请过该任务');
        }
        
        // 计算技能匹配度
        $skillMatchScore = $this->calculateSkillMatchScore($taskId, $userId);
        
        // 计算距离（这里简化处理，实际应该根据地理位置计算）
        $distance = 0;
        
        try {
            Db::startTrans();
            
            // 创建申请记录
            $applicationData = [
                'task_id' => $taskId,
                'user_id' => $userId,
                'status' => 'pending',
                'quoted_price' => $data['quoted_price'],
                'skill_match_score' => $skillMatchScore,
                'distance' => $distance,
                'application_note' => $data['application_note'] ?? '',
                'estimated_start_time' => $data['estimated_start_time'] ?? 0,
                'estimated_completion_time' => $data['estimated_completion_time'] ?? 0,
                'create_time' => time(),
            ];
            
            $result = Db::name('task_applications')->insert($applicationData);
            
            if ($result) {
                // 更新任务申请数量
                Db::name('tasks')
                    ->where('id', $taskId)
                    ->inc('application_count', 1);
            }
            
            Db::commit();
            return $result;
            
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 获取我的任务申请
     * @param int $userId
     * @param array $where
     * @param int $page
     * @param int $limit
     * @return array
     */
    public function getMyApplications(int $userId, array $where = [], int $page = 1, int $limit = 20): array
    {
        $query = Db::name('task_applications')
            ->alias('ta')
            ->join('tasks t', 'ta.task_id = t.id')
            ->join('user u', 't.user_id = u.uid')
            ->where('ta.user_id', $userId);
            
        // 状态筛选
        if (!empty($where['status'])) {
            $query->where('ta.status', $where['status']);
        }
        
        $total = $query->count();
        $list = $query->field([
                'ta.id', 'ta.status', 'ta.quoted_price', 'ta.application_note',
                'ta.skill_match_score', 'ta.create_time',
                't.id as task_id', 't.task_title', 't.task_location',
                't.hourly_rate', 't.status as task_status',
                'u.nickname as publisher_name'
            ])
            ->order('ta.create_time desc')
            ->page($page, $limit)
            ->select()
            ->toArray();
            
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    }
    
    /**
     * 获取我的任务
     * @param int $userId
     * @param array $where
     * @param int $page
     * @param int $limit
     * @return array
     */
    public function getMyTasks(int $userId, array $where = [], int $page = 1, int $limit = 20): array
    {
        $query = Db::name('task_applications')
            ->alias('ta')
            ->join('tasks t', 'ta.task_id = t.id')
            ->join('user u', 't.user_id = u.uid')
            ->where('ta.user_id', $userId)
            ->where('ta.status', 'approved');
            
        // 任务状态筛选
        if (!empty($where['task_status'])) {
            $query->where('t.status', $where['task_status']);
        }
        
        $total = $query->count();
        $list = $query->field([
                'ta.id as application_id', 'ta.quoted_price',
                't.id as task_id', 't.task_title', 't.task_description',
                't.task_location', 't.status as task_status', 't.create_time',
                't.start_time', 't.end_time', 't.deadline',
                'u.nickname as publisher_name', 'u.phone as publisher_phone'
            ])
            ->order('t.create_time desc')
            ->page($page, $limit)
            ->select()
            ->toArray();
            
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    }
    
    /**
     * 计算技能匹配度
     * @param int $taskId
     * @param int $userId
     * @return float
     */
    private function calculateSkillMatchScore(int $taskId, int $userId): float
    {
        // 获取任务需要的技能
        $task = Db::name('tasks')->where('id', $taskId)->find();
        $requiredSkills = json_decode($task['required_skills'] ?: '[]', true);
        
        if (empty($requiredSkills)) {
            return 100.0; // 没有技能要求，匹配度100%
        }
        
        // 获取师傅的技能
        $workerProfile = Db::name('worker_profiles')->where('user_id', $userId)->find();
        if (!$workerProfile) {
            return 0.0;
        }
        
        $workerSkills = Db::name('worker_skills_new')
            ->where('worker_id', $workerProfile['id'])
            ->where('auth_status', 'approved')
            ->column('skill_id');
            
        if (empty($workerSkills)) {
            return 0.0;
        }
        
        // 计算匹配度
        $matchedSkills = array_intersect($requiredSkills, $workerSkills);
        $matchScore = (count($matchedSkills) / count($requiredSkills)) * 100;
        
        return round($matchScore, 2);
    }
}
