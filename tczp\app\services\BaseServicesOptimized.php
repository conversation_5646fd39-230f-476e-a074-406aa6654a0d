<?php
declare(strict_types=1);

namespace app\services;

use think\facade\Db;
use think\facade\Cache;
use think\facade\Log;
use think\facade\Event;
use think\facade\Config;

/**
 * 统一基础服务类 - TCZP服务端优化版
 * 提供三端统一的服务基础功能
 * Class BaseServicesOptimized
 * @package app\services
 */
abstract class BaseServicesOptimized
{
    /**
     * 表名
     * @var string
     */
    protected $tableName = '';
    
    /**
     * 缓存前缀
     * @var string
     */
    protected $cachePrefix = '';
    
    /**
     * 缓存过期时间
     * @var int
     */
    protected $cacheExpire = 3600;
    
    /**
     * 是否启用缓存
     * @var bool
     */
    protected $enableCache = true;
    
    /**
     * 是否启用软删除
     * @var bool
     */
    protected $softDelete = false;
    
    /**
     * 软删除字段
     * @var string
     */
    protected $deleteField = 'is_del';

    /**
     * 获取分页配置
     * @param bool $isPage
     * @param bool $isRelieve
     * @return array
     */
    public function getPageValue(bool $isPage = true, bool $isRelieve = true): array
    {
        $page = $limit = 0;
        if ($isPage) {
            $page = app()->request->param(Config::get('database.page.pageKey', 'page'), 1);
            $limit = app()->request->param(Config::get('database.page.limitKey', 'limit'), 20);
        }
        $limitMax = Config::get('database.page.limitMax', 100);
        $defaultLimit = Config::get('database.page.defaultLimit', 20);
        
        if ($limit > $limitMax && $isRelieve) {
            $limit = $limitMax;
        }
        
        return [(int)$page, (int)$limit, (int)$defaultLimit];
    }

    /**
     * 数据库事务操作
     * @param callable $closure
     * @param bool $isTran
     * @return mixed
     */
    public function transaction(callable $closure, bool $isTran = true)
    {
        return $isTran ? Db::transaction($closure) : $closure();
    }

    /**
     * 获取数据库查询对象
     * @param string $table
     * @return \think\db\Query
     */
    protected function getQuery(string $table = ''): \think\db\Query
    {
        $tableName = $table ?: $this->tableName;
        $query = Db::name($tableName);
        
        // 如果启用软删除，自动添加条件
        if ($this->softDelete) {
            $query->where($this->deleteField, 0);
        }
        
        return $query;
    }

    /**
     * 根据ID获取数据
     * @param int $id
     * @param string $field
     * @param bool $useCache
     * @return array|null
     */
    public function getById(int $id, string $field = '*', bool $useCache = true): ?array
    {
        if ($useCache && $this->enableCache) {
            $cacheKey = $this->getCacheKey("id_{$id}");
            return Cache::remember($cacheKey, function () use ($id, $field) {
                return $this->getQuery()->where('id', $id)->field($field)->find();
            }, $this->cacheExpire);
        }
        
        return $this->getQuery()->where('id', $id)->field($field)->find();
    }

    /**
     * 根据条件获取单条数据
     * @param array $where
     * @param string $field
     * @param bool $useCache
     * @return array|null
     */
    public function getOne(array $where, string $field = '*', bool $useCache = false): ?array
    {
        $query = $this->getQuery()->where($where)->field($field);
        
        if ($useCache && $this->enableCache) {
            $cacheKey = $this->getCacheKey('one_' . md5(serialize($where)));
            return Cache::remember($cacheKey, function () use ($query) {
                return $query->find();
            }, $this->cacheExpire);
        }
        
        return $query->find();
    }

    /**
     * 获取列表数据
     * @param array $where
     * @param string $field
     * @param string $order
     * @param int $page
     * @param int $limit
     * @return array
     */
    public function getList(array $where = [], string $field = '*', string $order = '', int $page = 0, int $limit = 0): array
    {
        $query = $this->getQuery()->where($where)->field($field);
        
        if ($order) {
            $query->order($order);
        }
        
        if ($page > 0 && $limit > 0) {
            $query->page($page, $limit);
        }
        
        return $query->select()->toArray();
    }

    /**
     * 获取分页数据
     * @param array $where
     * @param string $field
     * @param string $order
     * @param int $page
     * @param int $limit
     * @return array
     */
    public function getPageList(array $where = [], string $field = '*', string $order = '', int $page = 1, int $limit = 20): array
    {
        $query = $this->getQuery()->where($where);
        
        $total = $query->count();
        
        $list = $query->field($field)
            ->order($order ?: 'id desc')
            ->page($page, $limit)
            ->select()
            ->toArray();
            
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    }

    /**
     * 添加数据
     * @param array $data
     * @return int
     */
    public function create(array $data): int
    {
        $data['create_time'] = $data['create_time'] ?? time();
        $data['update_time'] = $data['update_time'] ?? time();
        
        $id = $this->getQuery()->insertGetId($data);
        
        // 清除相关缓存
        $this->clearCache();
        
        // 触发事件
        Event::trigger('after_create', [$this->tableName, $id, $data]);
        
        return $id;
    }

    /**
     * 更新数据
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function update(int $id, array $data): bool
    {
        $data['update_time'] = $data['update_time'] ?? time();
        
        $result = $this->getQuery()->where('id', $id)->update($data);
        
        if ($result) {
            // 清除相关缓存
            $this->clearCache();
            $this->clearCacheById($id);
            
            // 触发事件
            Event::trigger('after_update', [$this->tableName, $id, $data]);
        }
        
        return $result > 0;
    }

    /**
     * 删除数据
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool
    {
        if ($this->softDelete) {
            $result = $this->update($id, [$this->deleteField => 1]);
        } else {
            $result = $this->getQuery()->where('id', $id)->delete() > 0;
        }
        
        if ($result) {
            // 清除相关缓存
            $this->clearCache();
            $this->clearCacheById($id);
            
            // 触发事件
            Event::trigger('after_delete', [$this->tableName, $id]);
        }
        
        return $result;
    }

    /**
     * 批量删除
     * @param array $ids
     * @return bool
     */
    public function batchDelete(array $ids): bool
    {
        if (empty($ids)) {
            return false;
        }
        
        if ($this->softDelete) {
            $result = $this->getQuery()->where('id', 'in', $ids)->update([$this->deleteField => 1]) > 0;
        } else {
            $result = $this->getQuery()->where('id', 'in', $ids)->delete() > 0;
        }
        
        if ($result) {
            // 清除相关缓存
            $this->clearCache();
            foreach ($ids as $id) {
                $this->clearCacheById($id);
            }
            
            // 触发事件
            Event::trigger('after_batch_delete', [$this->tableName, $ids]);
        }
        
        return $result;
    }

    /**
     * 获取缓存键
     * @param string $key
     * @return string
     */
    protected function getCacheKey(string $key): string
    {
        return $this->cachePrefix . $this->tableName . '_' . $key;
    }

    /**
     * 清除所有相关缓存
     */
    protected function clearCache(): void
    {
        if ($this->enableCache && $this->cachePrefix) {
            Cache::tag($this->cachePrefix . $this->tableName)->clear();
        }
    }

    /**
     * 清除指定ID的缓存
     * @param int $id
     */
    protected function clearCacheById(int $id): void
    {
        if ($this->enableCache) {
            $cacheKey = $this->getCacheKey("id_{$id}");
            Cache::delete($cacheKey);
        }
    }

    /**
     * 记录日志
     * @param string $message
     * @param array $context
     * @param string $level
     */
    protected function log(string $message, array $context = [], string $level = 'info'): void
    {
        Log::record($message, $level, array_merge($context, [
            'service' => static::class,
            'table' => $this->tableName
        ]));
    }

    /**
     * 触发事件
     * @param string $event
     * @param array $data
     */
    protected function trigger(string $event, array $data = []): void
    {
        Event::trigger($event, array_merge($data, [
            'service' => static::class,
            'table' => $this->tableName
        ]));
    }

    /**
     * 验证数据
     * @param array $data
     * @param array $rules
     * @param array $messages
     * @return bool
     * @throws \Exception
     */
    protected function validate(array $data, array $rules, array $messages = []): bool
    {
        $validate = new \think\Validate($rules, $messages);
        
        if (!$validate->check($data)) {
            throw new \Exception($validate->getError());
        }
        
        return true;
    }
}
