<?php

namespace app\controller\admin\finance;

use app\controller\admin\AuthController;
use app\services\user\UserBillServices;
use app\services\user\UserBrokerageServices;
use app\services\user\UserMoneyServices;
use think\annotation\Inject;

/**
 * Class Finance
 * @package app\controller\admin\v1\finance
 */
class Finance extends AuthController
{

    /**
     * @var UserBillServices
     */
    #[Inject]
    protected UserBillServices $services;

    /**
     * 筛选类型
     */
    public function bill_type(UserMoneyServices $services)
    {
        return $this->success($services->bill_type());
    }

    /**
     * 资金记录
     */
    public function list(UserMoneyServices $services)
    {
        $where = $this->request->getMore([
            ['start_time', ''],
            ['end_time', ''],
            ['nickname', ''],
            ['limit', 20],
            ['page', 1],
            ['type', ''],
        ]);
        return $this->success($services->getMoneyList($where));
    }

    /**
     * 用户佣金记录（用户列表）
     * @return mixed
     */
    public function get_commission_list(UserBrokerageServices $services)
    {
        $where = $this->request->getMore([
            ['nickname', ''],
            ['price_max', ''],
            ['price_min', ''],
            ['sum_number', 'normal'],
            ['brokerage_price', 'normal'],
            ['date', '', '', 'time']
        ]);
        return $this->success($services->getCommissionList($where));
    }

    /**
     * 佣金详情用户信息
     * @param $id
     * @return mixed
     */
    public function user_info(UserBrokerageServices $services, $id)
    {
        return $this->success($services->user_info((int)$id));
    }

    /**
     * 获取用户佣金列表
     * @param UserBrokerageServices $services
     * @param string $id
     * @return mixed
     */
    public function getUserBrokeragelist(UserBrokerageServices $services, $id = '')
    {
        if ($id == '') return $this->fail('缺少参数');
        $where = $this->request->getMore([
            ['start_time', ''],
            ['end_time', ''],
            ['nickname', '']
        ]);
        $where['uid'] = (int)$id;
        return $this->success($services->getBrokerageList($where));
    }

}
