<?php

namespace app\listener\user;


use app\services\work\WorkClientServices;
use crmeb\interfaces\ListenerInterface;

/**
 * 用户绑定客户
 * Class UserBindClient
 * @package app\listener\user
 */
class UserBindClient implements ListenerInterface
{

    public function handle($event): void
    {
        [$uid, $unionid] = $event;

        try {
            /** @var WorkClientServices $make */
            $make = app()->make(WorkClientServices::class);
            $clientInfo = $make->get(['unionid' => $unionid], ['id', 'unionid', 'uid']);
            if ($clientInfo) {
                $clientInfo->uid = $uid;
                $clientInfo->save();
            }
        } catch (\Throwable $e) {
            \think\facade\Log::error(json_encode([
                'error' => '用户绑定客户失败:' . $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]));
        }
    }
}
