<?php
namespace app\controller\admin\wechat;



use app\controller\admin\AuthController;

/**
 * 用户扫码点击事件
 * Class SystemMessage
 * @package app\admin\controller\system
 */
class WechatMessage extends AuthController
{
    /**
     * 显示操作记录
     */
    public function index()
    {
        $where = $this->getMore([
            ['page', 1],
            ['limit', 20],
            ['nickname', ''],
            ['type', ''],
            ['data', ''],
        ]);
        return $this->success([]);
    }

    /**
     * 操作名称列表
     * @return mixed
     */
    public function operate()
    {

        return $this->success([]);
    }

}

