<?php

namespace app\listener\user;

use app\jobs\activity\StoreCouponJob;
use app\jobs\user\UserIntegralJob;
use app\jobs\user\UserMoneyJob;
use crmeb\interfaces\ListenerInterface;


/**
 * 激活用户会员卡事件
 * Class ActivateLevel
 * @package app\listener\user
 */
class ActivateLevel implements ListenerInterface
{
    /**
	* @param $event
	* @return void
	 */
    public function handle($event): void
    {
        [$uid] = $event;

		//会员卡激活赠送积分
		UserIntegralJob::dispatchDo('levelGiveIntegral', [$uid]);
		//会员卡激活赠送余额
		UserMoneyJob::dispatchDo('levelGiveMoney', [$uid]);
		//会员卡激活赠送优惠券
		StoreCouponJob::dispatchDo('levelGiveCoupon', [$uid]);
    }
}
