<?php
/**
 * TCZP服务端优化脚本
 * 用于服务端启动时的优化和检查
 */

declare(strict_types=1);

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', '1');

// 定义根目录
define('ROOT_PATH', dirname(__DIR__) . '/');

echo "==================== TCZP服务端优化脚本 ====================\n";
echo "开始执行服务端优化...\n\n";

/**
 * 检查PHP环境
 */
function checkPhpEnvironment(): bool
{
    echo "1. 检查PHP环境...\n";
    
    // 检查PHP版本
    $phpVersion = PHP_VERSION;
    $minVersion = '7.4.0';
    
    if (version_compare($phpVersion, $minVersion, '<')) {
        echo "   ❌ PHP版本过低: {$phpVersion}，最低要求: {$minVersion}\n";
        return false;
    }
    echo "   ✅ PHP版本: {$phpVersion}\n";
    
    // 检查必需扩展
    $requiredExtensions = [
        'pdo', 'pdo_mysql', 'json', 'mbstring', 'openssl', 
        'curl', 'gd', 'fileinfo', 'redis'
    ];
    
    $missingExtensions = [];
    foreach ($requiredExtensions as $ext) {
        if (!extension_loaded($ext)) {
            $missingExtensions[] = $ext;
        }
    }
    
    if (!empty($missingExtensions)) {
        echo "   ❌ 缺少PHP扩展: " . implode(', ', $missingExtensions) . "\n";
        return false;
    }
    echo "   ✅ 所有必需的PHP扩展已安装\n";
    
    // 检查内存限制
    $memoryLimit = ini_get('memory_limit');
    echo "   ✅ 内存限制: {$memoryLimit}\n";
    
    // 检查上传限制
    $uploadMaxFilesize = ini_get('upload_max_filesize');
    $postMaxSize = ini_get('post_max_size');
    echo "   ✅ 上传限制: {$uploadMaxFilesize}, POST限制: {$postMaxSize}\n";
    
    return true;
}

/**
 * 检查目录权限
 */
function checkDirectoryPermissions(): bool
{
    echo "\n2. 检查目录权限...\n";
    
    $directories = [
        'runtime',
        'runtime/cache',
        'runtime/log',
        'runtime/session',
        'runtime/temp',
        'public/storage',
        'public/uploads'
    ];
    
    $allGood = true;
    
    foreach ($directories as $dir) {
        $fullPath = ROOT_PATH . $dir;
        
        // 创建目录（如果不存在）
        if (!is_dir($fullPath)) {
            if (!mkdir($fullPath, 0755, true)) {
                echo "   ❌ 无法创建目录: {$dir}\n";
                $allGood = false;
                continue;
            }
        }
        
        // 检查可写权限
        if (!is_writable($fullPath)) {
            echo "   ❌ 目录不可写: {$dir}\n";
            $allGood = false;
        } else {
            echo "   ✅ 目录权限正常: {$dir}\n";
        }
    }
    
    return $allGood;
}

/**
 * 检查配置文件
 */
function checkConfigFiles(): bool
{
    echo "\n3. 检查配置文件...\n";
    
    $configFiles = [
        'config/app.php',
        'config/database.php',
        'config/cache.php',
        'config/tczp.php'
    ];
    
    $allGood = true;
    
    foreach ($configFiles as $file) {
        $fullPath = ROOT_PATH . $file;
        
        if (!file_exists($fullPath)) {
            echo "   ❌ 配置文件不存在: {$file}\n";
            $allGood = false;
        } else {
            echo "   ✅ 配置文件存在: {$file}\n";
        }
    }
    
    // 检查环境变量文件
    $envFile = ROOT_PATH . '.env';
    if (!file_exists($envFile)) {
        echo "   ⚠️  环境变量文件不存在: .env\n";
        echo "      请复制 .env.example 为 .env 并配置相关参数\n";
    } else {
        echo "   ✅ 环境变量文件存在: .env\n";
    }
    
    return $allGood;
}

/**
 * 检查数据库连接
 */
function checkDatabaseConnection(): bool
{
    echo "\n4. 检查数据库连接...\n";
    
    try {
        // 加载ThinkPHP
        require_once ROOT_PATH . 'vendor/autoload.php';
        
        $app = new \think\App();
        $app->initialize();
        
        // 测试数据库连接
        $db = \think\facade\Db::connect();
        $result = $db->query('SELECT 1');
        
        if ($result) {
            echo "   ✅ 数据库连接正常\n";
            
            // 检查关键表是否存在
            $tables = [
                'eb_user', 'eb_worker_profiles', 'eb_enterprise_profiles',
                'eb_tasks', 'eb_task_categories', 'eb_system_admin'
            ];
            
            foreach ($tables as $table) {
                $exists = $db->query("SHOW TABLES LIKE '{$table}'");
                if (empty($exists)) {
                    echo "   ⚠️  数据表不存在: {$table}\n";
                } else {
                    echo "   ✅ 数据表存在: {$table}\n";
                }
            }
            
            return true;
        }
    } catch (\Exception $e) {
        echo "   ❌ 数据库连接失败: " . $e->getMessage() . "\n";
        return false;
    }
    
    return false;
}

/**
 * 检查Redis连接
 */
function checkRedisConnection(): bool
{
    echo "\n5. 检查Redis连接...\n";
    
    try {
        if (!extension_loaded('redis')) {
            echo "   ⚠️  Redis扩展未安装\n";
            return false;
        }
        
        $redis = new \Redis();
        $redis->connect('127.0.0.1', 6379);
        $redis->ping();
        
        echo "   ✅ Redis连接正常\n";
        return true;
    } catch (\Exception $e) {
        echo "   ⚠️  Redis连接失败: " . $e->getMessage() . "\n";
        echo "      Redis是可选的，但建议安装以提高性能\n";
        return false;
    }
}

/**
 * 优化配置
 */
function optimizeConfiguration(): void
{
    echo "\n6. 优化配置...\n";
    
    // 清理缓存
    $cacheDir = ROOT_PATH . 'runtime/cache';
    if (is_dir($cacheDir)) {
        $files = glob($cacheDir . '/*');
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
        echo "   ✅ 清理缓存完成\n";
    }
    
    // 清理日志
    $logDir = ROOT_PATH . 'runtime/log';
    if (is_dir($logDir)) {
        $files = glob($logDir . '/*.log');
        foreach ($files as $file) {
            if (filemtime($file) < time() - 7 * 24 * 3600) { // 删除7天前的日志
                unlink($file);
            }
        }
        echo "   ✅ 清理过期日志完成\n";
    }
    
    // 创建符号链接（如果不存在）
    $storageLink = ROOT_PATH . 'public/storage';
    $storagePath = ROOT_PATH . 'storage/app/public';
    
    if (!file_exists($storageLink) && is_dir($storagePath)) {
        if (symlink($storagePath, $storageLink)) {
            echo "   ✅ 创建存储符号链接完成\n";
        } else {
            echo "   ⚠️  创建存储符号链接失败\n";
        }
    }
}

/**
 * 生成优化报告
 */
function generateOptimizationReport(): void
{
    echo "\n7. 生成优化报告...\n";
    
    $report = [
        'timestamp' => date('Y-m-d H:i:s'),
        'php_version' => PHP_VERSION,
        'memory_limit' => ini_get('memory_limit'),
        'max_execution_time' => ini_get('max_execution_time'),
        'upload_max_filesize' => ini_get('upload_max_filesize'),
        'post_max_size' => ini_get('post_max_size'),
        'extensions' => get_loaded_extensions(),
    ];
    
    $reportFile = ROOT_PATH . 'runtime/optimization_report.json';
    file_put_contents($reportFile, json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    
    echo "   ✅ 优化报告已生成: runtime/optimization_report.json\n";
}

/**
 * 主函数
 */
function main(): void
{
    $success = true;
    
    // 执行检查
    $success &= checkPhpEnvironment();
    $success &= checkDirectoryPermissions();
    $success &= checkConfigFiles();
    $success &= checkDatabaseConnection();
    checkRedisConnection(); // Redis是可选的
    
    // 执行优化
    optimizeConfiguration();
    generateOptimizationReport();
    
    echo "\n==================== 优化完成 ====================\n";
    
    if ($success) {
        echo "✅ 服务端环境检查通过，可以正常运行！\n";
        echo "\n建议的下一步操作：\n";
        echo "1. 配置Web服务器（Nginx/Apache）\n";
        echo "2. 设置定时任务（crontab）\n";
        echo "3. 配置队列处理器\n";
        echo "4. 设置监控和日志\n";
    } else {
        echo "❌ 服务端环境存在问题，请根据上述提示进行修复！\n";
        exit(1);
    }
    
    echo "\n服务端API地址：\n";
    echo "- 师傅端：/api/v3/worker/*\n";
    echo "- 用户端：/api/v3/user/*\n";
    echo "- 管理后台：/api/v3/admin/*\n";
    echo "- 公共接口：/api/v3/public/*\n";
    
    echo "\n==================== 脚本执行完成 ====================\n";
}

// 执行主函数
main();
