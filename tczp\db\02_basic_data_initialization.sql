-- ==================== 基础数据初始化 ====================
-- 文件: 02_basic_data_initialization.sql
-- 描述: 初始化师傅端系统的基础数据
-- 执行顺序: 第二步执行（在01_database_structure_optimization.sql之后）
-- 作者: TCZP Team
-- 日期: 2024-12-19

-- 设置时间变量
SET @current_time = UNIX_TIMESTAMP();

-- ==================== 1. 插入基础角色数据 ====================
INSERT INTO `eb_user_roles` (`role_code`, `role_name`, `description`, `permissions`, `status`, `create_time`, `update_time`) VALUES
('customer', '普通用户', '发布任务的用户', '["task.create", "task.manage", "payment.pay", "review.create"]', 1, @current_time, @current_time),
('worker', '师傅', '接单的师傅用户', '["task.apply", "task.execute", "income.manage", "skill.manage", "profile.manage"]', 1, @current_time, @current_time),
('enterprise', '企业用户', '企业发布任务', '["task.batch_create", "worker.invite", "report.view", "enterprise.manage"]', 1, @current_time, @current_time),
('admin', '管理员', '平台管理员', '["*"]', 1, @current_time, @current_time);

-- ==================== 2. 插入技能分类数据 ====================

-- 2.1 一级分类
INSERT INTO `eb_skill_categories` (`parent_id`, `category_name`, `category_code`, `icon`, `description`, `sort`, `status`, `create_time`) VALUES
(0, '家政服务', 'housekeeping', 'icon-home', '家庭清洁、保姆、月嫂等服务', 1, 1, @current_time),
(0, '维修安装', 'repair', 'icon-tools', '家电维修、水电安装、装修等', 2, 1, @current_time),
(0, '搬家运输', 'moving', 'icon-truck', '搬家、货运、配送等服务', 3, 1, @current_time),
(0, '美容美发', 'beauty', 'icon-scissors', '美发、美甲、化妆等服务', 4, 1, @current_time),
(0, '教育培训', 'education', 'icon-book', '家教、技能培训等', 5, 1, @current_time),
(0, '健康护理', 'healthcare', 'icon-heart', '按摩、护理、康复等', 6, 1, @current_time),
(0, '其他服务', 'others', 'icon-more', '其他各类服务', 99, 1, @current_time);

-- 2.2 二级分类 - 家政服务
SET @housekeeping_id = (SELECT id FROM eb_skill_categories WHERE category_code = 'housekeeping');
INSERT INTO `eb_skill_categories` (`parent_id`, `category_name`, `category_code`, `icon`, `description`, `sort`, `status`, `create_time`) VALUES
(@housekeeping_id, '家庭清洁', 'house_cleaning', 'icon-clean', '日常清洁、深度清洁', 1, 1, @current_time),
(@housekeeping_id, '保姆服务', 'nanny', 'icon-user', '住家保姆、钟点工', 2, 1, @current_time),
(@housekeeping_id, '月嫂育儿', 'childcare', 'icon-baby', '月嫂、育儿嫂、早教', 3, 1, @current_time),
(@housekeeping_id, '老人护理', 'elderly_care', 'icon-elderly', '老人陪护、生活照料', 4, 1, @current_time);

-- 2.3 二级分类 - 维修安装
SET @repair_id = (SELECT id FROM eb_skill_categories WHERE category_code = 'repair');
INSERT INTO `eb_skill_categories` (`parent_id`, `category_name`, `category_code`, `icon`, `description`, `sort`, `status`, `create_time`) VALUES
(@repair_id, '家电维修', 'appliance_repair', 'icon-appliance', '空调、洗衣机、冰箱等维修', 1, 1, @current_time),
(@repair_id, '水电安装', 'plumbing_electrical', 'icon-electric', '水管、电路安装维修', 2, 1, @current_time),
(@repair_id, '装修装饰', 'decoration', 'icon-paint', '室内装修、墙面处理', 3, 1, @current_time),
(@repair_id, '家具安装', 'furniture_assembly', 'icon-furniture', '家具组装、安装', 4, 1, @current_time);

-- 2.4 二级分类 - 搬家运输
SET @moving_id = (SELECT id FROM eb_skill_categories WHERE category_code = 'moving');
INSERT INTO `eb_skill_categories` (`parent_id`, `category_name`, `category_code`, `icon`, `description`, `sort`, `status`, `create_time`) VALUES
(@moving_id, '居民搬家', 'residential_moving', 'icon-home-move', '家庭搬家服务', 1, 1, @current_time),
(@moving_id, '办公搬迁', 'office_moving', 'icon-office-move', '办公室搬迁服务', 2, 1, @current_time),
(@moving_id, '货物运输', 'cargo_transport', 'icon-cargo', '货物配送运输', 3, 1, @current_time),
(@moving_id, '同城配送', 'local_delivery', 'icon-delivery', '同城快递配送', 4, 1, @current_time);

-- ==================== 3. 插入具体技能数据 ====================

-- 3.1 家庭清洁技能
SET @house_cleaning_id = (SELECT id FROM eb_skill_categories WHERE category_code = 'house_cleaning');
INSERT INTO `eb_skills_new` (`category_id`, `skill_name`, `skill_code`, `description`, `icon`, `price_range`, `unit`, `certification_required`, `sort`, `status`, `create_time`) VALUES
(@house_cleaning_id, '日常保洁', 'daily_cleaning', '日常家庭清洁服务', 'icon-broom', '30-50元', '小时', 0, 1, 1, @current_time),
(@house_cleaning_id, '深度清洁', 'deep_cleaning', '全屋深度清洁服务', 'icon-deep-clean', '200-500元', '次', 0, 2, 1, @current_time),
(@house_cleaning_id, '开荒保洁', 'move_in_cleaning', '新房开荒清洁', 'icon-new-clean', '300-800元', '次', 0, 3, 1, @current_time),
(@house_cleaning_id, '玻璃清洁', 'window_cleaning', '门窗玻璃清洁', 'icon-window', '5-10元', '平米', 0, 4, 1, @current_time),
(@house_cleaning_id, '地毯清洗', 'carpet_cleaning', '地毯深度清洗', 'icon-carpet', '20-40元', '平米', 0, 5, 1, @current_time);

-- 3.2 家电维修技能
SET @appliance_repair_id = (SELECT id FROM eb_skill_categories WHERE category_code = 'appliance_repair');
INSERT INTO `eb_skills_new` (`category_id`, `skill_name`, `skill_code`, `description`, `icon`, `price_range`, `unit`, `certification_required`, `sort`, `status`, `create_time`) VALUES
(@appliance_repair_id, '空调维修', 'ac_repair', '空调安装、维修、清洗', 'icon-ac', '100-300元', '次', 1, 1, 1, @current_time),
(@appliance_repair_id, '洗衣机维修', 'washer_repair', '洗衣机故障维修', 'icon-washer', '80-200元', '次', 1, 2, 1, @current_time),
(@appliance_repair_id, '冰箱维修', 'fridge_repair', '冰箱故障维修', 'icon-fridge', '100-250元', '次', 1, 3, 1, @current_time),
(@appliance_repair_id, '热水器维修', 'heater_repair', '热水器安装维修', 'icon-heater', '100-200元', '次', 1, 4, 1, @current_time),
(@appliance_repair_id, '电视维修', 'tv_repair', '电视机故障维修', 'icon-tv', '120-300元', '次', 1, 5, 1, @current_time);

-- 3.3 水电安装技能
SET @plumbing_electrical_id = (SELECT id FROM eb_skill_categories WHERE category_code = 'plumbing_electrical');
INSERT INTO `eb_skills_new` (`category_id`, `skill_name`, `skill_code`, `description`, `icon`, `price_range`, `unit`, `certification_required`, `sort`, `status`, `create_time`) VALUES
(@plumbing_electrical_id, '水管安装', 'plumbing_install', '水管安装维修', 'icon-pipe', '50-150元', '米', 1, 1, 1, @current_time),
(@plumbing_electrical_id, '电路安装', 'electrical_install', '电路布线安装', 'icon-wire', '80-200元', '米', 1, 2, 1, @current_time),
(@plumbing_electrical_id, '开关插座', 'switch_socket', '开关插座安装', 'icon-socket', '20-50元', '个', 1, 3, 1, @current_time),
(@plumbing_electrical_id, '灯具安装', 'light_install', '各类灯具安装', 'icon-light', '30-100元', '个', 0, 4, 1, @current_time),
(@plumbing_electrical_id, '卫浴安装', 'bathroom_install', '卫浴设备安装', 'icon-bathroom', '100-300元', '套', 1, 5, 1, @current_time);

-- 3.4 保姆服务技能
SET @nanny_id = (SELECT id FROM eb_skill_categories WHERE category_code = 'nanny');
INSERT INTO `eb_skills_new` (`category_id`, `skill_name`, `skill_code`, `description`, `icon`, `price_range`, `unit`, `certification_required`, `sort`, `status`, `create_time`) VALUES
(@nanny_id, '住家保姆', 'live_in_nanny', '住家保姆服务', 'icon-home-care', '4000-8000元', '月', 1, 1, 1, @current_time),
(@nanny_id, '钟点工', 'hourly_worker', '按小时计费的家政服务', 'icon-clock', '25-45元', '小时', 0, 2, 1, @current_time),
(@nanny_id, '做饭阿姨', 'cooking_service', '专业做饭服务', 'icon-cook', '30-60元', '小时', 0, 3, 1, @current_time),
(@nanny_id, '洗衣熨烫', 'laundry_service', '洗衣熨烫服务', 'icon-laundry', '20-40元', '小时', 0, 4, 1, @current_time);

-- 3.5 搬家服务技能
SET @residential_moving_id = (SELECT id FROM eb_skill_categories WHERE category_code = 'residential_moving');
INSERT INTO `eb_skills_new` (`category_id`, `skill_name`, `skill_code`, `description`, `icon`, `price_range`, `unit`, `certification_required`, `sort`, `status`, `create_time`) VALUES
(@residential_moving_id, '小型搬家', 'small_moving', '小件物品搬运', 'icon-small-move', '200-500元', '次', 0, 1, 1, @current_time),
(@residential_moving_id, '整屋搬家', 'full_moving', '整套房屋搬迁', 'icon-full-move', '500-2000元', '次', 0, 2, 1, @current_time),
(@residential_moving_id, '钢琴搬运', 'piano_moving', '钢琴等贵重物品搬运', 'icon-piano', '300-800元', '次', 1, 3, 1, @current_time),
(@residential_moving_id, '家具拆装', 'furniture_disassembly', '家具拆装服务', 'icon-assembly', '50-200元', '件', 0, 4, 1, @current_time);

-- ==================== 4. 创建师傅完整信息视图 ====================
CREATE OR REPLACE VIEW `eb_worker_complete_info` AS
SELECT 
    wp.id,
    wp.user_id,
    wp.worker_no,
    wp.real_name,
    wp.phone,
    wp.work_experience,
    wp.service_areas,
    wp.work_status,
    wp.auth_status,
    wp.rating,
    wp.order_count,
    wp.completion_rate,
    u.nickname,
    u.avatar,
    u.add_time as register_time,
    GROUP_CONCAT(DISTINCT s.skill_name) as skills,
    AVG(ws.hourly_rate) as avg_hourly_rate,
    COUNT(DISTINCT ws.skill_id) as skill_count
FROM eb_worker_profiles wp
LEFT JOIN eb_user u ON wp.user_id = u.uid
LEFT JOIN eb_worker_skills_new ws ON wp.id = ws.worker_id AND ws.auth_status = 'approved'
LEFT JOIN eb_skills_new s ON ws.skill_id = s.id
WHERE wp.auth_status = 'approved'
GROUP BY wp.id;

-- ==================== 5. 数据完整性检查 ====================
SELECT 
    '用户角色' as table_name,
    COUNT(*) as record_count
FROM eb_user_roles
UNION ALL
SELECT 
    '技能分类' as table_name,
    COUNT(*) as record_count
FROM eb_skill_categories
UNION ALL
SELECT 
    '技能数据' as table_name,
    COUNT(*) as record_count
FROM eb_skills_new;

-- ==================== 完成提示 ====================
SELECT 'Basic data initialization completed successfully!' as message;
