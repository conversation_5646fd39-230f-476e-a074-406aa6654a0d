<?php

namespace app\controller\admin\statistic;


use app\controller\admin\AuthController;
use app\services\statistic\TradeStatisticServices;
use think\annotation\Inject;

/**
 * Class TradeStatistic
 * @package app\controller\admin\v1\statistic
 */
class TradeStatistic extends AuthController
{

    /**
     * @var TradeStatisticServices
     */
    #[Inject]
    protected TradeStatisticServices $services;

    /**
     * 顶部数据
     * @return mixed
     */
    public function topTrade()
    {
        $leftToday = $this->services->getTopLeftTrade(['time' => 'today']);
        $leftyestoday = $this->services->getTopLeftTrade(['time' => 'yestoday']);
        $rightOne = $this->services->getTopRightOneTrade();
        $rightTwo = $this->services->getTopRightTwoTrade();
        $right = ['today' => $rightOne, 'month' => $rightTwo];
        $totalleft = [$leftToday, $leftyestoday];
        $left = [];
        foreach ($totalleft as $k => $v) {
            $left['name'] = "当日订单金额";
            $left['x'] = $v['curve']['x'];
            $left['series'][$k]['money'] = round($v['total_money'], 2);
            $left['series'][$k]['value'] = array_values($v['curve']['y']);
        }

        $data['left'] = $left;
        $data['right'] = $right;
        return $this->success($data);
    }

    /**
     * 底部数据
     * @return mixed
     */
    public function bottomTrade()
    {
        $day = $this->request->getMore([
            ['data', ""],
        ]);
        $bottom = $this->services->getBottomTrade($day);
        return $this->success($bottom);
    }

}
