<?php

namespace app\services\other;


use app\dao\other\CityAreaDao;
use app\services\BaseServices;
use crmeb\exceptions\AdminException;
use crmeb\services\CacheService;
use crmeb\services\FormBuilder as Form;
use think\annotation\Inject;

/**
 * 城市数据（街道）
 * Class CityAreaServices
 * @package app\services\other
 * @mixin CityAreaDao
 */
class CityAreaServices extends BaseServices
{

    /**
     * 城市类型
     * @var string[]
     */
    public array $type = [
        '1' => 'province',
        '2' => 'city',
        '3' => 'area',
        '4' => 'street'
    ];

    /**
     * @var CityAreaDao
     */
    #[Inject]
    protected CityAreaDao $dao;

    /**
     * 获取某一个城市id相关上级所有ids
     * @param int $id
     * @return array|int[]
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getRelationCityIds(int $id)
    {
        $cityInfo = $this->dao->get($id);
        $ids = [];
        if ($cityInfo) {
            $ids = explode('/', trim($cityInfo['path'], '/'));
        }
        return array_merge([$id], $ids);
    }

    /**
     * @param int $id
     * @param int $expire
     * @return bool|mixed|null
     */
    public function getRelationCityIdsCache(int $id, int $expire = 1800)
    {
        return CacheService::redisHandler('apiCity')->remember('city_ids_' . $id, function () use ($id) {
            $cityInfo = $this->dao->get($id);
            $ids = [];
            if ($cityInfo) {
                $ids = explode('/', trim($cityInfo['path'], '/'));
            }
            return array_merge([$id], $ids);
        }, $expire);
    }


    /**
     * 获取城市数据
     * @param int $pid
     * @return false|mixed|string|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getCityTreeList(int $pid = 0)
    {
        $cityList = $this->dao->cacheStrRemember('pid_' . $pid, function () use ($pid) {
            $parent_name = '中国';
            if ($pid) {
                $city = $this->dao->get($pid);
                $parent_name = $city ? $city['name'] : '';
            }
            $cityList = $this->dao->getCityList(['parent_id' => $pid], 'id as value,id,name as label,parent_id as pid,level', ['children']);
            foreach ($cityList as &$item) {
                $item['parent_name'] = $parent_name;
                if (isset($item['children']) && $item['children']) {
                    $item['children'] = [];
                    $item['loading'] = false;
                    $item['_loading'] = false;
                } else {
                    unset($item['children']);
                }
            }
            return $cityList;
        });

        return $cityList;
    }

    /**
     * 添加城市数据表单
     * @param int $parentId
     * @return array
     * @throws \FormBuilder\Exception\FormBuilderException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function createCityForm(int $parentId)
    {
        $info = [];
        if ($parentId) {
            $info = $this->dao->get($parentId);
        }
        $field[] = Form::hidden('level', $info['level'] ?? 0);
        $field[] = Form::hidden('parent_id', $info['id'] ?? 0);
        $field[] = Form::input('parent_name', '父类名称', $info['name'] ?? '中国')->disabled(true);
        $field[] = Form::input('name', '名称')->required('请填写城市名称');
        return create_form('添加城市', $field, $this->url('/setting/city/save'));
    }

    /**
     * 添加城市数据创建
     * @param int $id
     * @return array
     * @throws \FormBuilder\Exception\FormBuilderException
     */
    public function updateCityForm(int $id)
    {
        $info = $this->dao->get($id);
        if (!$info) {
            throw new AdminException('需改的数据不存在');
        }
        if ($info['parent_id']) {
            $city = $this->dao->get($info['parent_id']);
            $info['parent_name'] = $city['name'];
        }
        $info = $info->toArray();
        $field[] = Form::hidden('id', $info['id']);
        $field[] = Form::hidden('level', $info['level']);
        $field[] = Form::hidden('parent_id', $info['parent_id']);
        $field[] = Form::input('parent_name', '父类名称', $info['parent_name'] ?? '中国')->disabled(true);
        $field[] = Form::input('name', '名称', $info['name'])->required('请填写城市名称');
        return create_form('修改城市', $field, $this->url('/setting/city/save'));
    }

    /**
     * 根据省市区名称获取对应 id
     * @param $province
     * @param $city
     * @param $area
     * @return int[]
     * @throws \ReflectionException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User: liusl
     * DateTime: 2024/12/18 下午3:42
     */
    public function getCityId($province, $city, $area)
    {
        $result = [
            'province' => 0,
            'city' => 0,
            'area' => 0
        ];

        $address = "{$province}/{$city}/$area";
        $info = $this->dao->search(['address' => $address])->find();

        if (!$info) {
            return $result;
        }

        $path = explode('/', $info['path']);
        return [
            'province' => $path[1],
            'city' => $path[2],
            'area' => $info['id']
        ];
    }
}
