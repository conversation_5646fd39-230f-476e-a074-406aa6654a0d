<?php

namespace app\model\system;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
use think\Model;

/**
 * 菜单关联模型
 * Class SystemMenus
 * @package app\model\system
 */
class SystemMenusRelevance extends BaseModel
{
    use ModelTrait;

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'system_menus_relevance';

    public function searchMenuIdAttr($query, $value)
    {
        if (is_array($value)) {
            $query->whereIn('menu_id', $value);
        } else {
            if ($value !== '') {
                $query->where('menu_id', $value);
            }
        }
    }

    public function searchKeywordAttr($query, $value)
    {
        if ($value !== '') {
            $query->whereLike('keyword', "%{$value}%");
        }
    }

}
