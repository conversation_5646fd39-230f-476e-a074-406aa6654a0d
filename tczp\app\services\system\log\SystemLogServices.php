<?php

namespace app\services\system\log;


use app\dao\system\log\SystemLogDao;
use app\services\BaseServices;
use app\services\system\admin\SystemAdminServices;
use app\services\system\SystemMenusServices;
use think\annotation\Inject;

/**
 * 系统日志
 * Class SystemLogServices
 * @package app\services\system\log
 * @mixin SystemLogDao
 */
class SystemLogServices extends BaseServices
{

    /**
     * @var SystemLogDao
     */
    #[Inject]
    protected SystemLogDao $dao;

    /**
     * 记录访问日志
     * @param int $adminId
     * @param string $adminName
     * @param string $module
     * @param string $rule
     * @param string $ip
     * @param string $type
     * @return bool
     */
    public function recordAdminLog(int $adminId, string $adminName, string $module, string $rule, string $ip, string $type)
    {
        /** @var SystemMenusServices $service */
        $service = app()->make(SystemMenusServices::class);
        $data = [
            'method' => $module,
            'add_time' => time(),
            'admin_name' => $adminName,
            'path' => $rule,
            'page' => $service->getVisitName($rule) ?: '未知',
            'ip' => $ip,
            'type' => $type
        ];
        if ($type == 'store') {
            $data['store_id'] = $adminId;
        } else {
            $data['admin_id'] = $adminId;
        }
        if ($this->dao->save($data)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 获取系统日志列表
     * @param array $where
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getLogList(array $where, int $level)
    {
        [$page, $limit] = $this->getPageValue();
        if (!$where['admin_id']) {
            /** @var SystemAdminServices $service */
            $service = app()->make(SystemAdminServices::class);
            $where['admin_id'] = $service->getAdminIds($level);
        }
        $list = $this->dao->getLogList($where, $page, $limit);
        $count = $this->dao->count($where);
        return compact('list', 'count');
    }
}
