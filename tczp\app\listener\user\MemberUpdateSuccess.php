<?php

namespace app\listener\user;


use app\services\user\member\MemberRightServices;
use crmeb\interfaces\ListenerInterface;

/**
 * Class MemberUpdateSuccess
 * @package app\listener\user
 */
class MemberUpdateSuccess implements ListenerInterface
{

    public function handle($event): void
    {
        /** @var MemberRightServices $memberRightService */
        $memberRightService = app()->make(MemberRightServices::class);
        $memberRightService->cacheTag()->clear();
    }
}
