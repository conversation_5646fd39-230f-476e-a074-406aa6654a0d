<?php
declare(strict_types=1);

namespace app\controller\worker;

use app\controller\BaseControllerOptimized;
use app\services\worker\WorkerProfileServices;
use app\services\worker\WorkerSkillServices;
use think\Response;

/**
 * 师傅个人中心控制器 - 完整版
 * Class Profile
 * @package app\controller\worker
 */
class Profile extends BaseControllerOptimized
{
    /**
     * 师傅档案服务
     * @var WorkerProfileServices
     */
    protected $workerProfileService;
    
    /**
     * 师傅技能服务
     * @var WorkerSkillServices
     */
    protected $workerSkillService;
    
    /**
     * 初始化
     */
    protected function initialize(): void
    {
        parent::initialize();
        $this->workerProfileService = app()->make(WorkerProfileServices::class);
        $this->workerSkillService = app()->make(WorkerSkillServices::class);
    }

    /**
     * 获取个人档案
     * @return Response
     */
    public function getProfile(): Response
    {
        try {
            $profile = $this->workerProfileService->getWorkerProfile($this->uid);
            return $this->success($profile);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 更新个人档案
     * @return Response
     */
    public function updateProfile(): Response
    {
        try {
            $data = $this->getPost();
            
            // 验证必填字段
            $this->validate($data, [
                'real_name' => 'require|max:50',
                'phone' => 'require|mobile',
                'id_card' => 'require|idCard',
                'work_experience' => 'require|number|between:0,50'
            ]);
            
            $result = $this->workerProfileService->updateProfile($this->uid, $data);
            
            if ($result) {
                return $this->success([], '档案更新成功');
            } else {
                return $this->fail('更新失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 上传头像
     * @return Response
     */
    public function uploadAvatar(): Response
    {
        try {
            $file = $this->request->file('avatar');
            
            if (!$file) {
                return $this->fail('请选择头像文件');
            }
            
            // 验证文件
            $validate = [
                'size' => 2 * 1024 * 1024, // 2MB
                'ext' => 'jpg,jpeg,png,gif'
            ];
            
            if (!$file->check($validate)) {
                return $this->fail($file->getError());
            }
            
            // 上传文件
            $saveName = \think\facade\Filesystem::disk('public')->putFile('avatar', $file);
            
            if (!$saveName) {
                return $this->fail('文件上传失败');
            }
            
            $avatarUrl = '/storage/' . $saveName;
            
            // 更新用户头像
            $result = $this->workerProfileService->updateAvatar($this->uid, $avatarUrl);
            
            if ($result) {
                return $this->success(['avatar' => $avatarUrl], '头像上传成功');
            } else {
                return $this->fail('头像更新失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取技能列表
     * @return Response
     */
    public function getSkills(): Response
    {
        try {
            $skills = $this->workerSkillService->getWorkerSkills($this->uid);
            return $this->success($skills);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 添加技能
     * @return Response
     */
    public function addSkill(): Response
    {
        try {
            $data = $this->getPost();
            
            // 验证必填字段
            $this->validate($data, [
                'skill_id' => 'require|number',
                'skill_level' => 'require|in:1,2,3,4,5',
                'experience_years' => 'require|number|between:0,50'
            ]);
            
            $result = $this->workerSkillService->addSkill($this->uid, $data);
            
            if ($result) {
                return $this->success([], '技能添加成功');
            } else {
                return $this->fail('添加失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 更新技能
     * @return Response
     */
    public function updateSkill(): Response
    {
        try {
            $skillId = (int)$this->getParam('id', 0);
            $data = $this->getPost();
            
            if (!$skillId) {
                return $this->fail('技能ID不能为空');
            }
            
            $result = $this->workerSkillService->updateSkill($this->uid, $skillId, $data);
            
            if ($result) {
                return $this->success([], '技能更新成功');
            } else {
                return $this->fail('更新失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 删除技能
     * @return Response
     */
    public function deleteSkill(): Response
    {
        try {
            $skillId = (int)$this->getParam('id', 0);
            
            if (!$skillId) {
                return $this->fail('技能ID不能为空');
            }
            
            $result = $this->workerSkillService->deleteSkill($this->uid, $skillId);
            
            if ($result) {
                return $this->success([], '技能删除成功');
            } else {
                return $this->fail('删除失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取工作统计
     * @return Response
     */
    public function getWorkStats(): Response
    {
        try {
            $period = $this->getParam('period', 'month');
            $stats = $this->workerProfileService->getWorkStats($this->uid, $period);
            return $this->success($stats);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取收入统计
     * @return Response
     */
    public function getIncomeStats(): Response
    {
        try {
            $period = $this->getParam('period', 'month');
            $stats = $this->workerProfileService->getIncomeStats($this->uid, $period);
            return $this->success($stats);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取评价统计
     * @return Response
     */
    public function getReviewStats(): Response
    {
        try {
            $stats = $this->workerProfileService->getReviewStats($this->uid);
            return $this->success($stats);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 更新工作状态
     * @return Response
     */
    public function updateWorkStatus(): Response
    {
        try {
            $status = $this->getPost('status', '');
            
            if (!in_array($status, ['available', 'busy', 'offline'])) {
                return $this->fail('无效的工作状态');
            }
            
            $result = $this->workerProfileService->updateWorkStatus($this->uid, $status);
            
            if ($result) {
                return $this->success([], '工作状态更新成功');
            } else {
                return $this->fail('更新失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取认证状态
     * @return Response
     */
    public function getAuthStatus(): Response
    {
        try {
            $status = $this->workerProfileService->getAuthStatus($this->uid);
            return $this->success($status);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 提交认证申请
     * @return Response
     */
    public function submitAuth(): Response
    {
        try {
            $data = $this->getPost();
            
            // 验证必填字段
            $this->validate($data, [
                'real_name' => 'require|max:50',
                'id_card' => 'require|idCard',
                'id_card_front' => 'require',
                'id_card_back' => 'require'
            ]);
            
            $result = $this->workerProfileService->submitAuth($this->uid, $data);
            
            if ($result) {
                return $this->success([], '认证申请提交成功');
            } else {
                return $this->fail('提交失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取设置信息
     * @return Response
     */
    public function getSettings(): Response
    {
        try {
            $settings = $this->workerProfileService->getSettings($this->uid);
            return $this->success($settings);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 更新设置
     * @return Response
     */
    public function updateSettings(): Response
    {
        try {
            $data = $this->getPost();
            
            $result = $this->workerProfileService->updateSettings($this->uid, $data);
            
            if ($result) {
                return $this->success([], '设置更新成功');
            } else {
                return $this->fail('更新失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}
