<?php

namespace app\model\user\label;


use crmeb\basic\BaseModel;
use think\Model;

/**
 * 标签分类
 * Class UserLabelCate
 * @package app\model\user\label
 */
class UserLabelCate extends BaseModel
{
    /**
     * 表名
     * @var string
     */
    protected $name = 'user_label_cate';

    /**
     * 主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * @param Model $query
     * @param $value
     */
    public function searchNameAttr($query, $value)
    {
        $query->whereLike('name', '%' . $value . '%');
    }

    /**
     * 一对多关联
     * @return \think\model\relation\HasMany
     */
    public function label()
    {
        return $this->hasMany(UserLabel::class, 'label_cate', 'id');
    }

    /**
     * type搜索器
     * @param Model $query
     * @param $value
     */
    public function searchTypeAttr($query, $value)
    {
        if ($value) $query->where('type', $value);
    }

    /**
     * store_id搜索器
     * @param Model $query
     * @param $value
     */
    public function searchStoreIdAttr($query, $value)
    {
        if ($value) $query->where('store_id', $value);
    }
}
