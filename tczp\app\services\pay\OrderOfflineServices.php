<?php

namespace app\services\pay;


use app\services\BaseServices;
use app\services\order\StoreOrderServices;
use app\services\order\StoreOrderSuccessServices;
use think\exception\ValidateException;

/**
 * 线下支付
 * Class OrderOfflineServices
 * @package app\services\pay
 */
class OrderOfflineServices extends BaseServices
{

    /**
     * 线下支付
     * @param int $id
     * @return mixed
     */
    public function orderOffline(int $id)
    {
        /** @var StoreOrderServices $orderSerives */
        $orderSerives = app()->make(StoreOrderServices::class);
        $orderInfo = $orderSerives->get($id);
        if (!$orderInfo) {
            throw new ValidateException('订单不存在');
        }

        if ($orderInfo->paid) {
            throw new ValidateException('订单已支付');
        }
        /** @var StoreOrderSuccessServices $storeOrderSuccessServices */
        $storeOrderSuccessServices = app()->make(StoreOrderSuccessServices::class);
        $storeOrderSuccessServices->paySuccess($orderInfo->toArray(), PayServices::OFFLINE_PAY);
        return true;
    }
}
