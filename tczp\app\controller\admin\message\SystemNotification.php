<?php
namespace app\controller\admin\message;

use app\controller\admin\AuthController;
use app\services\message\SystemNotificationServices;
use think\annotation\Inject;

/**
 * Class SystemRole
 * @package app\adminapi\controller\v1\setting
 */
class SystemNotification extends AuthController
{

    /**
     * @var SystemNotificationServices
     */
    #[Inject]
    protected SystemNotificationServices $services;

    /**
     * 显示资源列表
     *
     * @return \think\Response
     */
    public function index()
    {
        $where = $this->request->getMore([
            ['type', ''],
        ]);
        return app('json')->success($this->services->getNotList($where));
    }

    /**
     * 显示编辑
     *
     * @return \think\Response
     */
    public function info()
    {
        $where = $this->request->getMore([
            ['type', ''],
            ['id', 0]
        ]);
        if (!$where['id']) return app('json')->fail('参数错误');
        return app('json')->success($this->services->getNotInfo($where));
    }

    /**
     * 保存新建的资源
     *
     * @return \think\Response
     */
    public function save()
    {
        $data = $this->request->postMore([
            ['id', 0],
            ['type', ''],
            ['name', ''],
            ['title', ''],
            ['is_system', 0],
            ['is_app', 0],
            ['is_wechat', 0],
            ['is_routine', 0],
            ['is_sms', 0],
            ['sms_id', 0],
            ['is_ent_wechat', 0],
            ['system_title', ''],
            ['system_text', ''],
            ['tempid', ''],
            ['ent_wechat_text', ''],
            ['url', ''],
            ['wechat_id', ''],
            ['routine_id', '']
        ]);
        if (!$data['id']) return app('json')->fail('参数错误');
        if ($this->services->saveData($data)) {
            $this->services->clearTemplateCache();
            return app('json')->success('修改成功!');
        } else {
            return app('json')->fail('修改失败,请稍候再试!');
        }
    }

    /**
     * 修改消息状态
     *
     * @return array
     */
    public function set_status($type, $status, $id)
    {
        if ($type == '' || $status == '' || $id == 0) return $this->fail('参数错误');
        $this->services->update($id, [$type => $status]);
        $this->services->clearTemplateCache();
        return $this->success($status == 1 ? '开启成功' : '关闭成功');
    }

}
