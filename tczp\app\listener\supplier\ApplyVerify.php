<?php

namespace app\listener\supplier;


use crmeb\interfaces\ListenerInterface;

/**
 * 供应商入驻审核事件
 * Class ApplyVerify
 * @package app\listener\supplier
 */
class ApplyVerify implements ListenerInterface
{

    public function handle($event): void
    {
		[$supplierInfo, $verifyStatus] = $event;

		if ($verifyStatus == 1) {//通过
			$mark = 'supplier_verify_success';
		} else {//未通过
			$mark = 'supplier_verify_fail';
		}
		//发送消息
		event('notice.notice', [$supplierInfo, $mark]);
    }
}
