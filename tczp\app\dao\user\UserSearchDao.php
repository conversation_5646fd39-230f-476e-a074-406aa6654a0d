<?php
declare (strict_types=1);

namespace app\dao\user;

use app\dao\BaseDao;
use app\model\user\UserSearch;

/**
 * 用户搜索
 * Class UserSearchDao
 * @package app\dao\user
 */
class UserSearchDao extends BaseDao
{

    /**
     * 设置模型
     * @return string
     */
    protected function setModel(): string
    {
        return UserSearch::class;
    }

    /**
 	* 获取列表
	* @param array $where
	* @param string $field
	* @param int $page
	* @param int $limit
	* @param string $order
	* @return array
	* @throws \ReflectionException
	* @throws \think\db\exception\DataNotFoundException
	* @throws \think\db\exception\DbException
	* @throws \think\db\exception\ModelNotFoundException
	*/
    public function getList(array $where = [], string $field = '*', int $page = 0, int $limit = 0, string $order = 'id desc'): array
    {
        return $this->search($where)->field($field)
        	->when($page && $limit, function ($query) use ($page, $limit) {
            $query->page($page, $limit);
        	})->order($order)->select()->toArray();
    }

    /**
     * * 获取全局|用户某个关键词搜素结果
     * @param int $uid
     * @param string $keyword 关键词
     * @param int $preTime 多长时间内认为结果集有效
     * @return array|\think\Model
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getKeywordResult(int $uid, string $keyword, int $preTime = 7200)
    {
        if (!$keyword) return [];
        $where = ['keyword' => $keyword];
        if ($uid) $where['uid'] = $uid;
        return $this->search($where)->when($uid && $preTime == 0, function ($query) {
                $query->where('is_del', 0);
            })->when($preTime > 0, function ($query) use ($preTime) {
                $query->where('add_time', '>', time() - $preTime);
            })->order('add_time desc,id desc')->find() ?? [];
    }
}
