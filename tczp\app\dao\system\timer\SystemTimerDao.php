<?php

namespace app\dao\system\timer;

use app\dao\BaseDao;
use app\model\system\timer\SystemTimer;

/**
 * Class SystemTimerDao
 * @package app\dao\system\timer
 */
class SystemTimerDao extends BaseDao
{
    protected function setModel(): string
    {
        return SystemTimer::class;
    }

    /**
	* 获取列表
	* @param array $where
	* @param int $page
	* @param int $limit
	* @param string $field
	* @return array
	* @throws \think\db\exception\DataNotFoundException
	* @throws \think\db\exception\DbException
	* @throws \think\db\exception\ModelNotFoundException
	 */
    public function getList(array $where, int $page = 0, int $limit = 0, string $field = '*')
    {
        return $this->search($where)->field($field)->when($page && $limit, function ($query) use($page, $limit) {
			$query->page($page, $limit);
        })->select()->toArray();
    }
}
