<?php
// +----------------------------------------------------------------------
// | 同城用工系统 - 师傅端技能管理
// +----------------------------------------------------------------------

namespace app\controller\staff;

use app\controller\BaseAuth;
use think\facade\Db;

/**
 * 技能管理控制器
 */
class Skill extends BaseAuth
{
    /**
     * 不需要登录的方法
     */
    protected $noAuthMethods = ['getSkillList', 'getSkillCategories'];

    /**
     * 获取技能列表
     */
    public function getSkillList()
    {
        $category = $this->request->get('category', '');
        $keyword = $this->request->get('keyword', '');

        try {
            $where = [['status', '=', 1]];

            if ($category) {
                $where[] = ['category', '=', $category];
            }

            if ($keyword) {
                $where[] = ['name', 'like', '%' . $keyword . '%'];
            }

            $list = Db::name('skills')
                ->where($where)
                ->order('sort asc, id asc')
                ->select()
                ->toArray();

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail('获取技能列表失败：' . $e->getMessage());
        }
    }

    /**
     * 申请技能认证
     */
    public function applySkill()
    {
        $skillId = $this->request->post('skill_id', 0);
        $level = $this->request->post('level', 'beginner');
        $certificateUrl = $this->request->post('certificate_url', '');

        if (!$skillId) {
            return $this->fail('请选择要认证的技能');
        }

        if (!in_array($level, ['beginner', 'intermediate', 'advanced', 'expert'])) {
            return $this->fail('技能等级参数错误');
        }

        try {
            $userId = $this->getUserId();

            // 检查是否已申请过该技能
            $existSkill = Db::name('user_skills')
                ->where([
                    'user_id' => $userId,
                    'skill_id' => $skillId
                ])
                ->find();

            if ($existSkill) {
                return $this->fail('您已申请过该技能认证');
            }

            $userSkillId = Db::name('user_skills')->insertGetId([
                'user_id' => $userId,
                'skill_id' => $skillId,
                'level' => $level,
                'certificate_url' => $certificateUrl,
                'status' => 'pending',
                'create_time' => time()
            ]);

            return $this->success(['user_skill_id' => $userSkillId], '技能认证申请提交成功');
        } catch (\Exception $e) {
            return $this->fail('申请技能认证失败：' . $e->getMessage());
        }
    }

    /**
     * 获取我的技能
     */
    public function getMySkills()
    {
        try {
            $userId = $this->getUserId();

            $list = Db::name('user_skills')
                ->alias('us')
                ->leftJoin('skills s', 'us.skill_id = s.id')
                ->where('us.user_id', $userId)
                ->field('us.*, s.name as skill_name, s.category, s.icon')
                ->order('us.create_time desc')
                ->select()
                ->toArray();

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail('获取我的技能失败：' . $e->getMessage());
        }
    }

    /**
     * 获取技能分类
     */
    public function getSkillCategories()
    {
        try {
            $categories = Db::name('skills')
                ->where('status', 1)
                ->group('category')
                ->column('category');

            return $this->success($categories);
        } catch (\Exception $e) {
            return $this->fail('获取技能分类失败：' . $e->getMessage());
        }
    }
} 