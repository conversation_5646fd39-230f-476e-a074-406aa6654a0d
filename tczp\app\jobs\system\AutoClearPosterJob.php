<?php
namespace app\jobs\system;

use crmeb\basic\BaseJobs;
use app\services\system\attachment\SystemAttachmentServices;
use crmeb\traits\QueueTrait;
use think\facade\Log;

/**
 * 自动清除海报
 * Class AutoClearPosterJob
 * @package app\jobs\user
 */
class AutoClearPosterJob extends BaseJobs
{
    use QueueTrait;

    /**
     * @return string
     */
    protected static function queueName()
    {
        return 'CRMEB_PRO_TASK';
    }

    /**
     * @param $event
     */
    public function doJob()
    {
        //清除昨日海报
        try {
            /** @var SystemAttachmentServices $attach */
            $attach = app()->make(SystemAttachmentServices::class);
            $attach->emptyYesterdayAttachment();
        } catch (\Throwable $e) {

            response_log_write([
                'message' => '清除昨日海报,失败原因:[' . class_basename($this) . ']' . $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
		return true;
    }
}
