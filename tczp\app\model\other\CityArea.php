<?php

namespace app\model\other;


use crmeb\basic\BaseModel;

/**
 * 城市数据（包含街道）
 * Class CityArea
 * @package app\model\other
 */
class CityArea extends BaseModel
{

    /**
     * @var string
     */
    protected $name = 'city_area';

    /**
     * @var string
     */
    protected $key = 'id';

    /**
     * @return \think\model\relation\HasOne
     */
    public function children()
    {
        return $this->hasMany(self::class, 'parent_id', 'id');
    }

}
