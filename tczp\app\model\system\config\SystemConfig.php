<?php

namespace app\model\system\config;

use app\model\store\StoreConfig;
use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
use think\Model;

/**
 * 系统配置模型
 * Class SystemConfig
 * @package app\model\system\config
 */
class SystemConfig extends BaseModel
{
    use ModelTrait;

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'system_config';

    /**
     * 一对一关联门店配置表
     * @return \think\model\relation\HasOne
     */
    public function storeConfig()
    {
        return $this->hasOne(StoreConfig::class, 'key_name', 'menu_name')->field(['key_name', 'type', 'relation_id', 'value'])->bind([
            'store_value' => 'value',
        ]);
    }

    /**
     * 菜单名搜索器
     * @param Model $query
     * @param $value
     */
    public function searchMenuNameAttr($query, $value)
    {
        if (is_array($value)) {
            $query->whereIn('menu_name', $value);
        } else {
            $query->where('menu_name', $value);
        }
    }

    /**
     * tab id 搜索
     * @param Model $query
     * @param $value
     */
    public function searchTabIdAttr($query, $value)
    {
        $query->where('config_tab_id', $value);
    }

    /**
     * 状态搜索器
     * @param Model $query
     * @param $value
     */
    public function searchStatusAttr($query, $value)
    {
        $query->where('status', $value ?: 1);
    }

    /**
     * value搜索器
     * @param Model $query
     * @param $value
     */
    public function searchValueAttr($query, $value)
    {
        $query->where('value', $value);
    }


    /**
     * is_store 搜索
     * @param $query
     * @param $value
     */
    public function searchIsStoreAttr($query, $value)
    {
        if ($value) {
            $query->where('is_store', $value);
        }
    }
}
