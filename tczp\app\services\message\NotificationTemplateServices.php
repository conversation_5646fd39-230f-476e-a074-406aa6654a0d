<?php
declare (strict_types = 1);

namespace app\services\message;

use app\services\BaseServices;
use app\dao\message\NotificationTemplateDao;
use think\facade\Cache;

/**
 * 通知模板管理服务
 * Class NotificationTemplateServices
 * @package app\services\message
 */
class NotificationTemplateServices extends BaseServices
{
    /**
     * NotificationTemplateServices constructor.
     * @param NotificationTemplateDao $dao
     */
    public function __construct(NotificationTemplateDao $dao)
    {
        $this->dao = $dao;
    }

    /**
     * 获取通知模板
     * @param string $event_key 事件键名
     * @param string $channel 通知渠道
     * @return array|null
     */
    public function getTemplate(string $event_key, string $channel = 'wechat'): ?array
    {
        $cacheKey = "notification_template_{$event_key}_{$channel}";
        
        $template = Cache::remember($cacheKey, function () use ($event_key, $channel) {
            return $this->dao->getOne([
                'event_key' => $event_key,
                'channel' => $channel,
                'status' => 1
            ]);
        }, 3600);

        return $template ? $template->toArray() : null;
    }

    /**
     * 创建通知模板
     * @param array $data
     * @return bool
     */
    public function createTemplate(array $data): bool
    {
        $data['create_time'] = time();
        $data['update_time'] = time();
        
        $result = $this->dao->save($data);
        
        if ($result) {
            $this->clearTemplateCache($data['event_key'], $data['channel']);
        }
        
        return $result;
    }

    /**
     * 更新通知模板
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function updateTemplate(int $id, array $data): bool
    {
        $template = $this->dao->get($id);
        if (!$template) {
            return false;
        }

        $data['update_time'] = time();
        $result = $this->dao->update($id, $data);
        
        if ($result) {
            $this->clearTemplateCache($template['event_key'], $template['channel']);
        }
        
        return $result;
    }

    /**
     * 删除通知模板
     * @param int $id
     * @return bool
     */
    public function deleteTemplate(int $id): bool
    {
        $template = $this->dao->get($id);
        if (!$template) {
            return false;
        }

        $result = $this->dao->delete($id);
        
        if ($result) {
            $this->clearTemplateCache($template['event_key'], $template['channel']);
        }
        
        return $result;
    }

    /**
     * 获取模板列表
     * @param array $where
     * @param int $page
     * @param int $limit
     * @return array
     */
    public function getTemplateList(array $where = [], int $page = 1, int $limit = 20): array
    {
        [$page, $limit] = $this->getPageValue($page, $limit);
        
        $list = $this->dao->getList($where, '*', $page, $limit, 'id desc');
        $count = $this->dao->count($where);
        
        return compact('list', 'count');
    }

    /**
     * 批量导入模板
     * @param array $templates
     * @return bool
     */
    public function batchImportTemplates(array $templates): bool
    {
        try {
            foreach ($templates as $template) {
                $template['create_time'] = time();
                $template['update_time'] = time();
                
                // 检查是否已存在
                $exists = $this->dao->getOne([
                    'event_key' => $template['event_key'],
                    'channel' => $template['channel']
                ]);
                
                if ($exists) {
                    // 更新
                    $this->dao->update($exists['id'], $template);
                } else {
                    // 创建
                    $this->dao->save($template);
                }
                
                $this->clearTemplateCache($template['event_key'], $template['channel']);
            }
            
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 获取默认模板配置
     * @return array
     */
    public function getDefaultTemplates(): array
    {
        return [
            [
                'event_key' => 'task_published',
                'channel' => 'wechat',
                'name' => '任务发布成功',
                'template_id' => 'OPENTM207791515',
                'title' => '任务发布成功通知',
                'content' => '您的任务《{task_title}》已成功发布',
                'template_data' => json_encode([
                    'first' => '您的任务已成功发布！',
                    'keyword1' => '{task_title}',
                    'keyword2' => '{publish_time}',
                    'keyword3' => '{task_address}',
                    'remark' => '等待师傅申请，我们会及时通知您。'
                ]),
                'jump_url' => '/pages/task/detail?id={task_id}',
                'status' => 1
            ],
            [
                'event_key' => 'task_applied',
                'channel' => 'wechat',
                'name' => '师傅申请任务',
                'template_id' => 'OPENTM207791516',
                'title' => '师傅申请通知',
                'content' => '师傅{worker_name}申请了您的任务',
                'template_data' => json_encode([
                    'first' => '有师傅申请了您的任务！',
                    'keyword1' => '{worker_name}',
                    'keyword2' => '{task_title}',
                    'keyword3' => '{apply_time}',
                    'keyword4' => '{worker_rating}',
                    'remark' => '请及时查看师傅信息并确认。'
                ]),
                'jump_url' => '/pages/task/applications?id={task_id}',
                'status' => 1
            ],
            [
                'event_key' => 'task_completed',
                'channel' => 'wechat',
                'name' => '任务完成',
                'template_id' => 'OPENTM207791519',
                'title' => '任务完成通知',
                'content' => '任务《{task_title}》已完成',
                'template_data' => json_encode([
                    'first' => '任务已完成！',
                    'keyword1' => '{task_title}',
                    'keyword2' => '{worker_name}',
                    'keyword3' => '{complete_time}',
                    'keyword4' => '{total_amount}',
                    'remark' => '请确认任务完成情况并进行评价。'
                ]),
                'jump_url' => '/pages/task/confirm?id={task_id}',
                'status' => 1
            ],
            [
                'event_key' => 'new_task_available',
                'channel' => 'wechat',
                'name' => '新任务通知',
                'template_id' => 'OPENTM207791521',
                'title' => '新任务通知',
                'content' => '附近有新任务《{task_title}》',
                'template_data' => json_encode([
                    'first' => '附近有新任务！',
                    'keyword1' => '{task_title}',
                    'keyword2' => '{task_address}',
                    'keyword3' => '{distance}',
                    'keyword4' => '{task_price}',
                    'remark' => '快来抢单吧！先到先得。'
                ]),
                'jump_url' => '/pages/task/detail?id={task_id}',
                'status' => 1
            ]
        ];
    }

    /**
     * 清除模板缓存
     * @param string $event_key
     * @param string $channel
     */
    private function clearTemplateCache(string $event_key, string $channel): void
    {
        $cacheKey = "notification_template_{$event_key}_{$channel}";
        Cache::delete($cacheKey);
    }

    /**
     * 验证模板数据
     * @param array $data
     * @return array
     */
    public function validateTemplateData(array $data): array
    {
        $errors = [];
        
        if (empty($data['event_key'])) {
            $errors[] = '事件键名不能为空';
        }
        
        if (empty($data['channel'])) {
            $errors[] = '通知渠道不能为空';
        }
        
        if (empty($data['name'])) {
            $errors[] = '模板名称不能为空';
        }
        
        if (empty($data['template_id'])) {
            $errors[] = '模板ID不能为空';
        }
        
        return $errors;
    }

    /**
     * 测试模板
     * @param int $template_id
     * @param array $test_data
     * @return array
     */
    public function testTemplate(int $template_id, array $test_data): array
    {
        try {
            $template = $this->dao->get($template_id);
            if (!$template) {
                return ['success' => false, 'message' => '模板不存在'];
            }

            // 解析模板数据
            $templateData = json_decode($template['template_data'], true);
            
            // 替换测试数据
            $result = [];
            foreach ($templateData as $key => $value) {
                $result[$key] = $this->replaceVariables($value, $test_data);
            }

            return [
                'success' => true,
                'data' => $result,
                'preview' => $this->generatePreview($result)
            ];

        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * 替换变量
     * @param string $template
     * @param array $data
     * @return string
     */
    private function replaceVariables(string $template, array $data): string
    {
        foreach ($data as $key => $value) {
            $template = str_replace('{' . $key . '}', (string)$value, $template);
        }
        return $template;
    }

    /**
     * 生成预览
     * @param array $data
     * @return string
     */
    private function generatePreview(array $data): string
    {
        $preview = '';
        foreach ($data as $key => $value) {
            $preview .= "{$key}: {$value}\n";
        }
        return $preview;
    }
}
