<?php
declare (strict_types = 1);

namespace app\controller\api;

use app\Request;
use app\services\task\TaskProgressServices;
use think\annotation\Inject;

/**
 * 任务进度控制器
 * Class TaskProgressController
 * @package app\controller\api\v1
 */
class TaskProgressController
{
    /**
     * @var TaskProgressServices
     */
    #[Inject]
    protected TaskProgressServices $services;

    /**
     * 更新任务进度
     * @param Request $request
     * @return mixed
     */
    public function updateProgress(Request $request)
    {
        $uid = $request->uid();
        [$task_id, $status, $progress, $description, $images, $location] = $request->postMore([
            ['task_id', 0],
            ['status', ''],
            ['progress', 0],
            ['description', ''],
            ['images', []],
            ['location', []]
        ], true);

        if (!$task_id) {
            return app('json')->fail('任务ID不能为空');
        }

        if (!$status) {
            return app('json')->fail('状态不能为空');
        }

        try {
            $result = $this->services->updateTaskProgress($uid, $task_id, $status, $progress, $description, $images, $location);

            if ($result) {
                return app('json')->success([], '进度更新成功');
            } else {
                return app('json')->fail('进度更新失败');
            }
        } catch (\Exception $e) {
            return app('json')->fail('进度更新失败：' . $e->getMessage());
        }
    }

    /**
     * 获取任务进度列表
     * @param Request $request
     * @return mixed
     */
    public function getProgressList(Request $request)
    {
        [$task_id, $page, $limit] = $request->getMore([
            ['task_id', 0],
            ['page', 1],
            ['limit', 20]
        ], true);

        if (!$task_id) {
            return app('json')->fail('任务ID不能为空');
        }

        try {
            $result = $this->services->getProgressList($task_id, $page, $limit);
            return app('json')->success($result);
        } catch (\Exception $e) {
            return app('json')->fail('获取进度列表失败：' . $e->getMessage());
        }
    }

    /**
     * 获取任务进度详情
     * @param Request $request
     * @return mixed
     */
    public function getProgressDetail(Request $request)
    {
        $task_id = $request->get('task_id', 0);

        if (!$task_id) {
            return app('json')->fail('任务ID不能为空');
        }

        try {
            $result = $this->services->getProgressDetail($task_id);
            return app('json')->success($result);
        } catch (\Exception $e) {
            return app('json')->fail('获取进度详情失败：' . $e->getMessage());
        }
    }

    /**
     * 获取任务时间轴
     * @param Request $request
     * @return mixed
     */
    public function getTaskTimeline(Request $request)
    {
        $task_id = $request->get('task_id', 0);

        if (!$task_id) {
            return app('json')->fail('任务ID不能为空');
        }

        try {
            $result = $this->services->getTaskTimeline($task_id);
            return app('json')->success($result);
        } catch (\Exception $e) {
            return app('json')->fail('获取任务时间轴失败：' . $e->getMessage());
        }
    }

    /**
     * 开始任务
     * @param Request $request
     * @return mixed
     */
    public function startTask(Request $request)
    {
        $uid = $request->uid();
        [$task_id, $start_location, $start_images] = $request->postMore([
            ['task_id', 0],
            ['start_location', []],
            ['start_images', []]
        ], true);

        if (!$task_id) {
            return app('json')->fail('任务ID不能为空');
        }

        try {
            $result = $this->services->startTask($uid, $task_id, $start_location, $start_images);

            if ($result) {
                return app('json')->success([], '任务已开始');
            } else {
                return app('json')->fail('开始任务失败');
            }
        } catch (\Exception $e) {
            return app('json')->fail('开始任务失败：' . $e->getMessage());
        }
    }

    /**
     * 暂停任务
     * @param Request $request
     * @return mixed
     */
    public function pauseTask(Request $request)
    {
        $uid = $request->uid();
        [$task_id, $reason] = $request->postMore([
            ['task_id', 0],
            ['reason', '']
        ], true);

        if (!$task_id) {
            return app('json')->fail('任务ID不能为空');
        }

        try {
            $result = $this->services->pauseTask($uid, $task_id, $reason);

            if ($result) {
                return app('json')->success([], '任务已暂停');
            } else {
                return app('json')->fail('暂停任务失败');
            }
        } catch (\Exception $e) {
            return app('json')->fail('暂停任务失败：' . $e->getMessage());
        }
    }

    /**
     * 恢复任务
     * @param Request $request
     * @return mixed
     */
    public function resumeTask(Request $request)
    {
        $uid = $request->uid();
        $task_id = $request->post('task_id', 0);

        if (!$task_id) {
            return app('json')->fail('任务ID不能为空');
        }

        try {
            $result = $this->services->resumeTask($uid, $task_id);

            if ($result) {
                return app('json')->success('任务已恢复');
            } else {
                return app('json')->fail('恢复任务失败');
            }
        } catch (\Exception $e) {
            return app('json')->fail('恢复任务失败：' . $e->getMessage());
        }
    }

    /**
     * 完成任务
     * @param Request $request
     * @return mixed
     */
    public function completeTask(Request $request)
    {
        $uid = $request->uid();
        [$task_id, $completion_images, $completion_description, $completion_location] = $request->postMore([
            ['task_id', 0],
            ['completion_images', []],
            ['completion_description', ''],
            ['completion_location', []]
        ], true);

        if (!$task_id) {
            return app('json')->fail('任务ID不能为空');
        }

        try {
            $result = $this->services->completeTask($uid, $task_id, $completion_images, $completion_description, $completion_location);

            if ($result) {
                return app('json')->success('任务已完成');
            } else {
                return app('json')->fail('完成任务失败');
            }
        } catch (\Exception $e) {
            return app('json')->fail('完成任务失败：' . $e->getMessage());
        }
    }

    /**
     * 取消任务
     * @param Request $request
     * @return mixed
     */
    public function cancelTask(Request $request)
    {
        $uid = $request->uid();
        [$task_id, $cancel_reason] = $request->postMore([
            ['task_id', 0],
            ['cancel_reason', '']
        ], true);

        if (!$task_id) {
            return app('json')->fail('任务ID不能为空');
        }

        if (!$cancel_reason) {
            return app('json')->fail('取消原因不能为空');
        }

        try {
            $result = $this->services->cancelTask($uid, $task_id, $cancel_reason);

            if ($result) {
                return app('json')->success('任务已取消');
            } else {
                return app('json')->fail('取消任务失败');
            }
        } catch (\Exception $e) {
            return app('json')->fail('取消任务失败：' . $e->getMessage());
        }
    }

    /**
     * 获取任务统计信息
     * @param Request $request
     * @return mixed
     */
    public function getTaskStats(Request $request)
    {
        $uid = $request->uid();
        $date_range = $request->get('date_range', '');

        try {
            $result = $this->services->getTaskStats($uid, $date_range);
            return app('json')->success($result);
        } catch (\Exception $e) {
            return app('json')->fail('获取统计信息失败：' . $e->getMessage());
        }
    }

    /**
     * 获取进度模板
     * @param Request $request
     * @return mixed
     */
    public function getProgressTemplates(Request $request)
    {
        $category_id = $request->get('category_id', 0);

        try {
            $result = $this->services->getProgressTemplates($category_id);
            return app('json')->success($result);
        } catch (\Exception $e) {
            return app('json')->fail('获取进度模板失败：' . $e->getMessage());
        }
    }
}