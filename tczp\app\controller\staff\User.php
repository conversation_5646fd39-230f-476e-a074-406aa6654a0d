<?php
// +----------------------------------------------------------------------
// | 同城用工系统 - 师傅端用户管理
// +----------------------------------------------------------------------

namespace app\controller\staff;

use app\controller\BaseAuth;
use think\facade\Db;

/**
 * 用户管理控制器
 */
class User extends BaseAuth
{
    /**
     * 获取用户信息
     */
    public function getUserInfo()
    {
        try {
            $userId = $this->getUserId();
            
            $userInfo = Db::name('users')
                ->where('id', $userId)
                ->field('id, nickname, avatar, phone, real_name, user_type, 
                        auth_status, skill_level, rating, total_tasks, 
                        total_income, create_time')
                ->find();

            if (!$userInfo) {
                return $this->fail('用户不存在');
            }

            // 获取认证信息
            $authInfo = Db::name('user_auth')
                ->where('user_id', $userId)
                ->where('status', 'approved')
                ->select()
                ->toArray();

            $userInfo['auth_info'] = $authInfo;

            // 获取技能认证
            $skills = Db::name('user_skills')
                ->alias('us')
                ->leftJoin('skills s', 'us.skill_id = s.id')
                ->where('us.user_id', $userId)
                ->where('us.status', 'approved')
                ->field('s.name, s.category, us.level, us.certificate_url')
                ->select()
                ->toArray();

            $userInfo['skills'] = $skills;

            return $this->success($userInfo);
        } catch (\Exception $e) {
            return $this->fail('获取用户信息失败：' . $e->getMessage());
        }
    }

    /**
     * 更新用户信息
     */
    public function updateUserInfo()
    {
        $nickname = $this->request->post('nickname', '');
        $realName = $this->request->post('real_name', '');
        $phone = $this->request->post('phone', '');
        $workExperience = $this->request->post('work_experience', '');
        $selfIntroduction = $this->request->post('self_introduction', '');

        try {
            $userId = $this->getUserId();
            $updateData = [];

            if ($nickname) {
                $updateData['nickname'] = $nickname;
            }

            if ($realName) {
                $updateData['real_name'] = $realName;
            }

            if ($phone) {
                // 验证手机号格式
                if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
                    return $this->fail('手机号格式不正确');
                }

                // 检查手机号是否已被使用
                $existUser = Db::name('users')
                    ->where('phone', $phone)
                    ->where('id', '<>', $userId)
                    ->find();

                if ($existUser) {
                    return $this->fail('手机号已被使用');
                }

                $updateData['phone'] = $phone;
            }

            if ($workExperience) {
                $updateData['work_experience'] = $workExperience;
            }

            if ($selfIntroduction) {
                $updateData['self_introduction'] = $selfIntroduction;
            }

            if (!empty($updateData)) {
                $updateData['update_time'] = time();
                Db::name('users')->where('id', $userId)->update($updateData);
            }

            return $this->success([], '信息更新成功');
        } catch (\Exception $e) {
            return $this->fail('更新信息失败：' . $e->getMessage());
        }
    }

    /**
     * 更新头像
     */
    public function updateAvatar()
    {
        $avatar = $this->request->post('avatar', '');

        if (!$avatar) {
            return $this->fail('请上传头像');
        }

        try {
            $userId = $this->getUserId();

            Db::name('users')
                ->where('id', $userId)
                ->update([
                    'avatar' => $avatar,
                    'update_time' => time()
                ]);

            return $this->success(['avatar' => $avatar], '头像更新成功');
        } catch (\Exception $e) {
            return $this->fail('头像更新失败：' . $e->getMessage());
        }
    }

    /**
     * 获取用户统计信息
     */
    public function getUserStats()
    {
        try {
            $userId = $this->getUserId();

            // 任务统计
            $taskStats = Db::name('task_applications')
                ->where('user_id', $userId)
                ->field('status, COUNT(*) as count')
                ->group('status')
                ->select()
                ->toArray();

            $stats = [
                'total_applications' => 0,
                'pending_applications' => 0,
                'approved_applications' => 0,
                'completed_tasks' => 0,
                'cancelled_tasks' => 0,
                'in_progress_tasks' => 0
            ];

            foreach ($taskStats as $stat) {
                $stats['total_applications'] += $stat['count'];
                switch ($stat['status']) {
                    case 'pending':
                        $stats['pending_applications'] = $stat['count'];
                        break;
                    case 'approved':
                        $stats['approved_applications'] = $stat['count'];
                        break;
                    case 'completed':
                        $stats['completed_tasks'] = $stat['count'];
                        break;
                    case 'cancelled':
                        $stats['cancelled_tasks'] = $stat['count'];
                        break;
                    case 'in_progress':
                        $stats['in_progress_tasks'] = $stat['count'];
                        break;
                }
            }

            // 收入统计
            $incomeStats = Db::name('task_applications')
                ->where('user_id', $userId)
                ->where('status', 'completed')
                ->field('SUM(total_amount) as total_income, COUNT(*) as completed_count')
                ->find();

            $stats['total_income'] = $incomeStats['total_income'] ?? 0;
            $stats['completed_count'] = $incomeStats['completed_count'] ?? 0;

            // 评价统计
            $reviewStats = Db::name('task_reviews')
                ->where('worker_id', $userId)
                ->field('AVG(rating) as avg_rating, COUNT(*) as review_count')
                ->find();

            $stats['avg_rating'] = round($reviewStats['avg_rating'] ?? 0, 1);
            $stats['review_count'] = $reviewStats['review_count'] ?? 0;

            return $this->success($stats);
        } catch (\Exception $e) {
            return $this->fail('获取统计信息失败：' . $e->getMessage());
        }
    }
} 