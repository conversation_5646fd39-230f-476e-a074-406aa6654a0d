<?php

namespace app\http\middleware\staff;


use app\Request;
use app\services\staff\OutAccountServices;
use app\services\staff\OutInterfaceServices;
use crmeb\interfaces\MiddlewareInterface;
use think\facade\Config;

/**
 * Class AuthTokenMiddleware
 * @package app\http\middleware\staff
 */
class AuthTokenMiddleware implements MiddlewareInterface
{

    /**
     * @param Request $request
     * @param \Closure $next
     * @throws \Psr\SimpleCache\InvalidArgumentException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function handle(Request $request, \Closure $next)
    {
        $authInfo = null;
        $token = trim(ltrim($request->header(Config::get('cookie.token_name', 'Authori-zation')), 'Bearer'));
        /** @var OutAccountServices $services */
        $services = app()->make(OutAccountServices::class);
        $outInfo = $services->parseToken($token);

        $request->outId = (int)$outInfo['out_id'];

        $request->outInfo = $outInfo;

        /** @var OutInterfaceServices $outInterfaceServices */
        $outInterfaceServices = app()->make(OutInterfaceServices::class);
        $outInterfaceServices->verifyAuth($request);

        return $next($request);
    }
}
