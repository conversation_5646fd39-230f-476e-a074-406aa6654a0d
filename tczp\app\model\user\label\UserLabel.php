<?php

namespace app\model\user\label;

use crmeb\basic\BaseModel;
use crmeb\traits\ModelTrait;
use think\Model;

/**
 * 用户标签
 * Class UserLabel
 * @package app\model\user\label
 */
class UserLabel extends BaseModel
{
    use ModelTrait;

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'user_label';

    /**
     * 标签分类
     * @param \think\Model $query
     * @param $value
     */
    public function searchLabelCateAttr($query, $value)
    {
        if (is_array($value)) {
            $query->whereIn('label_cate', $value);
        } else {
            if ($value) {
                $query->where('label_cate', $value);
            }
        }
    }

    /**
     * type搜索器
     * @param Model $query
     * @param $value
     */
    public function searchTypeAttr($query, $value)
    {
        if ($value) $query->where('type', $value);
    }

    /**
     * store_id搜索器
     * @param Model $query
     * @param $value
     */
    public function searchStoreIdAttr($query, $value)
    {
        if ($value !== '') $query->where('store_id', $value);
    }

    /**
     * ids搜索器
     * @param Model $query
     * @param $value
     */
    public function searchIdsAttr($query, $value)
    {
        if ($value) $query->whereIn('id', $value);
    }

    /**
     * @param $query
     * @param $value
     */
    public function searchNotTagIdAttr($query, $value)
    {
        $query->where(function ($query) {
            $query->whereNull('tag_id')->whereOr('tag_id', '');
        });
    }
}
