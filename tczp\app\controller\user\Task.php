<?php
declare(strict_types=1);

namespace app\controller\user;

use app\controller\BaseController;
use app\services\user\UserTaskServices;
use app\services\user\EnterpriseAuthServices;
use think\Response;

/**
 * 用户任务管理控制器 - 优化版
 * Class Task
 * @package app\controller\user
 */
class Task extends BaseController
{
    /**
     * 用户任务服务
     * @var UserTaskServices
     */
    protected $userTaskService;
    
    /**
     * 企业认证服务
     * @var EnterpriseAuthServices
     */
    protected $enterpriseAuthService;
    
    /**
     * 初始化
     */
    protected function initialize(): void
    {
        parent::initialize();
        $this->userTaskService = app()->make(UserTaskServices::class);
        $this->enterpriseAuthService = app()->make(EnterpriseAuthServices::class);
    }
    
    /**
     * 获取用户ID
     * @return int
     */
    private function getUserId(): int
    {
        return $this->request->uid ?? 0;
    }

    /**
     * 发布任务
     * @return Response
     */
    public function publishTask(): Response
    {
        try {
            $data = $this->request->post();
            
            // 验证必填字段
            $required = ['task_title', 'task_description', 'task_category_id', 'hourly_rate'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    return $this->fail("请填写{$field}");
                }
            }
            
            $uid = $this->getUserId();
            
            // 发布任务
            $taskId = $this->userTaskService->publishTask($uid, $data);
            
            if ($taskId) {
                return $this->success(['task_id' => $taskId], '任务发布成功');
            } else {
                return $this->fail('任务发布失败');
            }
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取我发布的任务列表
     * @return Response
     */
    public function getMyTasks(): Response
    {
        try {
            $page = (int)$this->request->get('page', 1);
            $limit = (int)$this->request->get('limit', 20);
            
            $where = [
                'status' => $this->request->get('status', ''),
                'task_type' => $this->request->get('task_type', ''),
                'start_time' => $this->request->get('start_time', ''),
                'end_time' => $this->request->get('end_time', '')
            ];
            
            // 过滤空值
            $where = array_filter($where, function($value) {
                return $value !== '';
            });
            
            $uid = $this->getUserId();
            $result = $this->userTaskService->getMyTasks($uid, $where, $page, $limit);
            
            return $this->success($result);
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取任务详情
     * @return Response
     */
    public function getTaskDetail(): Response
    {
        try {
            $taskId = (int)$this->request->param('id', 0);
            
            if (!$taskId) {
                return $this->fail('任务ID不能为空');
            }
            
            // 这里应该调用任务详情服务
            $detail = \think\facade\Db::name('tasks')
                ->alias('t')
                ->join('task_categories tc', 't.task_category_id = tc.id', 'left')
                ->join('user u', 't.user_id = u.uid')
                ->where('t.id', $taskId)
                ->field([
                    't.*',
                    'tc.category_name',
                    'u.nickname as publisher_name', 'u.avatar as publisher_avatar'
                ])
                ->find();
                
            if (!$detail) {
                return $this->fail('任务不存在');
            }
            
            // 处理JSON字段
            $detail['required_skills'] = json_decode($detail['required_skills'] ?: '[]', true);
            $detail['worker_requirements'] = json_decode($detail['worker_requirements'] ?: '{}', true);
            $detail['preferred_workers'] = json_decode($detail['preferred_workers'] ?: '[]', true);
            
            return $this->success($detail);
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 更新任务
     * @return Response
     */
    public function updateTask(): Response
    {
        try {
            $taskId = (int)$this->request->param('id', 0);
            $data = $this->request->put();
            
            if (!$taskId) {
                return $this->fail('任务ID不能为空');
            }
            
            $uid = $this->getUserId();
            
            // 检查任务是否存在且属于该用户
            $task = \think\facade\Db::name('tasks')
                ->where('id', $taskId)
                ->where('user_id', $uid)
                ->find();
                
            if (!$task) {
                return $this->fail('任务不存在或无权操作');
            }
            
            if (!in_array($task['status'], ['published', 'draft'])) {
                return $this->fail('任务状态不允许修改');
            }
            
            // 更新任务
            $updateData = array_merge($data, [
                'update_time' => time()
            ]);
            
            $result = \think\facade\Db::name('tasks')
                ->where('id', $taskId)
                ->update($updateData);
                
            if ($result) {
                return $this->success([], '任务更新成功');
            } else {
                return $this->fail('更新失败');
            }
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 取消任务
     * @return Response
     */
    public function cancelTask(): Response
    {
        try {
            $taskId = (int)$this->request->param('id', 0);
            $reason = $this->request->delete('reason', '');
            
            if (!$taskId) {
                return $this->fail('任务ID不能为空');
            }
            
            $uid = $this->getUserId();
            
            $result = $this->userTaskService->cancelTask($taskId, $uid, $reason);
            
            if ($result) {
                return $this->success([], '任务已取消');
            } else {
                return $this->fail('取消失败');
            }
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取任务申请列表
     * @return Response
     */
    public function getTaskApplications(): Response
    {
        try {
            $taskId = (int)$this->request->param('id', 0);
            $page = (int)$this->request->get('page', 1);
            $limit = (int)$this->request->get('limit', 20);
            
            if (!$taskId) {
                return $this->fail('任务ID不能为空');
            }
            
            $result = $this->userTaskService->getTaskApplications($taskId, $page, $limit);
            
            return $this->success($result);
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 批准申请
     * @return Response
     */
    public function approveApplication(): Response
    {
        try {
            $applicationId = (int)$this->request->param('id', 0);
            
            if (!$applicationId) {
                return $this->fail('申请ID不能为空');
            }
            
            $result = $this->userTaskService->handleTaskApplication($applicationId, 'approve');
            
            if ($result) {
                return $this->success([], '申请已批准');
            } else {
                return $this->fail('操作失败');
            }
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 拒绝申请
     * @return Response
     */
    public function rejectApplication(): Response
    {
        try {
            $applicationId = (int)$this->request->param('id', 0);
            $reason = $this->request->put('reason', '');
            
            if (!$applicationId) {
                return $this->fail('申请ID不能为空');
            }
            
            $result = $this->userTaskService->handleTaskApplication($applicationId, 'reject', $reason);
            
            if ($result) {
                return $this->success([], '申请已拒绝');
            } else {
                return $this->fail('操作失败');
            }
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 确认任务完成
     * @return Response
     */
    public function confirmTask(): Response
    {
        try {
            $taskId = (int)$this->request->param('id', 0);
            $data = $this->request->put();
            
            if (!$taskId) {
                return $this->fail('任务ID不能为空');
            }
            
            $uid = $this->getUserId();
            
            $result = $this->userTaskService->confirmTaskCompletion($taskId, $uid, $data);
            
            if ($result) {
                return $this->success([], '任务确认完成');
            } else {
                return $this->fail('确认失败');
            }
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取任务模板列表
     * @return Response
     */
    public function getTaskTemplates(): Response
    {
        try {
            $page = (int)$this->request->get('page', 1);
            $limit = (int)$this->request->get('limit', 20);
            
            $where = [
                'category_id' => $this->request->get('category_id', 0),
                'keyword' => $this->request->get('keyword', '')
            ];
            
            // 过滤空值
            $where = array_filter($where, function($value) {
                return $value !== '' && $value !== 0;
            });
            
            $uid = $this->getUserId();
            $result = $this->userTaskService->getTaskTemplates($uid, $where, $page, $limit);
            
            return $this->success($result);
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 保存任务模板
     * @return Response
     */
    public function saveTaskTemplate(): Response
    {
        try {
            $data = $this->request->post();
            
            // 验证必填字段
            $required = ['template_name', 'template_data', 'category_id'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    return $this->fail("请填写{$field}");
                }
            }
            
            $uid = $this->getUserId();
            
            $result = $this->userTaskService->saveTaskTemplate($uid, $data);
            
            if ($result) {
                return $this->success([], '模板保存成功');
            } else {
                return $this->fail('保存失败');
            }
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 使用任务模板
     * @return Response
     */
    public function useTaskTemplate(): Response
    {
        try {
            $templateId = (int)$this->request->param('id', 0);
            
            if (!$templateId) {
                return $this->fail('模板ID不能为空');
            }
            
            // 获取模板数据
            $template = \think\facade\Db::name('task_templates')
                ->where('id', $templateId)
                ->find();
                
            if (!$template) {
                return $this->fail('模板不存在');
            }
            
            // 更新使用次数
            \think\facade\Db::name('task_templates')
                ->where('id', $templateId)
                ->inc('use_count', 1);
            
            // 返回模板数据
            $templateData = json_decode($template['template_data'], true) ?: [];
            
            return $this->success($templateData, '模板加载成功');
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取任务列表（公开接口）
     * @return Response
     */
    public function getTaskList(): Response
    {
        try {
            $page = (int)$this->request->get('page', 1);
            $limit = (int)$this->request->get('limit', 20);
            
            $where = [
                'category_id' => $this->request->get('category_id', 0),
                'city' => $this->request->get('city', ''),
                'min_price' => $this->request->get('min_price', 0),
                'max_price' => $this->request->get('max_price', 0),
                'difficulty_level' => $this->request->get('difficulty_level', 0),
                'keyword' => $this->request->get('keyword', '')
            ];
            
            // 过滤空值
            $where = array_filter($where, function($value) {
                return $value !== '' && $value !== 0;
            });
            
            // 这里应该调用公共任务列表服务
            $query = \think\facade\Db::name('tasks')
                ->alias('t')
                ->join('user u', 't.user_id = u.uid')
                ->join('task_categories tc', 't.task_category_id = tc.id', 'left')
                ->where('t.status', 'published');
                
            // 应用筛选条件
            if (!empty($where['category_id'])) {
                $query->where('t.task_category_id', $where['category_id']);
            }
            
            if (!empty($where['city'])) {
                $query->where('t.task_location', 'like', "%{$where['city']}%");
            }
            
            if (!empty($where['keyword'])) {
                $query->where(function ($q) use ($where) {
                    $q->where('t.task_title', 'like', "%{$where['keyword']}%")
                      ->whereOr('t.task_description', 'like', "%{$where['keyword']}%");
                });
            }
            
            $total = $query->count();
            $list = $query->field([
                    't.id', 't.task_title', 't.task_description', 't.task_location',
                    't.hourly_rate', 't.required_workers', 't.difficulty_level',
                    't.create_time', 't.deadline',
                    'u.nickname as publisher_name', 'u.avatar as publisher_avatar',
                    'tc.category_name'
                ])
                ->order('t.create_time desc')
                ->page($page, $limit)
                ->select()
                ->toArray();
                
            $result = [
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($total / $limit)
            ];
            
            return $this->success($result);
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}
