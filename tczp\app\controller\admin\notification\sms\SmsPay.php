<?php
namespace app\controller\admin\notification\sms;

use app\controller\admin\AuthController;
use crmeb\exceptions\AdminException;
use crmeb\services\{sms\Sms, SystemConfigService};

/**
 * 短信购买
 * Class SmsPay
 * @package app\controller\admin\v1\notification\sms
 */
class SmsPay extends AuthController
{
    /**
     * @var Sms
     */
    protected $smsHandle;

    public function initialize()
    {
        parent::initialize();
        $data = SystemConfigService::more(['sms_account', 'sms_token', 'site_url']);
        $this->smsHandle = new Sms('yunxin', $data);
        if (!$this->smsHandle->isLogin()) {
            throw new AdminException('请先填写短息配置');
        }
    }

    /**
     *  获取账号信息
     */
    public function number()
    {
        $countInfo = $this->smsHandle->count();
        if ($countInfo['status'] == 400) return $this->fail($countInfo['msg']);
        $info['account'] = sys_config('sms_account');
        $info['number'] = $countInfo['data']['number'];
        $info['send_total'] = $countInfo['data']['send_total'];
        return $this->success($info);
    }

    /**
     *  获取支付套餐
     */
    public function price()
    {
        [$page, $limit] = $this->request->getMore([
            ['page', 1],
            ['limit', 20],
        ], true);
        $mealInfo = $this->smsHandle->meal($page, $limit);
        if ($mealInfo['status'] == 400) return $this->fail($mealInfo['msg']);
        return $this->success($mealInfo['data']);
    }

    /**
     * 获取支付码
     */
    public function pay()
    {
        [$payType, $mealId, $price] = $this->request->postMore([
            ['payType', 'weixin'],
            ['mealId', 0],
            ['price', 0],
        ], true);
        $payInfo = $this->smsHandle->pay($payType, $mealId, $price, $this->adminId);
        if ($payInfo['status'] == 400) return $this->fail($payInfo['msg']);
        return $this->success($payInfo['data']);
    }
}
