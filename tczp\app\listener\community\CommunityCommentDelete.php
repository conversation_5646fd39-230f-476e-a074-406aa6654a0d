<?php
namespace app\listener\community;

use app\jobs\activity\StorePromotionsJob;
use app\jobs\community\CommunityJob;
use app\services\order\StoreOrderInvoiceServices;
use crmeb\interfaces\ListenerInterface;

/**
 * 订单取消删除事件
 * Class Cancel
 * @package app\listener\order
 */
class CommunityCommentDelete implements ListenerInterface
{
    /**
     * 帖子删除事件
     * @param $event
     */
    public function handle($event): void
    {
        [$id] = $event;
        if ($id) {
            CommunityJob::dispatchDo('CommunityCommentDelete', [$id]);
        }
    }
}
