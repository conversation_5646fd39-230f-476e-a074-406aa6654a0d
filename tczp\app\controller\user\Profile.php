<?php
declare(strict_types=1);

namespace app\controller\user;

use app\controller\BaseControllerOptimized;
use app\services\user\UserProfileServices;
use app\services\user\EnterpriseAuthServices;
use think\Response;

/**
 * 用户个人中心控制器 - 完整版
 * Class Profile
 * @package app\controller\user
 */
class Profile extends BaseControllerOptimized
{
    /**
     * 用户档案服务
     * @var UserProfileServices
     */
    protected $userProfileService;
    
    /**
     * 企业认证服务
     * @var EnterpriseAuthServices
     */
    protected $enterpriseAuthService;
    
    /**
     * 初始化
     */
    protected function initialize(): void
    {
        parent::initialize();
        $this->userProfileService = app()->make(UserProfileServices::class);
        $this->enterpriseAuthService = app()->make(EnterpriseAuthServices::class);
    }

    /**
     * 获取个人档案
     * @return Response
     */
    public function getProfile(): Response
    {
        try {
            $profile = $this->userProfileService->getUserProfile($this->uid);
            return $this->success($profile);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 更新个人档案
     * @return Response
     */
    public function updateProfile(): Response
    {
        try {
            $data = $this->getPost();
            
            // 验证必填字段
            $this->validate($data, [
                'nickname' => 'max:50',
                'real_name' => 'max:50',
                'phone' => 'mobile',
                'email' => 'email'
            ]);
            
            $result = $this->userProfileService->updateProfile($this->uid, $data);
            
            if ($result) {
                return $this->success([], '档案更新成功');
            } else {
                return $this->fail('更新失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 上传头像
     * @return Response
     */
    public function uploadAvatar(): Response
    {
        try {
            $file = $this->request->file('avatar');
            
            if (!$file) {
                return $this->fail('请选择头像文件');
            }
            
            // 验证文件
            $validate = [
                'size' => 2 * 1024 * 1024, // 2MB
                'ext' => 'jpg,jpeg,png,gif'
            ];
            
            if (!$file->check($validate)) {
                return $this->fail($file->getError());
            }
            
            // 上传文件
            $saveName = \think\facade\Filesystem::disk('public')->putFile('avatar', $file);
            
            if (!$saveName) {
                return $this->fail('文件上传失败');
            }
            
            $avatarUrl = '/storage/' . $saveName;
            
            // 更新用户头像
            $result = $this->userProfileService->updateAvatar($this->uid, $avatarUrl);
            
            if ($result) {
                return $this->success(['avatar' => $avatarUrl], '头像上传成功');
            } else {
                return $this->fail('头像更新失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取用户统计
     * @return Response
     */
    public function getStats(): Response
    {
        try {
            $period = $this->getParam('period', 'month');
            $stats = $this->userProfileService->getUserStats($this->uid, $period);
            return $this->success($stats);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取统计图表数据
     * @return Response
     */
    public function getStatsChart(): Response
    {
        try {
            $period = $this->getParam('period', 'month');
            $type = $this->getParam('type', 'task');
            
            $chartData = $this->userProfileService->getStatsChart($this->uid, $period, $type);
            return $this->success($chartData);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取设置信息
     * @return Response
     */
    public function getSettings(): Response
    {
        try {
            $settings = $this->userProfileService->getSettings($this->uid);
            return $this->success($settings);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 更新设置
     * @return Response
     */
    public function updateSettings(): Response
    {
        try {
            $data = $this->getPost();
            
            $result = $this->userProfileService->updateSettings($this->uid, $data);
            
            if ($result) {
                return $this->success([], '设置更新成功');
            } else {
                return $this->fail('更新失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取通知设置
     * @return Response
     */
    public function getNotificationSettings(): Response
    {
        try {
            $settings = $this->userProfileService->getNotificationSettings($this->uid);
            return $this->success($settings);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 更新通知设置
     * @return Response
     */
    public function updateNotificationSettings(): Response
    {
        try {
            $data = $this->getPost();
            
            $result = $this->userProfileService->updateNotificationSettings($this->uid, $data);
            
            if ($result) {
                return $this->success([], '通知设置更新成功');
            } else {
                return $this->fail('更新失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 修改密码
     * @return Response
     */
    public function changePassword(): Response
    {
        try {
            $data = $this->getPost();
            
            // 验证必填字段
            $this->validate($data, [
                'old_password' => 'require',
                'new_password' => 'require|min:6|max:20',
                'confirm_password' => 'require|confirm:new_password'
            ]);
            
            $result = $this->userProfileService->changePassword($this->uid, $data['old_password'], $data['new_password']);
            
            if ($result) {
                return $this->success([], '密码修改成功');
            } else {
                return $this->fail('密码修改失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 绑定手机号
     * @return Response
     */
    public function bindPhone(): Response
    {
        try {
            $data = $this->getPost();
            
            // 验证必填字段
            $this->validate($data, [
                'phone' => 'require|mobile',
                'verify_code' => 'require|length:6'
            ]);
            
            $result = $this->userProfileService->bindPhone($this->uid, $data['phone'], $data['verify_code']);
            
            if ($result) {
                return $this->success([], '手机号绑定成功');
            } else {
                return $this->fail('绑定失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 绑定邮箱
     * @return Response
     */
    public function bindEmail(): Response
    {
        try {
            $data = $this->getPost();
            
            // 验证必填字段
            $this->validate($data, [
                'email' => 'require|email',
                'verify_code' => 'require|length:6'
            ]);
            
            $result = $this->userProfileService->bindEmail($this->uid, $data['email'], $data['verify_code']);
            
            if ($result) {
                return $this->success([], '邮箱绑定成功');
            } else {
                return $this->fail('绑定失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取收藏列表
     * @return Response
     */
    public function getFavorites(): Response
    {
        try {
            [$page, $limit] = $this->getPageParams();
            $type = $this->getParam('type', 'worker'); // worker, task
            
            $result = $this->userProfileService->getFavorites($this->uid, $type, $page, $limit);
            return $this->successPage($result['list'], $result['total'], $page, $limit);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 添加收藏
     * @return Response
     */
    public function addFavorite(): Response
    {
        try {
            $data = $this->getPost();
            
            // 验证必填字段
            $this->validate($data, [
                'type' => 'require|in:worker,task',
                'target_id' => 'require|number'
            ]);
            
            $result = $this->userProfileService->addFavorite($this->uid, $data['type'], $data['target_id']);
            
            if ($result) {
                return $this->success([], '收藏成功');
            } else {
                return $this->fail('收藏失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 取消收藏
     * @return Response
     */
    public function removeFavorite(): Response
    {
        try {
            $favoriteId = (int)$this->getParam('id', 0);
            
            if (!$favoriteId) {
                return $this->fail('收藏ID不能为空');
            }
            
            $result = $this->userProfileService->removeFavorite($this->uid, $favoriteId);
            
            if ($result) {
                return $this->success([], '取消收藏成功');
            } else {
                return $this->fail('操作失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取地址列表
     * @return Response
     */
    public function getAddresses(): Response
    {
        try {
            $addresses = $this->userProfileService->getAddresses($this->uid);
            return $this->success($addresses);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 添加地址
     * @return Response
     */
    public function addAddress(): Response
    {
        try {
            $data = $this->getPost();
            
            // 验证必填字段
            $this->validate($data, [
                'contact_name' => 'require|max:50',
                'contact_phone' => 'require|mobile',
                'province' => 'require',
                'city' => 'require',
                'district' => 'require',
                'address' => 'require|max:200'
            ]);
            
            $result = $this->userProfileService->addAddress($this->uid, $data);
            
            if ($result) {
                return $this->success([], '地址添加成功');
            } else {
                return $this->fail('添加失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 更新地址
     * @return Response
     */
    public function updateAddress(): Response
    {
        try {
            $addressId = (int)$this->getParam('id', 0);
            $data = $this->getPost();
            
            if (!$addressId) {
                return $this->fail('地址ID不能为空');
            }
            
            $result = $this->userProfileService->updateAddress($this->uid, $addressId, $data);
            
            if ($result) {
                return $this->success([], '地址更新成功');
            } else {
                return $this->fail('更新失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 删除地址
     * @return Response
     */
    public function deleteAddress(): Response
    {
        try {
            $addressId = (int)$this->getParam('id', 0);
            
            if (!$addressId) {
                return $this->fail('地址ID不能为空');
            }
            
            $result = $this->userProfileService->deleteAddress($this->uid, $addressId);
            
            if ($result) {
                return $this->success([], '地址删除成功');
            } else {
                return $this->fail('删除失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 设置默认地址
     * @return Response
     */
    public function setDefaultAddress(): Response
    {
        try {
            $addressId = (int)$this->getParam('id', 0);
            
            if (!$addressId) {
                return $this->fail('地址ID不能为空');
            }
            
            $result = $this->userProfileService->setDefaultAddress($this->uid, $addressId);
            
            if ($result) {
                return $this->success([], '默认地址设置成功');
            } else {
                return $this->fail('设置失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}
