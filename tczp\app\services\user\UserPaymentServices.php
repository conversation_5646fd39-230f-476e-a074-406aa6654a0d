<?php
declare(strict_types=1);

namespace app\services\user;

use app\services\BaseServices;
use think\facade\Db;
use think\facade\Cache;

/**
 * 用户支付服务类
 * Class UserPaymentServices
 * @package app\services\user
 */
class UserPaymentServices extends BaseServices
{
    /**
     * 创建支付订单
     * @param int $taskId
     * @param int $uid
     * @param int $workerId
     * @param string $paymentType
     * @param array $data
     * @return array
     */
    public function createPaymentOrder(int $taskId, int $uid, int $workerId, string $paymentType, array $data = []): array
    {
        // 验证支付类型
        $allowedTypes = ['deposit', 'final', 'full'];
        if (!in_array($paymentType, $allowedTypes)) {
            throw new \Exception('无效的支付类型');
        }
        
        try {
            Db::startTrans();
            
            // 获取任务信息
            $task = Db::name('tasks')->where('id', $taskId)->find();
            if (!$task) {
                throw new \Exception('任务不存在');
            }
            
            if ($task['user_id'] != $uid) {
                throw new \Exception('无权操作该任务');
            }
            
            // 计算支付金额
            $amounts = $this->calculatePaymentAmount($task, $paymentType, $data);
            
            // 生成订单号
            $orderNo = $this->generateOrderNo();
            
            // 创建支付订单
            $orderData = [
                'order_no' => $orderNo,
                'task_id' => $taskId,
                'user_id' => $uid,
                'worker_id' => $workerId,
                'payment_type' => $paymentType,
                'amount' => $amounts['amount'],
                'platform_fee' => $amounts['platform_fee'],
                'actual_amount' => $amounts['actual_amount'],
                'payment_method' => $data['payment_method'] ?? 'wechat',
                'status' => 'pending',
                'create_time' => time(),
                'update_time' => time()
            ];
            
            $orderId = Db::name('payment_orders')->insertGetId($orderData);
            
            Db::commit();
            
            return [
                'order_id' => $orderId,
                'order_no' => $orderNo,
                'amount' => $amounts['actual_amount'],
                'payment_type' => $paymentType
            ];
            
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 处理支付回调
     * @param string $orderNo
     * @param string $transactionId
     * @param array $data
     * @return bool
     */
    public function handlePaymentCallback(string $orderNo, string $transactionId, array $data = []): bool
    {
        try {
            Db::startTrans();
            
            // 获取订单信息
            $order = Db::name('payment_orders')->where('order_no', $orderNo)->find();
            if (!$order) {
                throw new \Exception('订单不存在');
            }
            
            if ($order['status'] !== 'pending') {
                throw new \Exception('订单状态异常');
            }
            
            // 更新订单状态
            Db::name('payment_orders')
                ->where('id', $order['id'])
                ->update([
                    'status' => 'paid',
                    'transaction_id' => $transactionId,
                    'pay_time' => time(),
                    'update_time' => time()
                ]);
            
            // 更新任务状态
            $this->updateTaskStatusAfterPayment($order);
            
            // 更新用户统计
            Db::name('user')
                ->where('uid', $order['user_id'])
                ->inc('total_spent', $order['actual_amount'])
                ->update(['last_active_time' => time()]);
            
            // 如果是企业用户，更新企业统计
            $enterpriseAuthService = app()->make(EnterpriseAuthServices::class);
            if ($enterpriseAuthService->isEnterprise($order['user_id'])) {
                $enterpriseInfo = $enterpriseAuthService->getEnterpriseInfo($order['user_id']);
                if ($enterpriseInfo) {
                    Db::name('enterprise_profiles')
                        ->where('id', $enterpriseInfo['id'])
                        ->inc('total_amount', $order['actual_amount'])
                        ->update(['update_time' => time()]);
                }
            }
            
            Db::commit();
            
            return true;
            
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 申请退款
     * @param int $orderId
     * @param int $uid
     * @param string $reason
     * @return bool
     */
    public function requestRefund(int $orderId, int $uid, string $reason): bool
    {
        try {
            Db::startTrans();
            
            // 获取订单信息
            $order = Db::name('payment_orders')->where('id', $orderId)->find();
            if (!$order) {
                throw new \Exception('订单不存在');
            }
            
            if ($order['user_id'] != $uid) {
                throw new \Exception('无权操作该订单');
            }
            
            if ($order['status'] !== 'paid') {
                throw new \Exception('订单状态不允许退款');
            }
            
            // 检查任务状态是否允许退款
            $task = Db::name('tasks')->where('id', $order['task_id'])->find();
            if ($task && in_array($task['status'], ['completed', 'confirmed'])) {
                throw new \Exception('任务已完成，不允许退款');
            }
            
            // 更新订单状态
            Db::name('payment_orders')
                ->where('id', $orderId)
                ->update([
                    'status' => 'refunded',
                    'refund_reason' => $reason,
                    'refund_time' => time(),
                    'update_time' => time()
                ]);
            
            // 这里应该调用第三方支付接口进行实际退款
            // $this->processActualRefund($order);
            
            Db::commit();
            
            return true;
            
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 获取支付订单列表
     * @param int $uid
     * @param array $where
     * @param int $page
     * @param int $limit
     * @return array
     */
    public function getPaymentOrders(int $uid, array $where = [], int $page = 1, int $limit = 20): array
    {
        $query = Db::name('payment_orders')
            ->alias('po')
            ->join('tasks t', 'po.task_id = t.id')
            ->join('user u', 'po.worker_id = u.uid', 'left')
            ->where('po.user_id', $uid);
            
        // 状态筛选
        if (!empty($where['status'])) {
            $query->where('po.status', $where['status']);
        }
        
        // 支付类型筛选
        if (!empty($where['payment_type'])) {
            $query->where('po.payment_type', $where['payment_type']);
        }
        
        // 时间筛选
        if (!empty($where['start_time']) && !empty($where['end_time'])) {
            $query->whereBetween('po.create_time', [$where['start_time'], $where['end_time']]);
        }
        
        $total = $query->count();
        $list = $query->field([
                'po.id', 'po.order_no', 'po.payment_type', 'po.amount', 'po.platform_fee',
                'po.actual_amount', 'po.payment_method', 'po.status', 'po.create_time',
                'po.pay_time', 'po.refund_time',
                't.task_title', 't.task_location',
                'u.nickname as worker_name'
            ])
            ->order('po.create_time desc')
            ->page($page, $limit)
            ->select()
            ->toArray();
            
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    }
    
    /**
     * 获取支付统计
     * @param int $uid
     * @param string $period
     * @return array
     */
    public function getPaymentStats(int $uid, string $period = 'month'): array
    {
        // 计算时间范围
        $timeRange = $this->getTimeRange($period);
        
        // 基础统计
        $basicStats = Db::name('payment_orders')
            ->where('user_id', $uid)
            ->where('status', 'paid')
            ->whereBetween('pay_time', $timeRange)
            ->field([
                'COUNT(*) as total_orders',
                'SUM(actual_amount) as total_amount',
                'AVG(actual_amount) as avg_amount',
                'SUM(platform_fee) as total_fee'
            ])
            ->find();
            
        // 按支付类型统计
        $typeStats = Db::name('payment_orders')
            ->where('user_id', $uid)
            ->where('status', 'paid')
            ->whereBetween('pay_time', $timeRange)
            ->field('payment_type, COUNT(*) as count, SUM(actual_amount) as amount')
            ->group('payment_type')
            ->select()
            ->toArray();
            
        // 按月份统计（用于图表）
        $monthlyStats = Db::name('payment_orders')
            ->where('user_id', $uid)
            ->where('status', 'paid')
            ->whereBetween('pay_time', $timeRange)
            ->field('FROM_UNIXTIME(pay_time, "%Y-%m") as month, COUNT(*) as count, SUM(actual_amount) as amount')
            ->group('month')
            ->order('month asc')
            ->select()
            ->toArray();
            
        return [
            'basic_stats' => $basicStats ?: [],
            'type_stats' => $typeStats,
            'monthly_stats' => $monthlyStats
        ];
    }
    
    /**
     * 创建结算记录
     * @param int $taskId
     * @param array $data
     * @return bool
     */
    public function createSettlementRecord(int $taskId, array $data = []): bool
    {
        try {
            Db::startTrans();
            
            // 获取任务和支付信息
            $task = Db::name('tasks')->where('id', $taskId)->find();
            if (!$task) {
                throw new \Exception('任务不存在');
            }
            
            $payments = Db::name('payment_orders')
                ->where('task_id', $taskId)
                ->where('status', 'paid')
                ->select()
                ->toArray();
                
            if (empty($payments)) {
                throw new \Exception('没有有效的支付记录');
            }
            
            // 计算结算金额
            $totalAmount = array_sum(array_column($payments, 'actual_amount'));
            $platformFee = array_sum(array_column($payments, 'platform_fee'));
            $workerAmount = $totalAmount - $platformFee;
            
            // 生成结算单号
            $settlementNo = $this->generateSettlementNo();
            
            // 创建结算记录
            $settlementData = [
                'settlement_no' => $settlementNo,
                'task_id' => $taskId,
                'user_id' => $task['user_id'],
                'worker_id' => $task['assigned_worker_id'],
                'total_amount' => $totalAmount,
                'platform_fee' => $platformFee,
                'worker_amount' => $workerAmount,
                'tax_amount' => $data['tax_amount'] ?? 0,
                'actual_amount' => $workerAmount - ($data['tax_amount'] ?? 0),
                'settlement_type' => $data['settlement_type'] ?? 'auto',
                'status' => 'pending',
                'remark' => $data['remark'] ?? '',
                'create_time' => time(),
                'update_time' => time()
            ];
            
            Db::name('settlement_records')->insert($settlementData);
            
            Db::commit();
            
            return true;
            
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 计算支付金额
     * @param array $task
     * @param string $paymentType
     * @param array $data
     * @return array
     */
    private function calculatePaymentAmount(array $task, string $paymentType, array $data): array
    {
        $hourlyRate = $task['hourly_rate'];
        $estimatedDuration = $data['work_hours'] ?? $task['estimated_duration'];
        $baseAmount = $hourlyRate * $estimatedDuration;
        
        switch ($paymentType) {
            case 'deposit':
                $amount = $baseAmount * ($task['deposit_rate'] / 100);
                break;
            case 'final':
                $depositAmount = $baseAmount * ($task['deposit_rate'] / 100);
                $amount = $baseAmount - $depositAmount;
                break;
            case 'full':
                $amount = $baseAmount;
                break;
            default:
                throw new \Exception('无效的支付类型');
        }
        
        // 计算平台费用
        $platformFeeRate = 5; // 5% 平台费用，应该从配置中读取
        $platformFee = $amount * ($platformFeeRate / 100);
        $actualAmount = $amount + $platformFee;
        
        return [
            'amount' => $amount,
            'platform_fee' => $platformFee,
            'actual_amount' => $actualAmount
        ];
    }
    
    /**
     * 更新任务状态（支付后）
     * @param array $order
     */
    private function updateTaskStatusAfterPayment(array $order): void
    {
        $task = Db::name('tasks')->where('id', $order['task_id'])->find();
        if (!$task) {
            return;
        }
        
        // 根据支付类型更新任务状态
        switch ($order['payment_type']) {
            case 'deposit':
                if ($task['status'] === 'assigned') {
                    Db::name('tasks')
                        ->where('id', $order['task_id'])
                        ->update([
                            'status' => 'deposit_paid',
                            'update_time' => time()
                        ]);
                }
                break;
            case 'full':
                if ($task['status'] === 'assigned') {
                    Db::name('tasks')
                        ->where('id', $order['task_id'])
                        ->update([
                            'status' => 'paid',
                            'update_time' => time()
                        ]);
                }
                break;
        }
    }
    
    /**
     * 生成订单号
     * @return string
     */
    private function generateOrderNo(): string
    {
        return 'PAY' . date('ymdHis') . mt_rand(1000, 9999);
    }
    
    /**
     * 生成结算单号
     * @return string
     */
    private function generateSettlementNo(): string
    {
        return 'SET' . date('ymdHis') . mt_rand(1000, 9999);
    }
    
    /**
     * 获取时间范围
     * @param string $period
     * @return array
     */
    private function getTimeRange(string $period): array
    {
        $now = time();
        
        switch ($period) {
            case 'week':
                return [strtotime('-1 week', $now), $now];
            case 'month':
                return [strtotime('-1 month', $now), $now];
            case 'quarter':
                return [strtotime('-3 months', $now), $now];
            case 'year':
                return [strtotime('-1 year', $now), $now];
            default:
                return [strtotime('-1 month', $now), $now];
        }
    }
}
