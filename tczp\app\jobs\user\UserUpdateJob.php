<?php

namespace app\jobs\user;


use app\services\user\UserServices;
use crmeb\basic\BaseJobs;
use crmeb\traits\QueueTrait;

/**
 * Class UserUpdateJob
 * @package app\jobs\user
 */
class UserUpdateJob extends BaseJobs
{

    use QueueTrait;


    /**
     * @param $uid
     * @param $realName
     * @param $userPhone
     * @return bool
     */
    public function updateRealName($uid, $realName, $userPhone)
    {
        /** @var UserServices $userService */
        $userService = app()->make(UserServices::class);
        $userService->update(['uid' => $uid], ['real_name' => $realName, 'record_phone' => $userPhone]);

        return true;
    }

}
