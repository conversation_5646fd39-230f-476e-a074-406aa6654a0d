<?php

namespace app\controller\api;

use app\BaseController;
use app\services\AIRiskAssessmentService;
use app\services\BlockchainTrustService;
use app\services\InternationalizationService;
use app\services\AICustomerServiceService;
use think\facade\Request;
use think\facade\Validate;

/**
 * AI智能服务控制器
 * 第三阶段AI升级相关接口
 */
class AIServiceController extends BaseController
{
    protected $aiRiskService;
    protected $blockchainService;
    protected $i18nService;
    protected $aiCustomerService;

    public function __construct()
    {
        $this->aiRiskService = new AIRiskAssessmentService();
        $this->blockchainService = new BlockchainTrustService();
        $this->i18nService = new InternationalizationService();
        $this->aiCustomerService = new AICustomerServiceService();
    }

    /**
     * AI风险评估
     * POST /api/ai-service/risk-assessment
     */
    public function riskAssessment()
    {
        $data = Request::post();
        
        $validate = Validate::rule([
            'task_id' => 'require|integer',
            'worker_id' => 'integer',
            'employer_id' => 'integer'
        ]);

        if (!$validate->check($data)) {
            return $this->error($validate->getError());
        }

        try {
            $result = $this->aiRiskService->assessRisk(
                $data['task_id'],
                $data['worker_id'] ?? null,
                $data['employer_id'] ?? null
            );

            return $this->success($result, 'AI风险评估完成');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 智能保费计算
     * POST /api/ai-service/dynamic-premium
     */
    public function calculateDynamicPremium()
    {
        $data = Request::post();
        
        $validate = Validate::rule([
            'task_id' => 'require|integer',
            'base_premium' => 'require|float',
            'insurance_plan_id' => 'require|integer'
        ]);

        if (!$validate->check($data)) {
            return $this->error($validate->getError());
        }

        try {
            $result = $this->aiRiskService->calculateDynamicPremium(
                $data['task_id'],
                $data['base_premium'],
                $data['insurance_plan_id']
            );

            return $this->success($result, '智能保费计算完成');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 理赔概率预测
     * POST /api/ai-service/claim-prediction
     */
    public function predictClaimProbability()
    {
        $data = Request::post();
        
        $validate = Validate::rule([
            'task_id' => 'require|integer',
            'worker_id' => 'integer'
        ]);

        if (!$validate->check($data)) {
            return $this->error($validate->getError());
        }

        try {
            $result = $this->aiRiskService->predictClaimProbability(
                $data['task_id'],
                $data['worker_id'] ?? null
            );

            return $this->success($result, '理赔概率预测完成');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 智能保险推荐
     * POST /api/ai-service/insurance-recommendation
     */
    public function recommendInsurance()
    {
        $data = Request::post();
        
        $validate = Validate::rule([
            'task_id' => 'require|integer',
            'user_profile' => 'require|array'
        ]);

        if (!$validate->check($data)) {
            return $this->error($validate->getError());
        }

        try {
            $result = $this->aiRiskService->recommendInsurancePlan(
                $data['task_id'],
                $data['user_profile']
            );

            return $this->success($result, '智能保险推荐完成');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * AI客服对话
     * POST /api/ai-service/chat
     */
    public function aiChat()
    {
        $data = Request::post();
        
        $validate = Validate::rule([
            'user_id' => 'require|integer',
            'message' => 'require|string',
            'session_id' => 'string'
        ]);

        if (!$validate->check($data)) {
            return $this->error($validate->getError());
        }

        try {
            $result = $this->aiCustomerService->processMessage(
                $data['user_id'],
                $data['message'],
                $data['session_id'] ?? null
            );

            return $this->success($result, 'AI客服回复');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 智能问题分类
     * POST /api/ai-service/classify-problem
     */
    public function classifyProblem()
    {
        $data = Request::post();
        
        $validate = Validate::rule([
            'description' => 'require|string'
        ]);

        if (!$validate->check($data)) {
            return $this->error($validate->getError());
        }

        try {
            $result = $this->aiCustomerService->classifyProblem($data['description']);
            return $this->success($result, '问题分类完成');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取智能建议
     * GET /api/ai-service/smart-suggestions
     */
    public function getSmartSuggestions()
    {
        $userId = Request::get('user_id');
        $context = Request::get('context', []);
        
        if (!$userId) {
            return $this->error('用户ID不能为空');
        }

        try {
            $result = $this->aiCustomerService->getSmartSuggestions($userId, $context);
            return $this->success($result, '智能建议获取成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 人工客服转接
     * POST /api/ai-service/transfer-human
     */
    public function transferToHuman()
    {
        $data = Request::post();
        
        $validate = Validate::rule([
            'session_id' => 'require|string',
            'reason' => 'string'
        ]);

        if (!$validate->check($data)) {
            return $this->error($validate->getError());
        }

        try {
            $result = $this->aiCustomerService->transferToHuman(
                $data['session_id'],
                $data['reason'] ?? ''
            );

            return $this->success($result, '正在转接人工客服');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 服务质量评价
     * POST /api/ai-service/evaluate-service
     */
    public function evaluateService()
    {
        $data = Request::post();
        
        $validate = Validate::rule([
            'session_id' => 'require|string',
            'rating' => 'require|integer|between:1,5',
            'feedback' => 'string'
        ]);

        if (!$validate->check($data)) {
            return $this->error($validate->getError());
        }

        try {
            $result = $this->aiCustomerService->evaluateServiceQuality(
                $data['session_id'],
                $data['rating'],
                $data['feedback'] ?? ''
            );

            return $this->success($result, '评价提交成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
