<?php

namespace app\http\middleware\admin;


use app\Request;
use app\jobs\system\AdminLogJob;
use crmeb\interfaces\MiddlewareInterface;

/**
 * 日志中間件
 * Class AdminLogMiddleware
 * @package app\http\middleware\admin
 */
class AdminLogMiddleware implements MiddlewareInterface
{
    /**
     * @param Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle(Request $request, \Closure $next)
    {
        $module = app('http')->getName();
        $rule = trim(strtolower($request->rule()->getRule()));
        //记录后台日志
        AdminLogJob::dispatch([$request->adminId(), $request->adminInfo()['account'], $module, $rule, $request->ip(), 'system']);

        return $next($request);
    }

}
