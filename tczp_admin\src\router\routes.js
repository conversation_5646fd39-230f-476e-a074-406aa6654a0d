// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2021 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import index from "./modules/index";
import order from "./modules/order";
import user from "./modules/user";
import setting from "./modules/setting";
import settingPage from "./modules/settingPage";
import finance from "./modules/finance";
import app from "./modules/app";
import system from "./modules/system";
import statistic from "./modules/statistic";
import staff from "./modules/staff";
import task from "./modules/task";
import company from "./modules/company";
import BasicLayout from "@/layouts/basic-layout";
import frameOut from "./modules/frameOut";
import content from "./modules/content";

/**
 * 在主框架内显示
 */

const frameIn = [
  {
    path: "/admin/",
    meta: {
      title: "YWORK",
    },
    redirect: {
      name: Number(localStorage.getItem("isSupplier"))
        ? "statistic_order"
        : "home_index",
    },
    component: BasicLayout,
    children: [
      {
        path: "/admin/system/log",
        name: "log",
        meta: {
          title: "前端日志",
          auth: true,
        },
        component: () => import("@/pages/system/log"),
      },
      {
        path: "/admin/system/user",
        name: `systemUser`,
        meta: {
          auth: true,
          title: "个人中心",
        },
        component: () => import("@/pages/setting/user/index"),
      },
      // 刷新页面 必须保留
      {
        path: "refresh",
        name: "refresh",
        hidden: true,
        component: {
          beforeRouteEnter(to, from, next) {
            next((instance) => instance.$router.replace(from.fullPath));
          },
          render: (h) => h(),
        },
      },
      // 页面重定向 必须保留
      {
        path: "redirect/:route*",
        name: "redirect",
        hidden: true,
        component: {
          beforeRouteEnter(to, from, next) {
            next((instance) =>
              instance.$router.replace(JSON.parse(from.params.route))
            );
          },
          render: (h) => h(),
        },
      },
    ],
  },
  {
    path: `/admin/setting/system/create`,
    name: `system_create`,
    meta: {
      auth: ["setting-system_create"],
      title: "系统表单",
    },
    component: () => import("@/pages/setting/systemForm/create"),
  },
  {
    path: "/admin/pages/diy",
    name: `setting_diy`,
    meta: {
      auth: ["admin-setting-pages-diy"],
      title: "首页装修",
    },
    component: () => import("@/pages/setting/devise/index"),
  },
  {
    path: "/admin/pages/special/diy",
    name: `setting_special_diy`,
    meta: {
      auth: ["setting-diy-special-diy"],
      title: "专题页设计",
    },
    component: () => import("@/pages/setting/special/index"),
  },
  {
    path: "/admin/widget.images/index.html",
    name: `images`,
    meta: {
      auth: true,
      title: "上传图片",
    },
    component: () => import("@/components/uploadPictures/widgetImg"),
  },
  {
    path: "/admin/widget.widgets/icon.html",
    name: `imagesIcon`,
    meta: {
      auth: true,
      title: "上传图标",
    },
    component: () => import("@/components/iconFrom/index"),
  },
  {
    path: "/admin/system.User/list.html",
    name: `changeUser`,
    meta: {
      title: "选择用户",
    },
    component: () => import("@/components/customerInfo/index"),
  },
  {
    path: "/admin/widget.video/index.html",
    name: `video`,
    meta: {
      title: "上传视频",
    },
    component: () => import("@/components/uploadVideos/index"),
  },
  {
    path: `/app/upload`,
    name: `mobile_upload`,
    meta: {
      auth: true,
      title: "手机端扫码上传",
    },
    component: () => import("@/pages/app/upload"),
  },
  index,
  order,
  user,
  setting,
  settingPage,
  finance,
  app,
  system,
  statistic,
  staff,
  task,
  company,
  content,
];

/**
 * 在主框架之外显示
 */
// const pre = 'kefu_';

const frameOuts = frameOut;

/**
 * 错误页面
 */

const errorPage = [
  {
    path: "/admin/other",
    name: "other",
    meta: {
      title: "other",
    },
    component: () => import("@/pages/system/error/other"),
  },
  {
    path: "/admin/403",
    name: "403",
    meta: {
      title: "403",
    },
    component: () => import("@/pages/system/error/403"),
  },
  {
    path: "/admin/500",
    name: "500",
    meta: {
      title: "500",
    },
    component: () => import("@/pages/system/error/500"),
  },
  {
    path: "/admin/*",
    name: "404",
    meta: {
      title: "404",
    },
    component: () => import("@/pages/system/error/404"),
  },
];

// 导出需要显示菜单的
export const frameInRoutes = frameIn;

// 重新组织后导出
export default [...frameIn, ...frameOuts, ...errorPage];
