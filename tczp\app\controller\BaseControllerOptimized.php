<?php
declare(strict_types=1);

namespace app\controller;

use think\App;
use think\Container;
use think\exception\ValidateException;
use think\facade\Config;
use think\facade\Request;
use think\facade\Log;
use think\facade\Cache;
use think\Response;

/**
 * 统一基础控制器类 - TCZP服务端优化版
 * 提供三端统一的基础功能和规范
 * Class BaseControllerOptimized
 * @package app\controller
 */
abstract class BaseControllerOptimized
{
    /**
     * Request实例
     * @var \think\Request
     */
    protected $request;

    /**
     * 应用实例
     * @var \think\App
     */
    protected $app;

    /**
     * 是否批量验证
     * @var bool
     */
    protected $batchValidate = false;

    /**
     * 控制器中间件
     * @var array
     */
    protected $middleware = [];

    /**
     * 当前用户ID
     * @var int
     */
    protected $uid = 0;

    /**
     * 当前管理员ID
     * @var int
     */
    protected $adminId = 0;

    /**
     * 当前用户信息
     * @var array
     */
    protected $userInfo = [];

    /**
     * 当前管理员信息
     * @var array
     */
    protected $adminInfo = [];

    /**
     * 响应格式
     * @var string
     */
    protected $responseFormat = 'json';

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        $this->app = $app;
        $this->request = $this->app->request;

        // 控制器初始化
        $this->initialize();
    }

    /**
     * 初始化
     * @access protected
     */
    protected function initialize()
    {
        // 获取用户信息
        $this->uid = $this->request->uid ?? 0;
        $this->userInfo = $this->request->userInfo ?? [];
        
        // 获取管理员信息
        $this->adminId = $this->request->adminId ?? 0;
        $this->adminInfo = $this->request->adminInfo ?? [];
    }

    /**
     * 验证数据
     * @access protected
     * @param array $data 数据
     * @param string|array $validate 验证器名或者验证规则数组
     * @param array $message 提示信息
     * @param bool $batch 是否批量验证
     * @return array|string|true
     * @throws ValidateException
     */
    protected function validate(array $data, $validate, array $message = [], bool $batch = false)
    {
        if (is_array($validate)) {
            $v = new \think\Validate();
            $v->rule($validate);
        } else {
            if (strpos($validate, '.')) {
                // 支持场景
                [$validate, $scene] = explode('.', $validate);
            }
            $class = false !== strpos($validate, '\\') ? $validate : $this->app->parseClass('validate', $validate);
            $v = new $class();
            if (!empty($scene)) {
                $v->scene($scene);
            }
        }

        $v->message($message);

        // 是否批量验证
        if ($batch || $this->batchValidate) {
            $v->batch(true);
        }

        return $v->failException(true)->check($data);
    }

    /**
     * 成功响应
     * @param mixed $data 数据
     * @param string $msg 消息
     * @param int $code 状态码
     * @return Response
     */
    protected function success($data = [], string $msg = 'success', int $code = 200): Response
    {
        return $this->result($data, $code, $msg);
    }

    /**
     * 失败响应
     * @param string $msg 消息
     * @param mixed $data 数据
     * @param int $code 状态码
     * @return Response
     */
    protected function fail(string $msg = 'error', $data = [], int $code = 400): Response
    {
        return $this->result($data, $code, $msg);
    }

    /**
     * 返回封装后的API数据到客户端
     * @param mixed $data 要返回的数据
     * @param int $code 返回的code
     * @param string $msg 提示信息
     * @param string $type 返回类型
     * @return Response
     */
    protected function result($data, int $code = 200, string $msg = 'ok', string $type = 'json'): Response
    {
        $result = [
            'code' => $code,
            'status' => $code,
            'msg' => $msg,
            'message' => $msg, // 兼容不同前端
            'data' => $data,
            'time' => time(),
            'timestamp' => time()
        ];

        return Response::create($result, $type)->header([
            'Access-Control-Allow-Origin' => '*',
            'Access-Control-Allow-Headers' => 'Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match, If-Unmodified-Since, X-Requested-With',
            'Access-Control-Allow-Methods' => 'GET, POST, PATCH, PUT, DELETE, OPTIONS',
        ]);
    }

    /**
     * 分页响应
     * @param array $list 列表数据
     * @param int $total 总数
     * @param int $page 当前页
     * @param int $limit 每页数量
     * @param string $msg 消息
     * @return Response
     */
    protected function successPage(array $list, int $total, int $page = 1, int $limit = 20, string $msg = 'success'): Response
    {
        $data = [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit),
            'has_more' => $page * $limit < $total
        ];

        return $this->success($data, $msg);
    }

    /**
     * 获取请求参数
     * @param string $name 参数名
     * @param mixed $default 默认值
     * @param string $filter 过滤器
     * @return mixed
     */
    protected function getParam(string $name, $default = null, string $filter = '')
    {
        return $this->request->param($name, $default, $filter);
    }

    /**
     * 获取POST参数
     * @param string $name 参数名
     * @param mixed $default 默认值
     * @param string $filter 过滤器
     * @return mixed
     */
    protected function getPost(string $name = '', $default = null, string $filter = '')
    {
        return $this->request->post($name, $default, $filter);
    }

    /**
     * 获取GET参数
     * @param string $name 参数名
     * @param mixed $default 默认值
     * @param string $filter 过滤器
     * @return mixed
     */
    protected function getGet(string $name = '', $default = null, string $filter = '')
    {
        return $this->request->get($name, $default, $filter);
    }

    /**
     * 获取分页参数
     * @param int $defaultLimit 默认每页数量
     * @return array [page, limit]
     */
    protected function getPageParams(int $defaultLimit = 20): array
    {
        $page = max(1, (int)$this->getParam('page', 1));
        $limit = max(1, min(100, (int)$this->getParam('limit', $defaultLimit)));
        
        return [$page, $limit];
    }

    /**
     * 记录日志
     * @param string $message 消息
     * @param array $context 上下文
     * @param string $level 级别
     */
    protected function log(string $message, array $context = [], string $level = 'info'): void
    {
        Log::record($message, $level, $context);
    }

    /**
     * 缓存操作
     * @param string $key 缓存键
     * @param mixed $value 缓存值
     * @param int $expire 过期时间
     * @return mixed
     */
    protected function cache(string $key, $value = null, int $expire = 0)
    {
        if ($value === null) {
            return Cache::get($key);
        }
        
        return Cache::set($key, $value, $expire);
    }

    /**
     * 检查权限
     * @param string $permission 权限标识
     * @return bool
     */
    protected function checkPermission(string $permission): bool
    {
        // 子类可重写此方法实现具体权限检查
        return true;
    }

    /**
     * 获取客户端IP
     * @return string
     */
    protected function getClientIp(): string
    {
        return $this->request->ip();
    }

    /**
     * 获取用户代理
     * @return string
     */
    protected function getUserAgent(): string
    {
        return $this->request->header('User-Agent', '');
    }

    /**
     * 是否为AJAX请求
     * @return bool
     */
    protected function isAjax(): bool
    {
        return $this->request->isAjax();
    }

    /**
     * 是否为POST请求
     * @return bool
     */
    protected function isPost(): bool
    {
        return $this->request->isPost();
    }

    /**
     * 是否为GET请求
     * @return bool
     */
    protected function isGet(): bool
    {
        return $this->request->isGet();
    }

    /**
     * 获取当前控制器名
     * @return string
     */
    protected function getController(): string
    {
        return $this->request->controller();
    }

    /**
     * 获取当前操作名
     * @return string
     */
    protected function getAction(): string
    {
        return $this->request->action();
    }
}
