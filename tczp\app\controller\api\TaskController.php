<?php
// +----------------------------------------------------------------------
// | 同城用工系统
// +----------------------------------------------------------------------

namespace app\controller\api;

use app\controller\BaseAuth;
use think\facade\Db;

/**
 * 任务管理控制器
 */
class TaskController extends BaseAuth
{
    /**
     * 不需要登录的方法
     */
    protected $noAuthMethods = ['getTaskList', 'getTaskDetail'];

    /**
     * 发布任务
     */
    public function publishTask()
    {
        $data = [
            'task_title' => $this->getPost('task_title', ''),
            'task_description' => $this->getPost('task_description', ''),
            'task_category_id' => $this->getPost('task_category_id', 0),
            'required_workers' => $this->getPost('required_workers', 1),
            'task_location' => $this->getPost('task_location', ''),
            'hourly_rate' => $this->getPost('hourly_rate', 0),
            'urgency_level' => $this->getPost('urgency_level', 1),
        ];

        if (!$data['task_title']) {
            return $this->fail('请输入任务标题');
        }

        if (!$this->isEnterprise()) {
            return $this->fail('只有企业用户可以发布任务');
        }

        try {
            $data['user_id'] = $this->getUserId();
            $data['status'] = 'published';
            $data['create_time'] = time();

            $taskId = Db::name('tasks')->insertGetId($data);
            
            return $this->success('任务发布成功', ['task_id' => $taskId]);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * 获取任务列表
     */
    public function getTaskList()
    {
        $params = $this->getPageParams();
        $categoryId = $this->getGet('category_id', 0);
        $keyword = $this->getGet('keyword', '');

        try {
            $where = [['status', '=', 'published']];

            if ($categoryId > 0) {
                $where[] = ['task_category_id', '=', $categoryId];
            }

            if ($keyword) {
                $where[] = ['task_title|task_description', 'like', '%' . $keyword . '%'];
            }

            $list = Db::name('tasks')
                ->alias('t')
                ->leftJoin('users u', 't.user_id = u.id')
                ->leftJoin('task_category tc', 't.task_category_id = tc.id')
                ->field('t.*, u.nickname as publisher_name, tc.name as category_name')
                ->where($where)
                ->order('t.create_time desc')
                ->paginate([
                    'list_rows' => $params['limit'],
                    'page' => $params['page']
                ]);

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * 获取任务详情
     */
    public function getTaskDetail()
    {
        $id = $this->getParam('id', 0);
        
        if (!$id) {
            return $this->fail('任务ID不能为空');
        }

        try {
            $detail = Db::name('tasks')
                ->alias('t')
                ->leftJoin('users u', 't.user_id = u.id')
                ->leftJoin('task_category tc', 't.task_category_id = tc.id')
                ->field('t.*, u.nickname as publisher_name, u.phone as publisher_phone, tc.name as category_name')
                ->where('t.id', $id)
                ->find();
            
            if (!$detail) {
                return $this->fail('任务不存在');
            }

            return $this->success($detail);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * 申请任务
     */
    public function applyTask()
    {
        $taskId = $this->getPost('task_id', 0);
        $message = $this->getPost('message', '');

        if (!$taskId) {
            return $this->fail('请选择要申请的任务');
        }

        if (!$this->isWorker()) {
            return $this->fail('只有师傅用户可以申请任务');
        }

        try {
            $applicationId = Db::name('task_applications')->insertGetId([
                'task_id' => $taskId,
                'user_id' => $this->getUserId(),
                'message' => $message,
                'status' => 'pending',
                'create_time' => time()
            ]);

            return $this->success('申请成功，请等待企业审核');
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * 获取我的任务列表
     */
    public function getMyTasks()
    {
        $params = $this->getPageParams();
        $type = $this->getGet('type', 'published');

        try {
            $where = [];
            $uid = $this->getUserId();

            if ($type === 'published') {
                $where[] = ['user_id', '=', $uid];
            }

            $list = Db::name('tasks')
                ->where($where)
                ->order('create_time desc')
                ->paginate([
                    'list_rows' => $params['limit'],
                    'page' => $params['page']
                ]);

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }
} 