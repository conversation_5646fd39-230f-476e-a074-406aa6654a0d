<?php
declare(strict_types=1);

namespace app\controller\user;

use app\controller\BaseController;
use app\services\user\EnterpriseAuthServices;
use think\Response;

/**
 * 企业管理控制器 - 优化版
 * Class Enterprise
 * @package app\controller\user
 */
class Enterprise extends BaseController
{
    /**
     * 企业认证服务
     * @var EnterpriseAuthServices
     */
    protected $enterpriseAuthService;
    
    /**
     * 初始化
     */
    protected function initialize(): void
    {
        parent::initialize();
        $this->enterpriseAuthService = app()->make(EnterpriseAuthServices::class);
    }
    
    /**
     * 获取用户ID
     * @return int
     */
    private function getUserId(): int
    {
        return $this->request->uid ?? 0;
    }

    /**
     * 获取企业档案
     * @return Response
     */
    public function getProfile(): Response
    {
        try {
            $uid = $this->getUserId();
            $enterpriseInfo = $this->enterpriseAuthService->getEnterpriseInfo($uid);
            
            if (!$enterpriseInfo) {
                return $this->fail('企业信息不存在');
            }
            
            return $this->success($enterpriseInfo);
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 更新企业档案
     * @return Response
     */
    public function updateProfile(): Response
    {
        try {
            $data = $this->request->put();
            $uid = $this->getUserId();
            
            $enterpriseInfo = $this->enterpriseAuthService->getEnterpriseInfo($uid);
            if (!$enterpriseInfo) {
                return $this->fail('企业信息不存在');
            }
            
            // 更新企业信息
            $updateData = array_merge($data, [
                'update_time' => time()
            ]);
            
            $result = \think\facade\Db::name('enterprise_profiles')
                ->where('user_id', $uid)
                ->update($updateData);
                
            if ($result) {
                return $this->success([], '企业信息更新成功');
            } else {
                return $this->fail('更新失败');
            }
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取企业可用师傅列表
     * @return Response
     */
    public function getWorkers(): Response
    {
        try {
            $page = (int)$this->request->get('page', 1);
            $limit = (int)$this->request->get('limit', 20);
            
            $where = [
                'skill_id' => $this->request->get('skill_id', 0),
                'rating' => $this->request->get('rating', 0),
                'experience_years' => $this->request->get('experience_years', 0),
                'keyword' => $this->request->get('keyword', '')
            ];
            
            // 过滤空值
            $where = array_filter($where, function($value) {
                return $value !== '' && $value !== 0;
            });
            
            $query = \think\facade\Db::name('worker_profiles')
                ->alias('wp')
                ->join('user u', 'wp.user_id = u.uid')
                ->where('wp.auth_status', 'approved')
                ->where('wp.work_status', 'available');
                
            // 应用筛选条件
            if (!empty($where['skill_id'])) {
                $query->join('worker_skills_new ws', 'wp.id = ws.worker_id')
                    ->where('ws.skill_id', $where['skill_id'])
                    ->where('ws.auth_status', 'approved');
            }
            
            if (!empty($where['rating'])) {
                $query->where('wp.rating', '>=', $where['rating']);
            }
            
            if (!empty($where['experience_years'])) {
                $query->where('wp.work_experience', '>=', $where['experience_years']);
            }
            
            if (!empty($where['keyword'])) {
                $query->where(function ($q) use ($where) {
                    $q->where('wp.real_name', 'like', "%{$where['keyword']}%")
                      ->whereOr('u.nickname', 'like', "%{$where['keyword']}%");
                });
            }
            
            $total = $query->count();
            $list = $query->field([
                    'wp.id', 'wp.worker_no', 'wp.real_name', 'wp.work_experience',
                    'wp.rating', 'wp.order_count', 'wp.completion_rate',
                    'u.nickname', 'u.avatar', 'u.phone'
                ])
                ->order('wp.rating desc, wp.order_count desc')
                ->page($page, $limit)
                ->select()
                ->toArray();
                
            $result = [
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($total / $limit)
            ];
            
            return $this->success($result);
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 收藏师傅
     * @return Response
     */
    public function favoriteWorker(): Response
    {
        try {
            $workerId = (int)$this->request->param('id', 0);
            $data = $this->request->post();
            
            if (!$workerId) {
                return $this->fail('师傅ID不能为空');
            }
            
            $uid = $this->getUserId();
            
            // 检查是否已收藏
            $exists = \think\facade\Db::name('worker_favorites')
                ->where('user_id', $uid)
                ->where('worker_id', $workerId)
                ->count() > 0;
                
            if ($exists) {
                return $this->fail('已收藏该师傅');
            }
            
            // 添加收藏
            $favoriteData = [
                'user_id' => $uid,
                'worker_id' => $workerId,
                'tags' => $data['tags'] ?? '',
                'notes' => $data['notes'] ?? '',
                'create_time' => time()
            ];
            
            $result = \think\facade\Db::name('worker_favorites')->insert($favoriteData);
            
            if ($result) {
                return $this->success([], '收藏成功');
            } else {
                return $this->fail('收藏失败');
            }
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 取消收藏师傅
     * @return Response
     */
    public function unfavoriteWorker(): Response
    {
        try {
            $workerId = (int)$this->request->param('id', 0);
            
            if (!$workerId) {
                return $this->fail('师傅ID不能为空');
            }
            
            $uid = $this->getUserId();
            
            $result = \think\facade\Db::name('worker_favorites')
                ->where('user_id', $uid)
                ->where('worker_id', $workerId)
                ->delete();
                
            if ($result) {
                return $this->success([], '取消收藏成功');
            } else {
                return $this->fail('操作失败');
            }
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取收藏的师傅列表
     * @return Response
     */
    public function getFavoriteWorkers(): Response
    {
        try {
            $page = (int)$this->request->get('page', 1);
            $limit = (int)$this->request->get('limit', 20);
            
            $uid = $this->getUserId();
            
            $query = \think\facade\Db::name('worker_favorites')
                ->alias('wf')
                ->join('worker_profiles wp', 'wf.worker_id = wp.id')
                ->join('user u', 'wp.user_id = u.uid')
                ->where('wf.user_id', $uid);
                
            $total = $query->count();
            $list = $query->field([
                    'wf.id as favorite_id', 'wf.tags', 'wf.notes', 'wf.create_time as favorite_time',
                    'wp.id', 'wp.worker_no', 'wp.real_name', 'wp.work_experience',
                    'wp.rating', 'wp.order_count', 'wp.completion_rate',
                    'u.nickname', 'u.avatar', 'u.phone'
                ])
                ->order('wf.create_time desc')
                ->page($page, $limit)
                ->select()
                ->toArray();
                
            $result = [
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($total / $limit)
            ];
            
            return $this->success($result);
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 批量发布任务
     * @return Response
     */
    public function batchPublishTasks(): Response
    {
        try {
            $data = $this->request->post();
            
            if (empty($data['tasks']) || !is_array($data['tasks'])) {
                return $this->fail('请提供任务列表');
            }
            
            $uid = $this->getUserId();
            $enterpriseInfo = $this->enterpriseAuthService->getEnterpriseInfo($uid);
            
            if (!$enterpriseInfo) {
                return $this->fail('企业信息不存在');
            }
            
            $successCount = 0;
            $failedTasks = [];
            
            \think\facade\Db::startTrans();
            
            try {
                foreach ($data['tasks'] as $index => $taskData) {
                    try {
                        // 构建任务数据
                        $taskInfo = [
                            'user_id' => $uid,
                            'enterprise_id' => $enterpriseInfo['id'],
                            'task_title' => $taskData['task_title'],
                            'task_description' => $taskData['task_description'],
                            'task_category_id' => $taskData['task_category_id'],
                            'task_type' => 'batch',
                            'required_workers' => $taskData['required_workers'] ?? 1,
                            'task_location' => $taskData['task_location'] ?? '',
                            'hourly_rate' => $taskData['hourly_rate'],
                            'payment_type' => $taskData['payment_type'] ?? 'hourly',
                            'status' => 'published',
                            'create_time' => time(),
                            'update_time' => time()
                        ];
                        
                        $taskId = \think\facade\Db::name('tasks')->insertGetId($taskInfo);
                        
                        if ($taskId) {
                            $successCount++;
                        } else {
                            $failedTasks[] = $index + 1;
                        }
                        
                    } catch (\Exception $e) {
                        $failedTasks[] = $index + 1;
                    }
                }
                
                // 更新企业统计
                \think\facade\Db::name('enterprise_profiles')
                    ->where('id', $enterpriseInfo['id'])
                    ->inc('task_count', $successCount)
                    ->update(['update_time' => time()]);
                
                \think\facade\Db::commit();
                
                $message = "批量发布完成，成功{$successCount}个";
                if (!empty($failedTasks)) {
                    $message .= "，失败" . count($failedTasks) . "个";
                }
                
                return $this->success([
                    'success_count' => $successCount,
                    'failed_count' => count($failedTasks),
                    'failed_tasks' => $failedTasks
                ], $message);
                
            } catch (\Exception $e) {
                \think\facade\Db::rollback();
                throw $e;
            }
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取企业统计信息
     * @return Response
     */
    public function getStats(): Response
    {
        try {
            $uid = $this->getUserId();
            $stats = $this->enterpriseAuthService->getEnterpriseStats($uid);
            
            return $this->success($stats);
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取企业统计图表数据
     * @return Response
     */
    public function getStatsChart(): Response
    {
        try {
            $uid = $this->getUserId();
            $period = $this->request->get('period', 'month'); // week, month, quarter, year
            
            $enterpriseInfo = $this->enterpriseAuthService->getEnterpriseInfo($uid);
            if (!$enterpriseInfo) {
                return $this->fail('企业信息不存在');
            }
            
            // 获取时间范围
            $timeRange = $this->getTimeRange($period);
            
            // 任务发布趋势
            $taskTrend = \think\facade\Db::name('tasks')
                ->where('enterprise_id', $enterpriseInfo['id'])
                ->whereBetween('create_time', $timeRange)
                ->field('FROM_UNIXTIME(create_time, "%Y-%m-%d") as date, COUNT(*) as count')
                ->group('date')
                ->order('date asc')
                ->select()
                ->toArray();
                
            // 任务状态分布
            $statusDistribution = \think\facade\Db::name('tasks')
                ->where('enterprise_id', $enterpriseInfo['id'])
                ->whereBetween('create_time', $timeRange)
                ->field('status, COUNT(*) as count')
                ->group('status')
                ->select()
                ->toArray();
                
            // 支付金额趋势
            $paymentTrend = \think\facade\Db::name('payment_orders')
                ->where('user_id', $uid)
                ->where('status', 'paid')
                ->whereBetween('pay_time', $timeRange)
                ->field('FROM_UNIXTIME(pay_time, "%Y-%m-%d") as date, SUM(actual_amount) as amount')
                ->group('date')
                ->order('date asc')
                ->select()
                ->toArray();
            
            $chartData = [
                'task_trend' => $taskTrend,
                'status_distribution' => $statusDistribution,
                'payment_trend' => $paymentTrend
            ];
            
            return $this->success($chartData);
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取企业报表
     * @return Response
     */
    public function getReports(): Response
    {
        try {
            $uid = $this->getUserId();
            $reportType = $this->request->get('type', 'summary'); // summary, task, payment, worker
            $period = $this->request->get('period', 'month');
            
            $enterpriseInfo = $this->enterpriseAuthService->getEnterpriseInfo($uid);
            if (!$enterpriseInfo) {
                return $this->fail('企业信息不存在');
            }
            
            $timeRange = $this->getTimeRange($period);
            $reportData = [];
            
            switch ($reportType) {
                case 'summary':
                    $reportData = $this->getSummaryReport($enterpriseInfo['id'], $timeRange);
                    break;
                case 'task':
                    $reportData = $this->getTaskReport($enterpriseInfo['id'], $timeRange);
                    break;
                case 'payment':
                    $reportData = $this->getPaymentReport($uid, $timeRange);
                    break;
                case 'worker':
                    $reportData = $this->getWorkerReport($enterpriseInfo['id'], $timeRange);
                    break;
                default:
                    return $this->fail('无效的报表类型');
            }
            
            return $this->success($reportData);
            
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取时间范围
     * @param string $period
     * @return array
     */
    private function getTimeRange(string $period): array
    {
        $now = time();
        
        switch ($period) {
            case 'week':
                return [strtotime('-1 week', $now), $now];
            case 'month':
                return [strtotime('-1 month', $now), $now];
            case 'quarter':
                return [strtotime('-3 months', $now), $now];
            case 'year':
                return [strtotime('-1 year', $now), $now];
            default:
                return [strtotime('-1 month', $now), $now];
        }
    }

    /**
     * 获取汇总报表
     * @param int $enterpriseId
     * @param array $timeRange
     * @return array
     */
    private function getSummaryReport(int $enterpriseId, array $timeRange): array
    {
        // 实现汇总报表逻辑
        return [
            'total_tasks' => 0,
            'completed_tasks' => 0,
            'total_amount' => 0,
            'avg_rating' => 0
        ];
    }

    /**
     * 获取任务报表
     * @param int $enterpriseId
     * @param array $timeRange
     * @return array
     */
    private function getTaskReport(int $enterpriseId, array $timeRange): array
    {
        // 实现任务报表逻辑
        return [];
    }

    /**
     * 获取支付报表
     * @param int $uid
     * @param array $timeRange
     * @return array
     */
    private function getPaymentReport(int $uid, array $timeRange): array
    {
        // 实现支付报表逻辑
        return [];
    }

    /**
     * 获取师傅报表
     * @param int $enterpriseId
     * @param array $timeRange
     * @return array
     */
    private function getWorkerReport(int $enterpriseId, array $timeRange): array
    {
        // 实现师傅报表逻辑
        return [];
    }
}
