<?php
declare(strict_types=1);

// +----------------------------------------------------------------------
// | 同城用工系统 - 师傅端任务管理 (优化版)
// +----------------------------------------------------------------------

namespace app\controller\staff;

use app\controller\BaseController;
use app\services\staff\StaffTaskServices;
use app\services\staff\StaffAuthServices;
use think\Response;

/**
 * 师傅任务管理控制器 - 优化版
 * Class Task
 * @package app\controller\staff
 */
class Task extends BaseController
{
    /**
     * 师傅任务服务
     * @var StaffTaskServices
     */
    protected $staffTaskService;

    /**
     * 师傅认证服务
     * @var StaffAuthServices
     */
    protected $staffAuthService;

    /**
     * 初始化
     */
    protected function initialize(): void
    {
        parent::initialize();
        $this->staffTaskService = app()->make(StaffTaskServices::class);
        $this->staffAuthService = app()->make(StaffAuthServices::class);
    }

    /**
     * 获取用户ID
     * @return int
     */
    private function getUserId(): int
    {
        return $this->request->uid ?? 0;
    }

    /**
     * 获取任务列表
     * @return Response
     */
    public function getTaskList(): Response
    {
        try {
            $page = (int)$this->request->get('page', 1);
            $limit = (int)$this->request->get('limit', 20);

            $where = [
                'category_id' => $this->request->get('category_id', 0),
                'city' => $this->request->get('city', ''),
                'min_price' => $this->request->get('min_price', 0),
                'max_price' => $this->request->get('max_price', 0),
                'difficulty_level' => $this->request->get('difficulty_level', 0),
                'keyword' => $this->request->get('keyword', '')
            ];

            // 过滤空值
            $where = array_filter($where, function($value) {
                return $value !== '' && $value !== 0;
            });

            $result = $this->staffTaskService->getTaskList($where, $page, $limit);

            return $this->success($result);

        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取任务详情
     * @return Response
     */
    public function getTaskDetail(): Response
    {
        try {
            $taskId = (int)$this->request->param('id', 0);

            if (!$taskId) {
                return $this->fail('任务ID不能为空');
            }

            $detail = $this->staffTaskService->getTaskDetail($taskId);

            if (!$detail) {
                return $this->fail('任务不存在');
            }

            return $this->success($detail);

        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 申请任务
     * @return Response
     */
    public function applyTask(): Response
    {
        try {
            $taskId = (int)$this->request->param('id', 0);
            $data = $this->request->post();

            if (!$taskId) {
                return $this->fail('任务ID不能为空');
            }

            $uid = $this->getUserId();

            // 检查师傅是否可以申请该任务
            $canApply = $this->staffAuthService->canApplyTask($uid, $taskId);
            if (!$canApply['can_apply']) {
                return $this->fail($canApply['reason']);
            }

            // 申请任务
            $result = $this->staffTaskService->applyTask($taskId, $uid, $data);

            if ($result) {
                return $this->success([], '任务申请提交成功');
            } else {
                return $this->fail('申请提交失败');
            }

        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取我的任务申请
     * @return Response
     */
    public function getMyApplications(): Response
    {
        try {
            $page = (int)$this->request->get('page', 1);
            $limit = (int)$this->request->get('limit', 20);
            $status = $this->request->get('status', '');

            $where = [];
            if ($status) {
                $where['status'] = $status;
            }

            $uid = $this->getUserId();
            $result = $this->staffTaskService->getMyApplications($uid, $where, $page, $limit);

            return $this->success($result);

        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取我的任务
     * @return Response
     */
    public function getMyTasks(): Response
    {
        try {
            $page = (int)$this->request->get('page', 1);
            $limit = (int)$this->request->get('limit', 20);
            $taskStatus = $this->request->get('task_status', '');

            $where = [];
            if ($taskStatus) {
                $where['task_status'] = $taskStatus;
            }

            $uid = $this->getUserId();
            $result = $this->staffTaskService->getMyTasks($uid, $where, $page, $limit);

            return $this->success($result);

        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 开始任务
     * @return Response
     */
    public function startTask(): Response
    {
        try {
            $taskId = (int)$this->request->param('id', 0);

            if (!$taskId) {
                return $this->fail('任务ID不能为空');
            }

            $uid = $this->getUserId();

            // 检查任务状态和权限
            $task = \think\facade\Db::name('tasks')->where('id', $taskId)->find();
            if (!$task) {
                return $this->fail('任务不存在');
            }

            if ($task['status'] !== 'assigned') {
                return $this->fail('任务状态不允许开始');
            }

            // 检查是否是该任务的执行者
            $application = \think\facade\Db::name('task_applications')
                ->where('task_id', $taskId)
                ->where('user_id', $uid)
                ->where('status', 'approved')
                ->find();

            if (!$application) {
                return $this->fail('您不是该任务的执行者');
            }

            // 更新任务状态
            $result = \think\facade\Db::name('tasks')
                ->where('id', $taskId)
                ->update([
                    'status' => 'in_progress',
                    'start_time' => time(),
                    'update_time' => time()
                ]);

            if ($result) {
                return $this->success([], '任务已开始');
            } else {
                return $this->fail('操作失败');
            }

        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 完成任务
     * @return Response
     */
    public function completeTask(): Response
    {
        try {
            $taskId = (int)$this->request->param('id', 0);
            $data = $this->request->post();

            if (!$taskId) {
                return $this->fail('任务ID不能为空');
            }

            $uid = $this->getUserId();

            // 检查任务状态和权限
            $task = \think\facade\Db::name('tasks')->where('id', $taskId)->find();
            if (!$task) {
                return $this->fail('任务不存在');
            }

            if ($task['status'] !== 'in_progress') {
                return $this->fail('任务状态不允许完成');
            }

            // 检查是否是该任务的执行者
            $application = \think\facade\Db::name('task_applications')
                ->where('task_id', $taskId)
                ->where('user_id', $uid)
                ->where('status', 'approved')
                ->find();

            if (!$application) {
                return $this->fail('您不是该任务的执行者');
            }

            // 更新任务状态
            $updateData = [
                'status' => 'completed',
                'end_time' => time(),
                'update_time' => time()
            ];

            // 如果有完成说明，添加到任务中
            if (!empty($data['completion_note'])) {
                $updateData['completion_note'] = $data['completion_note'];
            }

            $result = \think\facade\Db::name('tasks')
                ->where('id', $taskId)
                ->update($updateData);

            if ($result) {
                return $this->success([], '任务已完成');
            } else {
                return $this->fail('操作失败');
            }

        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 取消任务申请
     * @return Response
     */
    public function cancelTask(): Response
    {
        try {
            $taskId = (int)$this->request->param('id', 0);
            $reason = $this->request->post('reason', '');

            if (!$taskId) {
                return $this->fail('任务ID不能为空');
            }

            $uid = $this->getUserId();

            // 检查申请是否存在
            $application = \think\facade\Db::name('task_applications')
                ->where('task_id', $taskId)
                ->where('user_id', $uid)
                ->where('status', 'pending')
                ->find();

            if (!$application) {
                return $this->fail('申请不存在或状态不允许取消');
            }

            // 更新申请状态
            $result = \think\facade\Db::name('task_applications')
                ->where('id', $application['id'])
                ->update([
                    'status' => 'cancelled',
                    'cancel_reason' => $reason,
                    'update_time' => time()
                ]);

            if ($result) {
                return $this->success([], '申请已取消');
            } else {
                return $this->fail('操作失败');
            }

        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 暂停任务
     * @return Response
     */
    public function pauseTask(): Response
    {
        try {
            $taskId = (int)$this->request->param('id', 0);
            $reason = $this->request->post('reason', '');

            if (!$taskId) {
                return $this->fail('任务ID不能为空');
            }

            $uid = $this->getUserId();

            // 检查任务状态和权限
            $task = \think\facade\Db::name('tasks')->where('id', $taskId)->find();
            if (!$task) {
                return $this->fail('任务不存在');
            }

            if ($task['status'] !== 'in_progress') {
                return $this->fail('任务状态不允许暂停');
            }

            // 检查是否是该任务的执行者
            $application = \think\facade\Db::name('task_applications')
                ->where('task_id', $taskId)
                ->where('user_id', $uid)
                ->where('status', 'approved')
                ->find();

            if (!$application) {
                return $this->fail('您不是该任务的执行者');
            }

            // 更新任务状态
            $result = \think\facade\Db::name('tasks')
                ->where('id', $taskId)
                ->update([
                    'status' => 'paused',
                    'pause_reason' => $reason,
                    'pause_time' => time(),
                    'update_time' => time()
                ]);

            if ($result) {
                return $this->success([], '任务已暂停');
            } else {
                return $this->fail('操作失败');
            }

        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 恢复任务
     * @return Response
     */
    public function resumeTask(): Response
    {
        try {
            $taskId = (int)$this->request->param('id', 0);

            if (!$taskId) {
                return $this->fail('任务ID不能为空');
            }

            $uid = $this->getUserId();

            // 检查任务状态和权限
            $task = \think\facade\Db::name('tasks')->where('id', $taskId)->find();
            if (!$task) {
                return $this->fail('任务不存在');
            }

            if ($task['status'] !== 'paused') {
                return $this->fail('任务状态不允许恢复');
            }

            // 检查是否是该任务的执行者
            $application = \think\facade\Db::name('task_applications')
                ->where('task_id', $taskId)
                ->where('user_id', $uid)
                ->where('status', 'approved')
                ->find();

            if (!$application) {
                return $this->fail('您不是该任务的执行者');
            }

            // 更新任务状态
            $result = \think\facade\Db::name('tasks')
                ->where('id', $taskId)
                ->update([
                    'status' => 'in_progress',
                    'resume_time' => time(),
                    'update_time' => time()
                ]);

            if ($result) {
                return $this->success([], '任务已恢复');
            } else {
                return $this->fail('操作失败');
            }

        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取推荐任务
     * @return Response
     */
    public function getRecommendedTasks(): Response
    {
        try {
            $page = (int)$this->request->get('page', 1);
            $limit = (int)$this->request->get('limit', 10);

            $uid = $this->getUserId();

            // 获取师傅信息和技能
            $workerInfo = $this->staffAuthService->getWorkerInfo($uid);
            if (!$workerInfo) {
                return $this->fail('请先完成师傅认证');
            }

            $workerSkills = $this->staffAuthService->getWorkerSkills($uid);
            $skillIds = array_column($workerSkills, 'skill_id');

            // 基于技能推荐任务
            $where = [];
            if (!empty($skillIds)) {
                // 这里简化处理，实际应该使用更复杂的推荐算法
                $where['required_skills'] = $skillIds;
            }

            $result = $this->staffTaskService->getTaskList($where, $page, $limit);

            return $this->success($result);

        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}