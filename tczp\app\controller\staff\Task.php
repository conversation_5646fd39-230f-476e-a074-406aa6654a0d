<?php
// +----------------------------------------------------------------------
// | 同城用工系统 - 师傅端任务管理
// +----------------------------------------------------------------------

namespace app\controller\staff;

use app\controller\BaseAuth;
use think\facade\Db;

/**
 * 任务管理控制器
 */
class Task extends BaseAuth
{
    /**
     * 不需要登录的方法
     */
    protected $noAuthMethods = ['getTaskList', 'getTaskDetail', 'getCategories'];

    /**
     * 获取任务列表
     */
    public function getTaskList()
    {
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 20);
        $categoryId = $this->request->get('category_id', 0);
        $keyword = $this->request->get('keyword', '');
        $urgency = $this->request->get('urgency', 0);
        $minRate = $this->request->get('min_rate', 0);
        $maxRate = $this->request->get('max_rate', 0);
        $location = $this->request->get('location', '');

        try {
            $where = [['t.status', '=', 'published']];

            if ($categoryId > 0) {
                $where[] = ['t.task_category_id', '=', $categoryId];
            }

            if ($keyword) {
                $where[] = ['t.task_title|t.task_description', 'like', '%' . $keyword . '%'];
            }

            if ($urgency > 0) {
                $where[] = ['t.urgency_level', '=', $urgency];
            }

            if ($minRate > 0) {
                $where[] = ['t.hourly_rate', '>=', $minRate];
            }

            if ($maxRate > 0) {
                $where[] = ['t.hourly_rate', '<=', $maxRate];
            }

            if ($location) {
                $where[] = ['t.task_location', 'like', '%' . $location . '%'];
            }

            $list = Db::name('tasks')
                ->alias('t')
                ->leftJoin('users u', 't.user_id = u.id')
                ->leftJoin('task_category tc', 't.task_category_id = tc.id')
                ->leftJoin('task_applications ta', 't.id = ta.task_id')
                ->field('t.*, u.nickname as publisher_name, u.avatar as publisher_avatar, 
                        tc.name as category_name, tc.icon as category_icon,
                        COUNT(ta.id) as application_count')
                ->where($where)
                ->group('t.id')
                ->order('t.urgency_level desc, t.create_time desc')
                ->page($page, $limit)
                ->select()
                ->toArray();

            $total = Db::name('tasks')
                ->alias('t')
                ->where($where)
                ->count();

            return $this->success([
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ]);
        } catch (\Exception $e) {
            return $this->fail('获取任务列表失败：' . $e->getMessage());
        }
    }

    /**
     * 获取任务详情
     */
    public function getTaskDetail()
    {
        $id = $this->request->param('id', 0);
        
        if (!$id) {
            return $this->fail('任务ID不能为空');
        }

        try {
            $detail = Db::name('tasks')
                ->alias('t')
                ->leftJoin('users u', 't.user_id = u.id')
                ->leftJoin('task_category tc', 't.task_category_id = tc.id')
                ->field('t.*, u.nickname as publisher_name, u.avatar as publisher_avatar, 
                        u.phone as publisher_phone, tc.name as category_name, tc.icon as category_icon')
                ->where('t.id', $id)
                ->find();
            
            if (!$detail) {
                return $this->fail('任务不存在');
            }

            // 获取申请统计
            $applicationStats = Db::name('task_applications')
                ->where('task_id', $id)
                ->field('status, COUNT(*) as count')
                ->group('status')
                ->select()
                ->toArray();

            $detail['application_stats'] = $applicationStats;

            // 获取最近申请的师傅
            $recentApplications = Db::name('task_applications')
                ->alias('ta')
                ->leftJoin('users u', 'ta.user_id = u.id')
                ->field('u.id, u.nickname, u.avatar, ta.create_time')
                ->where('ta.task_id', $id)
                ->order('ta.create_time desc')
                ->limit(5)
                ->select()
                ->toArray();

            $detail['recent_applications'] = $recentApplications;

            return $this->success($detail);
        } catch (\Exception $e) {
            return $this->fail('获取任务详情失败：' . $e->getMessage());
        }
    }

    /**
     * 申请任务
     */
    public function applyTask()
    {
        $taskId = $this->request->param('id', 0);
        $message = $this->request->post('message', '');

        if (!$taskId) {
            return $this->fail('请选择要申请的任务');
        }

        // 检查用户身份
        $userInfo = $this->getUserInfo();
        if ($userInfo['user_type'] !== 'worker') {
            return $this->fail('只有师傅用户可以申请任务');
        }

        try {
            // 检查任务是否存在且可申请
            $task = Db::name('tasks')->where('id', $taskId)->find();
            if (!$task) {
                return $this->fail('任务不存在');
            }

            if ($task['status'] !== 'published') {
                return $this->fail('任务不可申请');
            }

            // 检查是否已申请
            $existApplication = Db::name('task_applications')
                ->where([
                    'task_id' => $taskId,
                    'user_id' => $this->getUserId()
                ])
                ->find();

            if ($existApplication) {
                return $this->fail('您已申请过此任务');
            }

            $applicationId = Db::name('task_applications')->insertGetId([
                'task_id' => $taskId,
                'user_id' => $this->getUserId(),
                'message' => $message,
                'status' => 'pending',
                'create_time' => time()
            ]);

            return $this->success(['application_id' => $applicationId], '申请成功，请等待企业审核');
        } catch (\Exception $e) {
            return $this->fail('申请失败：' . $e->getMessage());
        }
    }

    /**
     * 获取我的任务列表
     */
    public function getMyTasks()
    {
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 20);
        $status = $this->request->get('status', '');

        try {
            $where = [['ta.user_id', '=', $this->getUserId()]];

            if ($status) {
                $where[] = ['ta.status', '=', $status];
            }

            $list = Db::name('task_applications')
                ->alias('ta')
                ->leftJoin('tasks t', 'ta.task_id = t.id')
                ->leftJoin('users u', 't.user_id = u.id')
                ->leftJoin('task_category tc', 't.task_category_id = tc.id')
                ->field('ta.*, t.task_title, t.task_description, t.hourly_rate, t.task_location,
                        t.urgency_level, u.nickname as publisher_name, tc.name as category_name')
                ->where($where)
                ->order('ta.create_time desc')
                ->page($page, $limit)
                ->select()
                ->toArray();

            $total = Db::name('task_applications')
                ->alias('ta')
                ->where($where)
                ->count();

            return $this->success([
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ]);
        } catch (\Exception $e) {
            return $this->fail('获取我的任务失败：' . $e->getMessage());
        }
    }

    /**
     * 开始任务
     */
    public function startTask()
    {
        $taskId = $this->request->param('id', 0);

        if (!$taskId) {
            return $this->fail('任务ID不能为空');
        }

        try {
            // 检查申请状态
            $application = Db::name('task_applications')
                ->where([
                    'task_id' => $taskId,
                    'user_id' => $this->getUserId(),
                    'status' => 'approved'
                ])
                ->find();

            if (!$application) {
                return $this->fail('任务申请未通过或不存在');
            }

            // 更新申请状态为进行中
            Db::name('task_applications')
                ->where('id', $application['id'])
                ->update([
                    'status' => 'in_progress',
                    'start_time' => time()
                ]);

            // 更新任务状态
            Db::name('tasks')
                ->where('id', $taskId)
                ->update(['status' => 'in_progress']);

            return $this->success([], '任务已开始');
        } catch (\Exception $e) {
            return $this->fail('开始任务失败：' . $e->getMessage());
        }
    }

    /**
     * 完成任务
     */
    public function completeTask()
    {
        $taskId = $this->request->param('id', 0);
        $workHours = $this->request->post('work_hours', 0);
        $completionNote = $this->request->post('completion_note', '');

        if (!$taskId) {
            return $this->fail('任务ID不能为空');
        }

        if ($workHours <= 0) {
            return $this->fail('请输入工作时长');
        }

        try {
            // 检查申请状态
            $application = Db::name('task_applications')
                ->where([
                    'task_id' => $taskId,
                    'user_id' => $this->getUserId(),
                    'status' => 'in_progress'
                ])
                ->find();

            if (!$application) {
                return $this->fail('任务未在进行中');
            }

            // 获取任务信息
            $task = Db::name('tasks')->where('id', $taskId)->find();
            $totalAmount = $task['hourly_rate'] * $workHours;

            // 更新申请状态为已完成
            Db::name('task_applications')
                ->where('id', $application['id'])
                ->update([
                    'status' => 'completed',
                    'end_time' => time(),
                    'work_hours' => $workHours,
                    'completion_note' => $completionNote,
                    'total_amount' => $totalAmount
                ]);

            // 更新任务状态
            Db::name('tasks')
                ->where('id', $taskId)
                ->update(['status' => 'completed']);

            return $this->success(['total_amount' => $totalAmount], '任务已完成');
        } catch (\Exception $e) {
            return $this->fail('完成任务失败：' . $e->getMessage());
        }
    }

    /**
     * 取消任务
     */
    public function cancelTask()
    {
        $taskId = $this->request->param('id', 0);
        $cancelReason = $this->request->post('cancel_reason', '');

        if (!$taskId) {
            return $this->fail('任务ID不能为空');
        }

        try {
            // 检查申请状态
            $application = Db::name('task_applications')
                ->where([
                    'task_id' => $taskId,
                    'user_id' => $this->getUserId()
                ])
                ->whereIn('status', ['pending', 'approved', 'in_progress'])
                ->find();

            if (!$application) {
                return $this->fail('任务不可取消');
            }

            // 更新申请状态为已取消
            Db::name('task_applications')
                ->where('id', $application['id'])
                ->update([
                    'status' => 'cancelled',
                    'cancel_reason' => $cancelReason,
                    'cancel_time' => time()
                ]);

            return $this->success([], '任务已取消');
        } catch (\Exception $e) {
            return $this->fail('取消任务失败：' . $e->getMessage());
        }
    }

    /**
     * 获取申请记录
     */
    public function getApplications()
    {
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 20);

        try {
            $list = Db::name('task_applications')
                ->alias('ta')
                ->leftJoin('tasks t', 'ta.task_id = t.id')
                ->leftJoin('users u', 't.user_id = u.id')
                ->field('ta.*, t.task_title, t.hourly_rate, u.nickname as publisher_name')
                ->where('ta.user_id', $this->getUserId())
                ->order('ta.create_time desc')
                ->page($page, $limit)
                ->select()
                ->toArray();

            $total = Db::name('task_applications')
                ->where('user_id', $this->getUserId())
                ->count();

            return $this->success([
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ]);
        } catch (\Exception $e) {
            return $this->fail('获取申请记录失败：' . $e->getMessage());
        }
    }

    /**
     * 获取任务分类
     */
    public function getCategories()
    {
        try {
            $categories = Db::name('task_category')
                ->field('id, name, icon, parent_id, sort, description')
                ->where('status', 1)
                ->order('sort asc, id asc')
                ->select()
                ->toArray();

            return $this->success($categories);
        } catch (\Exception $e) {
            return $this->fail('获取任务分类失败：' . $e->getMessage());
        }
    }
} 