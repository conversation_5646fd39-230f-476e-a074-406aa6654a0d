<?php

namespace app\controller\api\user;


use app\services\user\UserSearchServices;
use think\annotation\Inject;
use think\Request;

/**
 * Class UserSearch
 * @package app\api\controller\v2\user
 */
class UserSearch
{
    /**
     * @var UserSearchServices
     */
    #[Inject]
    protected UserSearchServices $services;

    /**
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @email <EMAIL>
     * @date 2023/9/19
     */
    public function getUserSearchList(Request $request)
    {
        return app('json')->successful($this->services->getUserList((int)$request->uid()));
    }

    public function cleanUserSearch(Request $request)
    {
        $uid = (int)$request->uid();
        $this->services->update(['uid' => $uid], ['is_del' => 1]);
        return app('json')->successful('删除成功');
    }
}
