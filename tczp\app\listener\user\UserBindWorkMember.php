<?php

namespace app\listener\user;


use app\services\work\WorkMemberServices;
use crmeb\interfaces\ListenerInterface;

/**
 * 用户绑定成员
 * Class UserBindWorkMember
 * @package app\listener\user
 */
class UserBindWorkMember implements ListenerInterface
{

    public function handle($event): void
    {
        [$uid, $phone] = $event;
        /** @var WorkMemberServices $service */
        $service = app()->make(WorkMemberServices::class);

        try {
            $memberInfo = $service->get(['mobile' => $phone], ['id', 'uid']);
            if ($memberInfo) {
                $memberInfo->uid = $uid;
                $memberInfo->save();
            }
        } catch (\Throwable $e) {
            \think\facade\Log::error(json_encode([
                'error' => '用户绑定成员失败:' . $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]));
        }
    }

}
