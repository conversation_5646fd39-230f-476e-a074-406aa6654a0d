<?php

namespace app\controller\api;

use app\Request;
use think\Response;

/**
 * 用户认证控制器
 * Class AuthController
 * @package app\controller\api\v1
 */
class AuthController
{
    /**
     * 提交企业认证
     * @param Request $request
     * @return Response
     */
    public function submitEnterpriseAuth(Request $request)
    {
        $data = $request->post();
        
        // 验证必填字段
        $requiredFields = ['enterprise_name', 'enterprise_contact', 'phone', 'enterprise_address', 'enterprise_license'];
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                return app('json')->fail('请填写完整的企业信息');
            }
        }
        
        // 验证手机号格式
        if (!preg_match('/^1[3-9]\d{9}$/', $data['phone'])) {
            return app('json')->fail('请输入正确的手机号码');
        }
        
        try {
            $userId = $request->uid();
            
            // 检查是否已经提交过认证
            $existAuth = app()->make(\app\dao\user\UserDao::class)->get($userId);
            if ($existAuth && $existAuth['verification_status'] == 1) {
                return app('json')->fail('认证申请正在审核中，请勿重复提交');
            }
            
            // 更新用户认证信息
            $updateData = [
                'user_type' => 'enterprise',
                'enterprise_name' => $data['enterprise_name'],
                'enterprise_contact' => $data['enterprise_contact'],
                'phone' => $data['phone'],
                'enterprise_address' => $data['enterprise_address'],
                'enterprise_license' => $data['enterprise_license'],
                'verification_status' => 1, // 待审核
                'verification_time' => time(),
                'reject_reason' => ''
            ];
            
            app()->make(\app\dao\user\UserDao::class)->update($userId, $updateData);
            
            return app('json')->success('企业认证申请提交成功，请等待审核');
            
        } catch (\Exception $e) {
            return app('json')->fail('提交失败：' . $e->getMessage());
        }
    }
    
    /**
     * 提交师傅认证
     * @param Request $request
     * @return Response
     */
    public function submitWorkerAuth(Request $request)
    {
        $data = $request->post();
        
        // 验证必填字段
        $requiredFields = ['real_name', 'id_card', 'phone', 'service_area', 'work_experience', 'id_card_front', 'id_card_back'];
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                return app('json')->fail('请填写完整的个人信息');
            }
        }
        
        // 验证身份证号格式
        if (!preg_match('/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/', $data['id_card'])) {
            return app('json')->fail('请输入正确的身份证号码');
        }
        
        // 验证手机号格式
        if (!preg_match('/^1[3-9]\d{9}$/', $data['phone'])) {
            return app('json')->fail('请输入正确的手机号码');
        }
        
        // 验证技能选择
        if (empty($data['skills']) || !is_array($data['skills'])) {
            return app('json')->fail('请选择至少一项专业技能');
        }
        
        try {
            $userId = $request->uid();
            
            // 检查是否已经提交过认证
            $existAuth = app()->make(\app\dao\user\UserDao::class)->get($userId);
            if ($existAuth && $existAuth['verification_status'] == 1) {
                return app('json')->fail('认证申请正在审核中，请勿重复提交');
            }
            
            // 更新用户认证信息
            $updateData = [
                'user_type' => 'worker',
                'real_name' => $data['real_name'],
                'id_card' => $data['id_card'],
                'phone' => $data['phone'],
                'worker_service_area' => $data['service_area'],
                'worker_experience' => $data['work_experience'],
                'id_card_front' => $data['id_card_front'],
                'id_card_back' => $data['id_card_back'],
                'worker_skills' => json_encode($data['skills']),
                'worker_certificates' => isset($data['certificates']) ? json_encode($data['certificates']) : '',
                'verification_status' => 1, // 待审核
                'verification_time' => time(),
                'reject_reason' => ''
            ];
            
            app()->make(\app\dao\user\UserDao::class)->update($userId, $updateData);
            
            return app('json')->success('师傅认证申请提交成功，请等待审核');
            
        } catch (\Exception $e) {
            return app('json')->fail('提交失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取认证状态
     * @param Request $request
     * @return Response
     */
    public function getAuthStatus(Request $request)
    {
        try {
            $userId = $request->uid();
            
            $user = app()->make(\app\dao\user\UserDao::class)->get($userId);
            if (!$user) {
                return app('json')->fail('用户不存在');
            }
            
            $authInfo = [
                'user_type' => $user['user_type'] ?? '',
                'verification_status' => $user['verification_status'] ?? 0,
                'verification_time' => $user['verification_time'] ?? 0,
                'reject_reason' => $user['reject_reason'] ?? ''
            ];
            
            // 根据用户类型返回不同的认证信息
            if ($user['user_type'] === 'enterprise') {
                $authInfo['enterprise_name'] = $user['enterprise_name'] ?? '';
                $authInfo['enterprise_contact'] = $user['enterprise_contact'] ?? '';
                $authInfo['enterprise_address'] = $user['enterprise_address'] ?? '';
                $authInfo['enterprise_license'] = $user['enterprise_license'] ?? '';
            } elseif ($user['user_type'] === 'worker') {
                $authInfo['real_name'] = $user['real_name'] ?? '';
                $authInfo['id_card'] = $user['id_card'] ?? '';
                $authInfo['service_area'] = $user['worker_service_area'] ?? '';
                $authInfo['work_experience'] = $user['worker_experience'] ?? '';
                $authInfo['id_card_front'] = $user['id_card_front'] ?? '';
                $authInfo['id_card_back'] = $user['id_card_back'] ?? '';
                $authInfo['skills'] = $user['worker_skills'] ? json_decode($user['worker_skills'], true) : [];
                $authInfo['certificates'] = $user['worker_certificates'] ? json_decode($user['worker_certificates'], true) : [];
            }
            
            $authInfo['phone'] = $user['phone'] ?? '';
            
            return app('json')->success($authInfo);
            
        } catch (\Exception $e) {
            return app('json')->fail('获取认证信息失败：' . $e->getMessage());
        }
    }
    
    /**
     * 上传认证文件
     * @param Request $request
     * @return Response
     */
    public function uploadAuthFile(Request $request)
    {
        try {
            $file = $request->file('file');
            if (!$file) {
                return app('json')->fail('请选择要上传的文件');
            }
            
            // 验证文件类型
            $allowedTypes = ['jpg', 'jpeg', 'png'];
            $extension = strtolower($file->getOriginalExtension());
            if (!in_array($extension, $allowedTypes)) {
                return app('json')->fail('只支持上传jpg、png格式的图片');
            }
            
            // 验证文件大小（5MB）
            if ($file->getSize() > 5 * 1024 * 1024) {
                return app('json')->fail('文件大小不能超过5MB');
            }
            
            // 生成文件名
            $fileName = 'auth_' . date('YmdHis') . '_' . uniqid() . '.' . $extension;
            $savePath = 'uploads/auth/' . date('Y/m/d');
            
            // 移动文件
            $file->move(public_path($savePath), $fileName);
            
            $fileUrl = '/' . $savePath . '/' . $fileName;
            
            return app('json')->success([
                'url' => $fileUrl,
                'filename' => $fileName
            ]);
            
        } catch (\Exception $e) {
            return app('json')->fail('文件上传失败：' . $e->getMessage());
        }
    }
    
    /**
     * 检查认证状态
     * @param Request $request
     * @return Response
     */
    public function checkAuthRequired(Request $request)
    {
        try {
            $userId = $request->uid();
            $userType = $request->param('user_type', ''); // enterprise 或 worker
            
            $user = app()->make(\app\dao\user\UserDao::class)->get($userId);
            if (!$user) {
                return app('json')->fail('用户不存在');
            }
            
            $needAuth = false;
            $authStatus = $user['verification_status'] ?? 0;
            $currentUserType = $user['user_type'] ?? '';
            
            // 检查是否需要认证
            if (empty($currentUserType) || $currentUserType !== $userType) {
                $needAuth = true;
            } elseif ($authStatus == 0 || $authStatus == 3) { // 未认证或认证失败
                $needAuth = true;
            }
            
            return app('json')->success([
                'need_auth' => $needAuth,
                'auth_status' => $authStatus,
                'user_type' => $currentUserType,
                'reject_reason' => $user['reject_reason'] ?? ''
            ]);
            
        } catch (\Exception $e) {
            return app('json')->fail('检查认证状态失败：' . $e->getMessage());
        }
    }
} 