<?php

namespace app\services\message\service;


use app\dao\message\service\StoreServiceSpeechcraftDao;
use app\services\BaseServices;
use crmeb\services\FormBuilder;
use think\annotation\Inject;
use think\exception\ValidateException;
use think\Model;

/**
 * 话术
 * Class StoreServiceSpeechcraftServices
 * @package app\services\message\service
 * @mixin StoreServiceSpeechcraftDao
 */
class StoreServiceSpeechcraftServices extends BaseServices
{
    /**
     * @var StoreServiceSpeechcraftDao
     */
    #[Inject]
    protected StoreServiceSpeechcraftDao $dao;

    /**
     * @param array $where
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getSpeechcraftList(array $where)
    {
        [$page, $limit] = $this->getPageValue();
        $list = $this->dao->getSpeechcraftList($where, $page, $limit);
        $count = $this->dao->count($where);
        return compact('list', 'count');
    }

    /**
     * 创建form表单
     * @param int $cate_id
     * @return mixed
     */
    public function createForm(int $cate_id = 0)
    {
        return create_form('添加话术', $this->speechcraftForm([], $cate_id), $this->url('/app/wechat/speechcraft'), 'POST');
    }

    /**
     * @param int $id
     * @return array
     * @throws \FormBuilder\Exception\FormBuilderException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function updateForm(int $id)
    {
        $info = $this->dao->get($id);
        if (!$info) {
            throw new ValidateException('您修改的话术内容不存在');
        }
        return create_form('编辑话术', $this->speechcraftForm($info->toArray()), $this->url('/app/wechat/speechcraft/' . $id), 'PUT');
    }

    /**
     * @param array $infoData
     * @param int $cate_id
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    protected function speechcraftForm(array $infoData = [], int $cate_id = 0)
    {
        /** @var StoreServiceSpeechcraftCateServices $services */
        $services = app()->make(StoreServiceSpeechcraftCateServices::class);
        $cateList = $services->getCateList(['owner_id' => 0, 'type' => 0, 'group' => 1]);
        $data = [];
        foreach ($cateList['data'] as $item) {
            $data[] = ['value' => $item['id'], 'label' => $item['name']];
        }
        $form[] = FormBuilder::select('cate_id', '话术分类', $infoData['cate_id'] ?? $cate_id)->setOptions($data);
        $form[] = FormBuilder::textarea('title', '话术标题', $infoData['title'] ?? '');
        $form[] = FormBuilder::textarea('message', '话术内容', $infoData['message'] ?? '')->required();
        $form[] = FormBuilder::number('sort', '排序', (int)($infoData['sort'] ?? 0))->min(0);
        return $form;
    }
}
