<?php
namespace app\listener\system;

use app\services\message\sms\SmsRecordServices;
use crmeb\utils\Cron;
use crmeb\interfaces\ListenerInterface;
use think\facade\Log;

/**
 * 定时更新短信状态
 * Class AutoSmsCode
 * @package app\listener\system
 */
class AutoSmsCode extends Cron implements ListenerInterface
{
    /**
     * @param $event
     */
    public function handle($event): void
    {
        //更新短信状态
        $this->tick(1000 * 30, function () {
            try {
                //修改短信发送记录短信状态
                /** @var SmsRecordServices $smsRecord */
                $smsRecord = app()->make(SmsRecordServices::class);
                return $smsRecord->modifyResultCode();
            } catch (\Throwable $e) {
                Log::error('自动更新短信状态:[' . class_basename($this) . ']' . $e->getMessage());
            }
        });

    }
}
