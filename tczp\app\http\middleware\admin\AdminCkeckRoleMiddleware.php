<?php

namespace app\http\middleware\admin;

use app\Request;
use app\services\system\SystemRoleServices;
use crmeb\exceptions\AuthException;
use crmeb\interfaces\MiddlewareInterface;
use crmeb\utils\ApiErrorCode;

/**
 * 权限规则验证
 * Class AdminCkeckRoleMiddleware
 * @package app\http\middleware
 */
class AdminCkeckRoleMiddleware implements MiddlewareInterface
{

    public function handle(Request $request, \Closure $next)
    {
        if (!$request->adminId() || !$request->adminInfo())
            throw new AuthException(ApiErrorCode::ERR_ADMINID_VOID);
//        $data = [
//            'adminapi/channel/merchant/verify'
//        ];
//        $rule = trim(strtolower($request->rule()->getRule()));
//        if ($request->adminId() != 1 && in_array($request->method(), ['POST', 'PUT', 'DELETE']) && !in_array($rule, $data)){
//            return app('json')->fail('暂无权限');
//        }
        if ($request->adminInfo()['level']) {
            /** @var SystemRoleServices $systemRoleService */
            $systemRoleService = app()->make(SystemRoleServices::class);
            $systemRoleService->verifiAuth($request);
        }

        return $next($request);
    }
}
